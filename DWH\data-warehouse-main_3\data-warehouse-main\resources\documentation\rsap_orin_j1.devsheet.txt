Data vault pipeline developer cheat sheet 
rendered from  rsap_orin_j1.dvpi

pipeline name:  rsap_orin_j1

------------------------------------------------------
record source:  sap.orin

Source fields:
       COMPANY    Varchar(50)
       DOCENTRY   INTEGER
       CARDCODE   VARCHAR(20)
       SLPCODE    VARCHAR(20)
       JSON_TEXT  VARCHAR


------------------------------------------------------
Table List:
stage_table.rvlt_sap.rsap_orin_j1_stage
table.rvlt_sap.rsap_credit_memo_orin_hub
table.rvlt_sap.rsap_credit_memo_orin_j1_l10_sat
table.rvlt_sap.rsap_business_partner_ocrd_hub
table.rvlt_sap.rsap_sales_employee_oslp_hub
table.rvlt_sap.rsap_credit_memo_orin_business_partner_ocrd_sales_employee_oslp_lnk
table.rvlt_sap.rsap_credit_memo_orin_business_partner_ocrd_sales_employee_oslp_esat

------------------------------------------------------
stage table:  stage_rvlt.rsap_orin_j1_stage
Field to Stage mapping:
	--business keys,
		CARDCODE   >  CARDCODE,
		COMPANY    >  COMPANY,
		DOCENTRY   >  DOCENTRY,
		SLPCODE    >  SLPCODE,

	--content,
		JSON_TEXT  >  JSON_TEXT

------------------------------------------------------
Hash value composition

HK_RSAP_CREDIT_MEMO_ORIN (key)
		COMPANY 
		DOCENTRY 

HK_RSAP_BUSINESS_PARTNER_OCRD (key)
		CARDCODE 
		COMPANY 

HK_RSAP_SALES_EMPLOYEE_OSLP (key)
		COMPANY 
		SLPCODE 

LK_RSAP_CREDIT_MEMO_ORIN_BUSINESS_PARTNER_OCRD_SALES_EMPLOYEE_OSLP (key)
		COMPANY 
		DOCENTRY 
		CARDCODE 
		COMPANY 
		COMPANY 
		SLPCODE 

RH_RSAP_CREDIT_MEMO_ORIN_J1_L10_SAT (diff_hash)
		JSON_TEXT 


------------------------------------------------------
Table load method
(STAGE >  VAULT)

rsap_credit_memo_orin_hub (/) can be loaded by convention
		  key: HK_RSAP_CREDIT_MEMO_ORIN  >  HK_RSAP_CREDIT_MEMO_ORIN
		  business_key: COMPANY  >  COMPANY 
		  business_key: DOCENTRY  >  DOCENTRY 

rsap_credit_memo_orin_j1_l10_sat (/) can be loaded by convention
		  parent_key: HK_RSAP_CREDIT_MEMO_ORIN  >  HK_RSAP_CREDIT_MEMO_ORIN
		  diff_hash: RH_RSAP_CREDIT_MEMO_ORIN_J1_L10_SAT  >  RH_RSAP_CREDIT_MEMO_ORIN_J1_L10_SAT
		  content: JSON_TEXT  >  JSON_TEXT 

rsap_business_partner_ocrd_hub (/) can be loaded by convention
		  key: HK_RSAP_BUSINESS_PARTNER_OCRD  >  HK_RSAP_BUSINESS_PARTNER_OCRD
		  business_key: COMPANY  >  COMPANY 
		  business_key: CARDCODE  >  CARDCODE 

rsap_sales_employee_oslp_hub (/) can be loaded by convention
		  key: HK_RSAP_SALES_EMPLOYEE_OSLP  >  HK_RSAP_SALES_EMPLOYEE_OSLP
		  business_key: COMPANY  >  COMPANY 
		  business_key: SLPCODE  >  SLPCODE 

rsap_credit_memo_orin_business_partner_ocrd_sales_employee_oslp_lnk (/) can be loaded by convention
		  parent_key_1: HK_RSAP_CREDIT_MEMO_ORIN  >  HK_RSAP_CREDIT_MEMO_ORIN
		  parent_key_2: HK_RSAP_BUSINESS_PARTNER_OCRD  >  HK_RSAP_BUSINESS_PARTNER_OCRD
		  parent_key_3: HK_RSAP_SALES_EMPLOYEE_OSLP  >  HK_RSAP_SALES_EMPLOYEE_OSLP
		  key: LK_RSAP_CREDIT_MEMO_ORIN_BUSINESS_PARTNER_OCRD_SALES_EMPLOYEE_OSLP  >  LK_RSAP_CREDIT_MEMO_ORIN_BUSINESS_PARTNER_OCRD_SALES_EMPLOYEE_OSLP

rsap_credit_memo_orin_business_partner_ocrd_sales_employee_oslp_esat (/) can be loaded by convention
		! Driving keys: HK_RSAP_CREDIT_MEMO_ORIN 
		  parent_key: LK_RSAP_CREDIT_MEMO_ORIN_BUSINESS_PARTNER_OCRD_SALES_EMPLOYEE_OSLP  >  LK_RSAP_CREDIT_MEMO_ORIN_BUSINESS_PARTNER_OCRD_SALES_EMPLOYEE_OSLP

