{"dvpd_version": "0.6.2", "stage_properties": [{"stage_schema": "stage_rvlt", "stage_table_name": "rsap_ocrg_j1_stage"}], "pipeline_name": "rsap_ocrg_j1", "record_source_name_expression": "sap.ocrg", "data_extraction": {"fetch_module_name": "none - ddl generation only"}, "fields": [{"field_name": "company", "field_type": "VARCHAR(50)", "targets": [{"table_name": "RSAP_CARD_GROUP_OCRG_HUB"}]}, {"field_name": "groupcode", "field_type": "INTEGER", "targets": [{"table_name": "RSAP_CARD_GROUP_OCRG_HUB"}]}, {"field_name": "json_text", "field_type": "VARCHAR", "targets": [{"table_name": "RSAP_CARD_GROUP_OCRG_J1_L10_SAT"}]}], "data_vault_model": [{"schema_name": "rvlt_sap", "tables": [{"table_name": "rsap_card_group_ocrg_hub", "table_stereotype": "hub", "hub_key_column_name": "HK_RSAP_CARD_GROUP_OCRG"}, {"table_name": "rsap_card_group_ocrg_j1_l10_sat", "table_stereotype": "sat", "satellite_parent_table": "RSAP_CARD_GROUP_OCRG_HUB", "diff_hash_column_name": "RH_RSAP_CARD_GROUP_OCRG_J1_L10_SAT"}]}], "deletion_detection_rules": [{"procedure": "stage_comparison", "tables_to_cleanup": ["rsap_card_group_ocrg_j1_l10_sat"]}]}