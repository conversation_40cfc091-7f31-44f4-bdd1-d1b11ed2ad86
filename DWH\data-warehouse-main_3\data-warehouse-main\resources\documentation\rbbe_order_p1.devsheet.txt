Data vault pipeline developer cheat sheet 
rendered from  rbbe_order_p1.dvpi

pipeline name:  rbbe_ORDER_P1

------------------------------------------------------
record source:  billbee.order

Json array path: $.Data

Source fields:
       BillBeeOrderId            ->       B<PERSON><PERSON><PERSON>EORDERID              NUMBER(36,0)
       Bill<PERSON>eeParentOrderId      ->       B<PERSON><PERSON><PERSON>EPARENTORDERID        NUMBER(36,0)
       Customer.Id               ->       CUSTOMER_ID                 NUMBER(36,0)
       Seller.BillbeeShopId      ->       B<PERSON>L<PERSON>ESHOPID               NUMBER(36,0)
       OrderNumber               ->       ORDERNUMBER                 VARCHAR(200)
       TotalCost                 ->       TOTALCOST                   NUMBER(20,2)
       AdjustmentCost            ->       ADJUSTMENTCOST              NUMBER(20,2)
       Currency                  ->       CURRENCY                    VARCHAR(200)
       ShippingCost              ->       SHIPPINGCOST                NUMBER(20,2)
       InvoiceAddress.CountryISO2->       INVOICEADDRESS_COUNTRYISO2  VARCHAR(20)
       State                     ->       STATE                       NUMBER(36,0)
       CreatedAt                 ->       CREATEDAT                   TIMESTAMP
       ShippedAt                 ->       SHIPPEDAT                   TIMESTAMP
       ConfirmedAt               ->       CONFIRMEDAT                 TIMESTAMP
       InvoiceDate               ->       INVOICEDATE                 TIMESTAMP
       PayedAt                   ->       PAYEDAT                     TIMESTAMP
       UpdatedAt                 ->       UPDATEDAT                   TIMESTAMP
       LastModifiedAt            ->       LASTMODIFIEDAT              TIMESTAMP
       Seller.BillbeeShopName    ->       SELLER_BILLBEESHOPNAME      VARCHAR(200)


------------------------------------------------------
Table List:
stage_table.rvlt_billbee.rbbe_ORDER_P1_stage
table.rvlt_billbee.rbbe_order_hub.[/]
table.rvlt_billbee.rbbe_order_hub.[PARENT]
table.rvlt_billbee.rbbe_order_order_p1_l10_sat
table.rvlt_billbee.rbbe_order_order_p2_l10_sat
table.rvlt_billbee.rbbe_customer_hub
table.rvlt_billbee.rbbe_order_customer_lnk
table.rvlt_billbee.rbbe_order_customer_esat
table.rvlt_billbee.rbbe_shop_hub
table.rvlt_billbee.rbbe_order_shop_lnk
table.rvlt_billbee.rbbe_order_shop_p1_l10_sat
table.rvlt_billbee.rbbe_order_parent_order_lnk
table.rvlt_billbee.rbbe_order_parent_order_esat

------------------------------------------------------
stage table:  stage_rvlt.rbbe_ORDER_P1_stage
Field to Stage mapping:
	--business keys,
		BILLBEEORDERID              >  BILLBEEORDERID,
		BILLBEEPARENTORDERID        >  BILLBEEPARENTORDERID,
		BILLBEESHOPID               >  BILLBEESHOPID,
		CUSTOMER_ID                 >  CUSTOMER_ID,

	--content,
		ADJUSTMENTCOST              >  ADJUSTMENTCOST,
		CONFIRMEDAT                 >  CONFIRMEDAT,
		CREATEDAT                   >  CREATEDAT,
		CURRENCY                    >  CURRENCY,
		INVOICEADDRESS_COUNTRYISO2  >  INVOICEADDRESS_COUNTRYISO2,
		INVOICEDATE                 >  INVOICEDATE,
		ORDERNUMBER                 >  ORDERNUMBER,
		PAYEDAT                     >  PAYEDAT,
		SELLER_BILLBEESHOPNAME      >  SELLER_BILLBEESHOPNAME,
		SHIPPEDAT                   >  SHIPPEDAT,
		SHIPPINGCOST                >  SHIPPINGCOST,
		STATE                       >  STATE,
		TOTALCOST                   >  TOTALCOST,

	--content untracked,
		LASTMODIFIEDAT              >  LASTMODIFIEDAT,
		UPDATEDAT                   >  UPDATEDAT

------------------------------------------------------
Hash value composition

HK_RBBE_ORDER (key)
		BILLBEEORDERID 

HK_RBBE_ORDER_PARENT (key)
		BILLBEEPARENTORDERID 

HK_RBBE_CUSTOMER (key)
		CUSTOMER_ID 

HK_RBBE_SHOP (key)
		BILLBEESHOPID 

LK_RBBE_ORDER_CUSTOMER (key)
		BILLBEEORDERID 
		CUSTOMER_ID 

LK_RBBE_ORDER_SHOP (key)
		BILLBEEORDERID 
		BILLBEESHOPID 

LK_RBBE_ORDER_PARENT_ORDER (key)
		BILLBEEORDERID 
		BILLBEEPARENTORDERID 

RH_RBBE_ORDER_ORDER_P1_L10_SAT (diff_hash)
		ADJUSTMENTCOST 
		CURRENCY 
		INVOICEADDRESS_COUNTRYISO2 
		ORDERNUMBER 
		SHIPPINGCOST 
		TOTALCOST 

RH_RBBE_ORDER_ORDER_P2_L10_SAT (diff_hash)
		CONFIRMEDAT 
		CREATEDAT 
		INVOICEDATE 
		PAYEDAT 
		SHIPPEDAT 
		STATE 

RH_RBBE_ORDER_SHOP_P1_L10_SAT (diff_hash)
		SELLER_BILLBEESHOPNAME 


------------------------------------------------------
Table load method
(STAGE >  VAULT)

rbbe_order_hub (/) can be loaded by convention
		  key: HK_RBBE_ORDER  >  HK_RBBE_ORDER
		  business_key: BILLBEEORDERID  >  BILLBEEORDERID 

rbbe_order_hub (PARENT) needs explicit loading:
		* key: HK_RBBE_ORDER_PARENT  >  HK_RBBE_ORDER
		* business_key: BILLBEEPARENTORDERID  >  BILLBEEORDERID 

rbbe_order_order_p1_l10_sat (/) can be loaded by convention
		  parent_key: HK_RBBE_ORDER  >  HK_RBBE_ORDER
		  diff_hash: RH_RBBE_ORDER_ORDER_P1_L10_SAT  >  RH_RBBE_ORDER_ORDER_P1_L10_SAT
		  content: ORDERNUMBER  >  ORDERNUMBER 
		  content: TOTALCOST  >  TOTALCOST 
		  content: ADJUSTMENTCOST  >  ADJUSTMENTCOST 
		  content: CURRENCY  >  CURRENCY 
		  content: SHIPPINGCOST  >  SHIPPINGCOST 
		  content: INVOICEADDRESS_COUNTRYISO2  >  INVOICEADDRESS_COUNTRYISO2 
		  content_untracked: UPDATEDAT  >  UPDATEDAT 
		  content_untracked: LASTMODIFIEDAT  >  LASTMODIFIEDAT 

rbbe_order_order_p2_l10_sat (/) can be loaded by convention
		  parent_key: HK_RBBE_ORDER  >  HK_RBBE_ORDER
		  diff_hash: RH_RBBE_ORDER_ORDER_P2_L10_SAT  >  RH_RBBE_ORDER_ORDER_P2_L10_SAT
		  content: STATE  >  STATE 
		  content: CREATEDAT  >  CREATEDAT 
		  content: SHIPPEDAT  >  SHIPPEDAT 
		  content: CONFIRMEDAT  >  CONFIRMEDAT 
		  content: INVOICEDATE  >  INVOICEDATE 
		  content: PAYEDAT  >  PAYEDAT 
		  content_untracked: UPDATEDAT  >  UPDATEDAT 
		  content_untracked: LASTMODIFIEDAT  >  LASTMODIFIEDAT 

rbbe_customer_hub (/) needs explicit loading:
		  key: HK_RBBE_CUSTOMER  >  HK_RBBE_CUSTOMER
		* business_key: CUSTOMER_ID  >  ID 

rbbe_order_customer_lnk (/) can be loaded by convention
		  parent_key_1: HK_RBBE_ORDER  >  HK_RBBE_ORDER
		  parent_key_2: HK_RBBE_CUSTOMER  >  HK_RBBE_CUSTOMER
		  key: LK_RBBE_ORDER_CUSTOMER  >  LK_RBBE_ORDER_CUSTOMER

rbbe_order_customer_esat (/) can be loaded by convention
		! Driving keys: HK_RBBE_ORDER 
		  parent_key: LK_RBBE_ORDER_CUSTOMER  >  LK_RBBE_ORDER_CUSTOMER

rbbe_shop_hub (/) can be loaded by convention
		  key: HK_RBBE_SHOP  >  HK_RBBE_SHOP
		  business_key: BILLBEESHOPID  >  BILLBEESHOPID 

rbbe_order_shop_lnk (/) can be loaded by convention
		  parent_key_1: HK_RBBE_ORDER  >  HK_RBBE_ORDER
		  parent_key_2: HK_RBBE_SHOP  >  HK_RBBE_SHOP
		  key: LK_RBBE_ORDER_SHOP  >  LK_RBBE_ORDER_SHOP

rbbe_order_shop_p1_l10_sat (/) can be loaded by convention
		! Driving keys: HK_RBBE_ORDER 
		  parent_key: LK_RBBE_ORDER_SHOP  >  LK_RBBE_ORDER_SHOP
		  diff_hash: RH_RBBE_ORDER_SHOP_P1_L10_SAT  >  RH_RBBE_ORDER_SHOP_P1_L10_SAT
		  content: SELLER_BILLBEESHOPNAME  >  SELLER_BILLBEESHOPNAME 

rbbe_order_parent_order_lnk (/) can be loaded by convention
		  parent_key_1: HK_RBBE_ORDER  >  HK_RBBE_ORDER
		  parent_key_2: HK_RBBE_ORDER_PARENT  >  HK_RBBE_ORDER_PARENT
		  key: LK_RBBE_ORDER_PARENT_ORDER  >  LK_RBBE_ORDER_PARENT_ORDER

rbbe_order_parent_order_esat (/) can be loaded by convention
		! Driving keys: HK_RBBE_ORDER 
		  parent_key: LK_RBBE_ORDER_PARENT_ORDER  >  LK_RBBE_ORDER_PARENT_ORDER

