CREATE OR REPLACE VIEW rvlt_sap.rsap_credit_memo_line_rin1_p1_l20_sat
AS
SELECT
    MD_INSERTED_AT,
	MD_RUN_ID,
	MD_RECORD_SOURCE,
    MD_IS_DELETED,
    MD_VALID_BEFORE,
    HK_RSAP_CREDIT_MEMO_LINE_RIN1,
    RH_RSAP_CREDIT_MEMO_LINE_RIN1_J1_L10_SAT,
    JSON_EXTRACT_PATH_TEXT(JSON_TEXT, 'BaseLine') as BASELINE,
    JSON_EXTRACT_PATH_TEXT(JSON_TEXT, 'BaseEntry') as BASENTRY,
    JSON_EXTRACT_PATH_TEXT(JSON_TEXT, 'BaseType') as BASETYPE,
    JSON_EXTRACT_PATH_TEXT(JSON_TEXT, 'Currency') as CURRENCY,
    TO_NUMBER(JSON_EXTRACT_PATH_TEXT(JSON_TEXT, 'LineTotal'),38,3) as LINETOTAL,
    JSON_EXTRACT_PATH_TEXT(JSON_TEXT, 'LineType') as LINETYPE,
    JSO<PERSON>_EXTRACT_PATH_TEXT(JSON_TEXT, 'OcrCode') as OCRCODE,
    JSON_EXTRACT_PATH_TEXT(JSON_TEXT, 'OcrCode2') as OCRCODE2,
    TO_NUMBER(JSON_EXTRACT_PATH_TEXT(JSON_TEXT, 'Rate'),10,3) as RATE
FROM rvlt_sap.rsap_credit_memo_line_rin1_j1_l10_sat;
