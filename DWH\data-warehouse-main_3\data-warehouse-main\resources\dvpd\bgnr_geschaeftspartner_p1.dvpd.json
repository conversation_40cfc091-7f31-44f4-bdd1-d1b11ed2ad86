{"dvpd_version": "0.6.2", "stage_properties": [{"stage_schema": "stage_bvlt"}], "pipeline_name": "bgnr_geschaeftspartner_p1", "record_source_name_expression": "bgnr_geschaeftspartner_p1", "data_extraction": {"fetch_module_name": "none - ddl generation only"}, "fields": [{"field_name": "xd_geschaeftspartner_id", "field_type": "VARCHAR(255)", "targets": [{"table_name": "bgnr_geschaeftspartner_hub"}]}, {"field_name": "Gesellschaft", "field_type": "VARCHAR(255)", "targets": [{"table_name": "bgnr_geschaeftspartner_p1_b10_sat"}]}, {"field_name": "Name", "field_type": "VARCHAR(255)", "targets": [{"table_name": "bgnr_geschaeftspartner_p1_b10_sat"}]}, {"field_name": "Kundengruppe ", "field_type": "VARCHAR(255)", "targets": [{"table_name": "bgnr_geschaeftspartner_p1_b10_sat"}]}, {"field_name": "Verkaufsgebiet ", "field_type": "VARCHAR(255)", "targets": [{"table_name": "bgnr_geschaeftspartner_p1_b10_sat"}]}, {"field_name": "Verkaeufernummer ", "field_type": "VARCHAR(255)", "targets": [{"table_name": "bgnr_geschaeftspartner_p1_b10_sat"}]}, {"field_name": "Company", "field_type": "VARCHAR(255)", "targets": [{"table_name": "rsap_business_partner_ocrd_hub"}]}, {"field_name": "Cardcode", "field_type": "VARCHAR(255)", "targets": [{"table_name": "rsap_business_partner_ocrd_hub"}]}, {"field_name": "BillBeeShopId", "field_type": "NUMBER(36,0)", "targets": [{"table_name": "RBBE_SHOP_HUB"}]}], "data_vault_model": [{"schema_name": "rvlt_sap", "tables": [{"table_name": "rsap_business_partner_ocrd_hub", "table_stereotype": "hub", "is_only_structural_element": "true", "hub_key_column_name": "HK_RSAP_BUSINESS_PARTNER_OCRD"}]}, {"schema_name": "rvlt_billbee", "tables": [{"table_name": "RBBE_SHOP_HUB", "table_stereotype": "hub", "is_only_structural_element": "true", "hub_key_column_name": "HK_RBBE_SHOP"}]}, {"schema_name": "bvlt_general", "tables": [{"table_name": "bgnr_geschaeftspartner_hub", "table_stereotype": "hub", "hub_key_column_name": "HK_bgnr_geschaeftspartner"}, {"table_name": "bgnr_geschaeftspartner_p1_b10_sat", "table_stereotype": "sat", "satellite_parent_table": "bgnr_geschaeftspartner_hub", "diff_hash_column_name": "rh_bgnr_geschaeftspartner_p1_b10_sat"}, {"table_name": "bgnr_geschaeftspartner_rsap_business_partner_LNK", "table_stereotype": "lnk", "link_key_column_name": "LK_bgnr_geschaeftspartner_rsap_business_partner", "link_parent_tables": ["bgnr_geschaeftspartner_hub", "RSAP_BUSINESS_PARTNER_OCRD_HUB"]}, {"table_name": "bgnr_geschaeftspartner_rsap_business_partner_ESAT", "table_stereotype": "sat", "satellite_parent_table": "bgnr_geschaeftspartner_rsap_business_partner_LNK", "driving_keys": ["HK_bgnr_geschaeftspartner"]}, {"table_name": "bgnr_geschaeftspartner_rbbe_shop_LNK", "table_stereotype": "lnk", "link_key_column_name": "LK_bgnr_geschaeftspartner_rbbe_shop", "link_parent_tables": ["bgnr_geschaeftspartner_hub", "RBBE_SHOP_HUB"]}, {"table_name": "bgnr_geschaeftspartner_rbbe_shop_ESAT", "table_stereotype": "sat", "satellite_parent_table": "bgnr_geschaeftspartner_rbbe_shop_LNK", "driving_keys": ["HK_bgnr_geschaeftspartner"]}]}], "deletion_detection": [{"procedure": "stage_comparison", "tables_to_cleanup": ["bgnr_geschaeftspartner_p1_b10_sat", "bgnr_geschaeftspartner_rsap_business_partner_ESAT", "bgnr_geschaeftspartner_rbbe_customer_ESAT"]}]}