/* Alle verkäufe aus Billdbee = SUND_DIGITAL */
select 
us.* exclude dk_ges<PERSON><PERSON><PERSON><PERSON>tner ,
gp.gesellschaft ,
gp.name gp_name,
gp.kundengruppe gp_kundengruppe,
gp.verkaufsgebiet gp_verkaufsgebiet,
gp.verkaeufernummer gp_verkaeufernummer
from MART_AUFTRAEGE.F_UMSATZ us
join MART_AUFTRAEGE.D_GESCHAEFTSPARTNER gp
	on gp.dk_geschaeftspartner = us.dk_geschaeftspartner 
where us.gesellschaft='SUND_DIGITAL'	
order by us.kanal , tagesdatum 

/* Aufstellung Wähungsdaten */
select company ,currency ,min(ratedate),max(ratedate),count(1),max(md_inserted_at )
from 
BVLT_SAP.BSAP_EXCHANGE_RATE_ORTT_CURRENT_REF
group by 1,2
order by 1,2


/* Wo sind otto und amazon ? */
select 
SELLER_BILLBEESHOPNAME--, count(1)
from RVLT_BILLBEE.RBBE_ORDER_SHOP_P1_L10_SAT shop_sat
join RVLT_BILLBEE.RBBE_ORDER_SHOP_LNK order_shop_l 
	on order_shop_l.LK_RBBE_ORDER_SHOP = shop_sat.lk_rbbe_order_shop 
join RVLT_BILLBEE.RBBE_ORDER_ORDER_P1_L10_SAT order_sat
on order_sat.hk_rbbe_order = order_shop_l.HK_RBBE_ORDER
join RVLT_BILLBEE.RBBE_ORDER_ITEM_ORDER_LNK item_order_l 
	on item_order_l.hk_rbbe_order = order_sat.hk_rbbe_order 
join RVLT_BILLBEE.RBBE_ORDER_ITEM_ORDER_P1_L10_SAT item_s
	on item_s.hk_rbbe_order_item = item_order_l.hk_rbbe_order_item 
join RVLT_BILLBEE.RBBE_ORDER_ITEM_PRODUCT_LNK item_product_l 
    on item_product_l.hk_rbbe_order_item = item_order_l.hk_rbbe_order_item 
group by 1;
	

8f6yObAzvdB7WzxGwA/esSNROOY=
DbvxaKyqvG1DHZqjKNXjcBNQa4Q=
+LeqKtoPJhpa1GtVETz64Mkatbs=


with order_summierte_orderpositionen as (
		select item_order_l.hk_rbbe_order,
			count_if( item_prod_l.hk_rbbe_product<>'0000000000000000000000000000') 	    as product_item_count,
			count(1) 																	as order_item_count,
			sum(case when item_prod_l.hk_rbbe_product='0000000000000000000000000000' 
					then item_s.totalprice
					else 0
					end) 																as  zusatz_totalprice_sum,
			sum(case when item_prod_l.hk_rbbe_product='0000000000000000000000000000' 
					then item_s.taxamount 
					else 0
					end) 																as  zusatz_taxamount_sum,			
			sum(case when item_prod_l.hk_rbbe_product<>'0000000000000000000000000000' 
					then item_s.totalprice
					else 0
					end) 																as  product_totalprice_sum,
			sum(case when item_prod_l.hk_rbbe_product<>'0000000000000000000000000000' 
					then item_s.taxamount 
					else 0
					end) 																as product_taxamount_sum			
		from RVLT_BILLBEE.RBBE_ORDER_ITEM_ORDER_LNK item_order_l
		join RVLT_BILLBEE.RBBE_ORDER_ITEM_ORDER_ESAT item_order_e
			on item_order_e.lk_rbbe_order_item_order =item_order_l.lk_rbbe_order_item_order 
			and item_order_e.md_valid_before =lib.dwh_far_future_date()
			and not item_order_e.md_is_deleted		
		join RVLT_BILLBEE.RBBE_ORDER_ITEM_ORDER_P1_L10_SAT	item_s
			on item_s.hk_rbbe_order_item = item_order_l.hk_rbbe_order_item 
			and item_s.md_valid_before =lib.dwh_far_future_date()
			and not item_s.md_is_deleted
		join RVLT_BILLBEE.RBBE_ORDER_ITEM_PRODUCT_LNK item_prod_l
			on item_prod_l.hk_rbbe_order_item = item_order_l.hk_rbbe_order_item 
		join RVLT_BILLBEE.RBBE_ORDER_ITEM_PRODUCT_ESAT item_prod_e
		  on item_prod_e.lk_rbbe_order_item_product = item_prod_l.lk_rbbe_order_item_product 
			and item_prod_e.md_valid_before =lib.dwh_far_future_date()
			and not item_prod_e.md_is_deleted	
		group by 1
		)		
,order_mit_zusatzkosten as (
select order_s.hk_rbbe_order
		,order_sumpos.zusatz_totalprice_sum+order_s.shippingcost   											as zu_verteilender_wert_brutto
		,order_sumpos.zusatz_totalprice_sum-order_sumpos.zusatz_taxamount_sum+order_s.shippingcost/1.19   	as zu_verteilender_wert_netto
		,order_sumpos.product_totalprice_sum 	 															
		,order_s.shippingcost
		,order_sumpos.product_item_count
		,order_sumpos.order_item_count
from order_summierte_orderpositionen order_sumpos
		join RVLT_BILLBEE.RBBE_ORDER_ORDER_P1_L10_SAT   order_s
			   on order_s.hk_rbbe_order =order_sumpos.hk_rbbe_order 
			   and order_s.md_valid_before =lib.dwh_far_future_date()
			   and not order_s.md_is_deleted 
  ) 
select 	item_order_l.hk_rbbe_order_item 
		,item_s.totalprice-item_s.taxamount totalprice_netto
		,omzk.zu_verteilender_wert_netto
		,item_s.totalprice/omzk.product_totalprice_sum							as product_verteilschluessel     
		,item_s.totalprice - item_s.taxamount + omzk.zu_verteilender_wert_netto* (item_s.totalprice/omzk.product_totalprice_sum) as totalprice_netto_korrigiert
		,item_order_l.hk_rbbe_order as hk_rbbe_order___FOR_DEBUG_ONLY
from order_mit_zusatzkosten omzk
	join DV_D_MAIN_DATABASE.RVLT_BILLBEE.RBBE_ORDER_ITEM_ORDER_LNK item_order_l
		on item_order_l.hk_rbbe_order = omzk.hk_rbbe_order
	join DV_D_MAIN_DATABASE.RVLT_BILLBEE.RBBE_ORDER_ITEM_ORDER_ESAT item_order_e
		on item_order_e.lk_rbbe_order_item_order =item_order_l.lk_rbbe_order_item_order 
		and item_order_e.md_valid_before =lib.dwh_far_future_date()
		and not item_order_e.md_is_deleted
	join DV_D_MAIN_DATABASE.RVLT_BILLBEE.RBBE_ORDER_ITEM_ORDER_P1_L10_SAT	item_s
		on item_s.hk_rbbe_order_item = item_order_l.hk_rbbe_order_item 
		and item_s.md_valid_before =lib.dwh_far_future_date()
		and not item_s.md_is_deleted
	join RVLT_BILLBEE.RBBE_ORDER_ITEM_PRODUCT_LNK item_prod_l
		on item_prod_l.hk_rbbe_order_item = item_order_l.hk_rbbe_order_item 
	join RVLT_BILLBEE.RBBE_ORDER_ITEM_PRODUCT_ESAT item_prod_e
	  on item_prod_e.lk_rbbe_order_item_product = item_prod_l.lk_rbbe_order_item_product 
		and item_prod_e.md_valid_before =lib.dwh_far_future_date()
		and not item_prod_e.md_is_deleted	
where omzk.product_item_count>0 -- es müssen items mit product im order  sein
	   and omzk.product_totalprice_sum<>0	-- Produkte müssen insgesamt geld gekostet haben
       and item_prod_l.hk_rbbe_product<>'0000000000000000000000000000' -- items die kein Produkt sind, werden rausgehalten
       and item_order_l.hk_rbbe_order ='8f6yObAzvdB7WzxGwA/esSNROOY='

group by 1
order by 1