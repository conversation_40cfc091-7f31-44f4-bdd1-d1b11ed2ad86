"""

__main__.py

"""
from lib.cimtjobinstance import CimtJobInstance, cimtjobinstance_job
from lib.dvf_basics import Dvf_dwh_connection_type
from lib.dvf_ddl_deploymentmanager_snf import Dvf_ddl_deploymentmanager_snf
from lib.connection_snf import  connection_snf_for_dwh_connection_type
from lib.blobstorage_utils import get_source_files_from_blob_storage, move_processed_file_to_processed_container
from lib.connection_azrblob import connection_azrblob

from load_vault_1 import load_vault_1


def deploy_datamodel(my_job_instance):
    run_id=my_job_instance.get_job_instance_id()
    insert_date=my_job_instance.get_job_started_at()
    deployment_manager = Dvf_ddl_deploymentmanager_snf(connection_snf_for_dwh_connection_type(Dvf_dwh_connection_type.owner),run_id,insert_date)
    deployment_manager.deploy_stage_table("rvlt_sap", "rsap_hld1_j1_stage")
    deployment_manager.deploy_table("rvlt_sap", "rsap_feiertage_hld1_hub")
    deployment_manager.deploy_table("rvlt_sap", "rsap_feiertage_hld1_j1_l10_msat")
    deployment_manager.deploy_view("rvlt_sap", "rsap_feiertage_hld1_p1_l20_msat")


@cimtjobinstance_job
def main(**kwargs):
    # ### FRAMEWORK PHASE: setup job_instance for main module
    my_job_instance = CimtJobInstance(kwargs['instance_job_name'])
    my_job_instance.start_instance()

    sap_source_object = "HLD1"
    container_name = "rawdata"
    blob_service_client = connection_azrblob('blob_storage')

    try:        # ### FRAMEWORK PHASE: do processing here
        deploy_datamodel(my_job_instance)
        # gets all files for defined sap object from defined container in Blob Storage
        source_files = get_source_files_from_blob_storage(blob_service_client, sap_source_object, container_name)
        # sorts files alphabetically to process from the oldest to the newest
        sorted_source_file_names = sorted([file.name for file in source_files])
        # loops through all fetched files, loads them to dwh
        for file_name in sorted_source_file_names:
            load_vault_1(parent_job_instance=my_job_instance, file_name=file_name)
            # if load is successful, moves file to container with processed file, per default from rawdata to processed
            move_processed_file_to_processed_container(blob_service_client, file_name)

        blob_service_client.close()
    # # ### FRAMEWORK PHASE: End with bad or good result
    except Exception as e:
        my_job_instance.end_instance_with_error(1, 'some processing failed')
        raise e
    my_job_instance.end_instance()


if __name__ == '__main__':
    main()
