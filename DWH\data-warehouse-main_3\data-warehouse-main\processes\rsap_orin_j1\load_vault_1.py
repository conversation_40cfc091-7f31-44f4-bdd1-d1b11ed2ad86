import json

from lib.json_utils import get_json_text_content_column, sort_json_content_column
from lib.blobstorage_utils import fetch_json_source_file_from_blob_container
from lib.cimtjobinstance import CimtJobInstance, cimtjobinstance_job
from lib.connection_snf import  connection_snf_for_dwh_connection_type
from lib.connection_azrblob import connection_azrblob

from lib.dvf_basics import dvf_assemble_datavault_hash, Dvf_dwh_connection_type
from lib.dvf_sqlByConvention_snf import dvf_get_datavault_hub_elt_sql, \
    dvf_get_datavault_sat_elt_sql, dvf_execute_elt_statement_list, dvf_get_check_hash_collision_hub_elt_sql, dvf_get_check_singularity_sat_elt_sql, \
    dvf_get_datavault_lnk_elt_sql, dvf_get_datavault_esat_elt_sql,dvf_get_check_hash_collision_lnk_elt_sql

from lib.dbutils_snf import get_snf_dict_insert_sql, execute_snf_dict_bulk_insert



@cimtjobinstance_job
def fetch_and_stage_file(parent_job_instance, dwh_connection, arzblob_connection, file_name, bk_keys, exclude_from_hash_diff=None, **kwargs):

    my_job_instance = CimtJobInstance(kwargs['instance_job_name'], parent_job_instance)
    my_job_instance.set_work_item(file_name)
    my_job_instance.start_instance()

    try:
        # ### FRAMEWORK PHASE: do processing here

        # prepare stage insert operation
        dwh_cursor = dwh_connection.cursor()
        dwh_cursor.execute("TRUNCATE TABLE stage_rvlt.rsap_orin_j1_stage")
        insert_statement = get_snf_dict_insert_sql(dwh_connection, "stage_rvlt", "rsap_orin_j1_stage")
        stage_data_row = {'md_inserted_at': my_job_instance.get_job_started_at().isoformat(),
                    'md_record_source': 'sap.orin',
                    'md_run_id': my_job_instance.get_job_instance_id(),
                    'md_is_deleted': False
                   }

        # fetch source files from blob storage
        source_container = "rawdata"

        blob_client = arzblob_connection.get_blob_client(container=source_container, blob=file_name)
        source_json = fetch_json_source_file_from_blob_container(blob_client)

        # prepare stage data
        stage_data_rows = []

        for source_row in source_json:
            stage_data_row_temp = dict(stage_data_row)

            stage_data_row_temp['company'] = source_row.get('Company')
            stage_data_row_temp['cardcode'] = source_row.get('CardCode')
            stage_data_row_temp['docentry'] = source_row.get('DocEntry')
            stage_data_row_temp['slpcode'] = source_row.get('SlpCode')

            # treatment when foreign keys are used as business keys
            company_for_cardcode = None if stage_data_row_temp['cardcode'] is None else stage_data_row_temp['company']
            company_for_slpcode = None if stage_data_row_temp['slpcode'] is None else stage_data_row_temp['company']

            json_text = sort_json_content_column(get_json_text_content_column(source_row, keys_to_remove=bk_keys))
            stage_data_row_temp['json_text'] = json.dumps(json_text)
            json_text_for_hash_diff = json_text if exclude_from_hash_diff is None else get_json_text_content_column(json_text , keys_to_remove=exclude_from_hash_diff)

            ### hashes
            stage_data_row_temp['hk_rsap_credit_memo_orin'] = dvf_assemble_datavault_hash([stage_data_row_temp['company'], stage_data_row_temp['docentry']])
            stage_data_row_temp['hk_rsap_business_partner_ocrd'] = dvf_assemble_datavault_hash([stage_data_row_temp['cardcode'], company_for_cardcode])
            stage_data_row_temp['hk_rsap_sales_employee_oslp'] = dvf_assemble_datavault_hash([company_for_slpcode, stage_data_row_temp['slpcode']])
            stage_data_row_temp['lk_rsap_credit_memo_orin_business_partner_ocrd_sales_employee_oslp'] = dvf_assemble_datavault_hash([stage_data_row_temp['company'],
                                                                                                                                     stage_data_row_temp['docentry'],
                                                                                                                                     stage_data_row_temp['cardcode'],
                                                                                                                                     company_for_cardcode,
                                                                                                                                     company_for_slpcode,
                                                                                                                                     stage_data_row_temp['slpcode']
                                                                                                                                     ])

            stage_data_row_temp['rh_rsap_credit_memo_orin_j1_l10_sat'] = dvf_assemble_datavault_hash([json_text_for_hash_diff])

            stage_data_rows.append(stage_data_row_temp)
            my_job_instance.count_input(1)

        execute_snf_dict_bulk_insert(dwh_cursor, insert_statement, stage_data_rows)
        dwh_connection.commit()

    # ### FRAMEWORK PHASE: Log any exception to instance
    except Exception:
        my_job_instance.end_instance_with_error(1, 'something went wrong')
        raise

    # # ### FRAMEWORK PHASE: End the instance normally
    my_job_instance.end_instance()


@cimtjobinstance_job
def load_data_to_vault(parent_job_instance, dwh_connection, file_name=None, **kwargs):


    # ### FRAMEWORK PHASE: setup your job_instance
    my_job_instance = CimtJobInstance(kwargs['instance_job_name'], parent_job_instance)
    my_job_instance.set_work_item(file_name)
    my_job_instance.start_instance()
    try:

        # ### FRAMEWORK PHASE: do processing here
        # BEGIN LOAD DATA TO VLT PART
        # general constants
        stage_schema = 'stage_rvlt'
        stage_table = 'rsap_orin_j1_stage'


        dwh_cursor = dwh_connection.cursor()


        hash_collision_check_statement_list = dvf_get_check_hash_collision_hub_elt_sql(vault_table='rsap_credit_memo_orin_hub',
        vault_schema='rvlt_sap',
        stage_hk_column='HK_RSAP_CREDIT_MEMO_ORIN',
        stage_bk_column_list=['COMPANY', 'DOCENTRY'],
        db_connection=dwh_connection,
        stage_schema=stage_schema,
        stage_table=stage_table)

        dvf_execute_elt_statement_list(dwh_cursor, hash_collision_check_statement_list)

        statement_list = dvf_get_datavault_hub_elt_sql(vault_table='rsap_credit_memo_orin_hub',
        vault_schema='rvlt_sap',
        stage_hk_column='HK_RSAP_CREDIT_MEMO_ORIN',
        stage_bk_column_list=['COMPANY', 'DOCENTRY'],
        db_connection=dwh_connection,
        stage_schema=stage_schema,
        stage_table=stage_table,
        meta_job_instance_id = my_job_instance.get_job_instance_id(),
        meta_inserted_at = my_job_instance.get_job_started_at() )

        dvf_execute_elt_statement_list(dwh_cursor, statement_list)

        # ----------


        singularity_check_statement_list = dvf_get_check_singularity_sat_elt_sql(vault_table='rsap_credit_memo_orin_j1_l10_sat',
        stage_hk_column='HK_RSAP_CREDIT_MEMO_ORIN',
        stage_rh_column='RH_RSAP_CREDIT_MEMO_ORIN_J1_L10_SAT',
        stage_schema=stage_schema,
        stage_table=stage_table )

        dvf_execute_elt_statement_list(dwh_cursor, singularity_check_statement_list)

        statement_list = dvf_get_datavault_sat_elt_sql(vault_table='rsap_credit_memo_orin_j1_l10_sat',
        vault_schema='rvlt_sap',
        stage_hk_column='HK_RSAP_CREDIT_MEMO_ORIN',
        stage_rh_column='RH_RSAP_CREDIT_MEMO_ORIN_J1_L10_SAT',
        stage_content_column_list=['JSON_TEXT'],
        with_deletion_detection=False,
        db_connection=dwh_connection,
        stage_schema=stage_schema,
        stage_table=stage_table,
        meta_job_instance_id = my_job_instance.get_job_instance_id(),
        meta_inserted_at = my_job_instance.get_job_started_at() )

        dvf_execute_elt_statement_list(dwh_cursor, statement_list)

        # ----------


        hash_collision_check_statement_list = dvf_get_check_hash_collision_hub_elt_sql(vault_table='rsap_business_partner_ocrd_hub',
        vault_schema='rvlt_sap',
        stage_hk_column='HK_RSAP_BUSINESS_PARTNER_OCRD',
        stage_bk_column_list=['CARDCODE', 'COMPANY'],
        db_connection=dwh_connection,
        stage_schema=stage_schema,
        stage_table=stage_table)

        dvf_execute_elt_statement_list(dwh_cursor, hash_collision_check_statement_list)

        statement_list = dvf_get_datavault_hub_elt_sql(vault_table='rsap_business_partner_ocrd_hub',
        vault_schema='rvlt_sap',
        stage_hk_column='HK_RSAP_BUSINESS_PARTNER_OCRD',
        stage_bk_column_list=['CARDCODE', 'COMPANY'],
        db_connection=dwh_connection,
        stage_schema=stage_schema,
        stage_table=stage_table,
        meta_job_instance_id = my_job_instance.get_job_instance_id(),
        meta_inserted_at = my_job_instance.get_job_started_at() )

        dvf_execute_elt_statement_list(dwh_cursor, statement_list)

        # ----------


        hash_collision_check_statement_list = dvf_get_check_hash_collision_hub_elt_sql(vault_table='rsap_sales_employee_oslp_hub',
        vault_schema='rvlt_sap',
        stage_hk_column='HK_RSAP_SALES_EMPLOYEE_OSLP',
        stage_bk_column_list=['COMPANY', 'SLPCODE'],
        db_connection=dwh_connection,
        stage_schema=stage_schema,
        stage_table=stage_table)

        dvf_execute_elt_statement_list(dwh_cursor, hash_collision_check_statement_list)

        statement_list = dvf_get_datavault_hub_elt_sql(vault_table='rsap_sales_employee_oslp_hub',
        vault_schema='rvlt_sap',
        stage_hk_column='HK_RSAP_SALES_EMPLOYEE_OSLP',
        stage_bk_column_list=['COMPANY', 'SLPCODE'],
        db_connection=dwh_connection,
        stage_schema=stage_schema,
        stage_table=stage_table,
        meta_job_instance_id = my_job_instance.get_job_instance_id(),
        meta_inserted_at = my_job_instance.get_job_started_at() )

        dvf_execute_elt_statement_list(dwh_cursor, statement_list)

        # ----------


        hash_collision_check_statement_list = dvf_get_check_hash_collision_lnk_elt_sql(vault_table='rsap_credit_memo_orin_business_partner_ocrd_sales_employee_oslp_lnk',
        vault_schema='rvlt_sap',
        stage_lk_column='LK_RSAP_CREDIT_MEMO_ORIN_BUSINESS_PARTNER_OCRD_SALES_EMPLOYEE_OSLP',
        stage_hk_column_list=['HK_RSAP_BUSINESS_PARTNER_OCRD', 'HK_RSAP_CREDIT_MEMO_ORIN', 'HK_RSAP_SALES_EMPLOYEE_OSLP'],
        db_connection=dwh_connection,
        stage_schema=stage_schema,
        stage_table=stage_table)

        dvf_execute_elt_statement_list(dwh_cursor, hash_collision_check_statement_list)

        statement_list = dvf_get_datavault_lnk_elt_sql(vault_table='rsap_credit_memo_orin_business_partner_ocrd_sales_employee_oslp_lnk',
        vault_schema='rvlt_sap',
        stage_lk_column='LK_RSAP_CREDIT_MEMO_ORIN_BUSINESS_PARTNER_OCRD_SALES_EMPLOYEE_OSLP',
        stage_hk_column_list=['HK_RSAP_BUSINESS_PARTNER_OCRD', 'HK_RSAP_CREDIT_MEMO_ORIN', 'HK_RSAP_SALES_EMPLOYEE_OSLP'],
        db_connection=dwh_connection,
        stage_schema=stage_schema,
        stage_table=stage_table,
        meta_job_instance_id = my_job_instance.get_job_instance_id(),
        meta_inserted_at = my_job_instance.get_job_started_at() )

        dvf_execute_elt_statement_list(dwh_cursor, statement_list)

        # ----------


        statement_list = dvf_get_datavault_esat_elt_sql(vault_esat_table='rsap_credit_memo_orin_business_partner_ocrd_sales_employee_oslp_esat',
        vault_schema='rvlt_sap',
        stage_lk_column='LK_RSAP_CREDIT_MEMO_ORIN_BUSINESS_PARTNER_OCRD_SALES_EMPLOYEE_OSLP',
        vault_lnk_table='rsap_credit_memo_orin_business_partner_ocrd_sales_employee_oslp_lnk',
        vault_driving_key_column_list=['HK_RSAP_CREDIT_MEMO_ORIN'],
        stage_driving_key_column_list=['HK_RSAP_CREDIT_MEMO_ORIN'],
        with_deletion_detection=False,
        db_connection=dwh_connection,
        stage_schema=stage_schema,
        stage_table=stage_table,
        meta_job_instance_id = my_job_instance.get_job_instance_id(),
        meta_inserted_at = my_job_instance.get_job_started_at() )

        dvf_execute_elt_statement_list(dwh_cursor, statement_list)

        # ----------


        # END LOAD DATA TO VLT PART

        dwh_connection.commit()
    # ### FRAMEWORK PHASE: Log any exception to instance
    except Exception:
        my_job_instance.end_instance_with_error(1, 'something went wrong')
        raise
    # # ### FRAMEWORK PHASE: End the instance normally
    my_job_instance.end_instance()



@cimtjobinstance_job
def load_vault_1(file_name, parent_job_instance=None, **kwargs):

    # ### FRAMEWORK PHASE: setup your job_instance
    my_job_instance = CimtJobInstance(kwargs['instance_job_name'], parent_job_instance)
    my_job_instance.set_work_item(file_name)
    my_job_instance.start_instance()

    bk_keys = ['CardCode', 'Company', 'DocEntry', 'SlpCode']
    exclude_from_hash_diff = ['UpdateDate']

    try:
        # ### FRAMEWORK PHASE: do processing here
        dwh_connection = connection_snf_for_dwh_connection_type(Dvf_dwh_connection_type.raw_vault)
        arzblob_connection = connection_azrblob()
        fetch_and_stage_file(my_job_instance, dwh_connection, arzblob_connection, file_name, bk_keys,exclude_from_hash_diff)
        load_data_to_vault(my_job_instance, dwh_connection, file_name)
        dwh_connection.close()

    # ### FRAMEWORK PHASE: Log any exception to instance
    except Exception:
        my_job_instance.end_instance_with_error(1, 'some processing failed')
        raise

    # # ### FRAMEWORK PHASE: End the instance normally
    my_job_instance.end_instance()


if __name__ == '__main__':
    # local execution
    load_vault_1()