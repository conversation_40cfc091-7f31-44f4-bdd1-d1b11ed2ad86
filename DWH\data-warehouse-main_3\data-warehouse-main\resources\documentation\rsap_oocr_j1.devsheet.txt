Data vault pipeline developer cheat sheet 
rendered from  rsap_oocr_j1.dvpi

pipeline name:  rsap_oocr_j1

------------------------------------------------------
record source:  sap.oocr

Source fields:
       COMPANY    VARCHAR(50)
       OCRCODE    VARCHAR(20)
       JSON_TEXT  VARCHAR


------------------------------------------------------
Table List:
stage_table.rvlt_sap.rsap_oocr_j1_stage
table.rvlt_sap.rsap_distribution_rule_oocr_hub
table.rvlt_sap.rsap_distribution_rule_oocr_j1_l10_sat

------------------------------------------------------
stage table:  stage_rvlt.rsap_oocr_j1_stage
Field to Stage mapping:
	--business keys,
		COMPANY    >  COMPANY,
		OCRCODE    >  OCRCODE,

	--content,
		JSON_TEXT  >  JSON_TEXT

------------------------------------------------------
Hash value composition

HK_RSAP_DISTRIBUTION_RULE_OOCR (key)
		COMPANY 
		OCRCODE 

RH_RSAP_DISTRIBUTION_RULE_OOCR_J1_L10_SAT (diff_hash)
		JSON_TEXT 


------------------------------------------------------
Table load method
(STAGE >  VAULT)

rsap_distribution_rule_oocr_hub (/) can be loaded by convention
		  key: HK_RSAP_DISTRIBUTION_RULE_OOCR  >  HK_RSAP_DISTRIBUTION_RULE_OOCR
		  business_key: COMPANY  >  COMPANY 
		  business_key: OCRCODE  >  OCRCODE 

rsap_distribution_rule_oocr_j1_l10_sat (/) can be loaded by convention
		  parent_key: HK_RSAP_DISTRIBUTION_RULE_OOCR  >  HK_RSAP_DISTRIBUTION_RULE_OOCR
		  diff_hash: RH_RSAP_DISTRIBUTION_RULE_OOCR_J1_L10_SAT  >  RH_RSAP_DISTRIBUTION_RULE_OOCR_J1_L10_SAT
		  content: JSON_TEXT  >  JSON_TEXT 

