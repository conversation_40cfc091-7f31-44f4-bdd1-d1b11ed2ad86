Data vault pipeline developer cheat sheet 
rendered from  rsap_hld1_j1.dvpi

pipeline name:  rsap_hld1_j1

------------------------------------------------------
record source:  sap.hld1

Source fields:
       COMPANY    Varchar(50)
       HLDCODE    VARCHAR(20)
       JSON_TEXT  VARCHAR


------------------------------------------------------
Table List:
stage_table.rvlt_sap.rsap_hld1_j1_stage
table.rvlt_sap.rsap_feiertage_hld1_hub
table.rvlt_sap.rsap_feiertage_hld1_j1_l10_msat

------------------------------------------------------
stage table:  stage_rvlt.rsap_hld1_j1_stage
Field to Stage mapping:
	--business keys,
		COMPANY    >  COMPANY,
		HLDCODE    >  HLDCODE,

	--content,
		JSON_TEXT  >  JSON_TEXT

------------------------------------------------------
Hash value composition

HK_RSAP_FEIERTAGE_HLD1 (key)
		COMPANY 
		HLDCODE 

GH_RSAP_FEIERTAGE_HLD1_J1_L10_MSAT (diff_hash)
		JSON_TEXT 


------------------------------------------------------
Table load method
(STAGE >  VAULT)

rsap_feiertage_hld1_hub (/) can be loaded by convention
		  key: HK_RSAP_FEIERTAGE_HLD1  >  HK_RSAP_FEIERTAGE_HLD1
		  business_key: COMPANY  >  COMPANY 
		  business_key: HLDCODE  >  HLDCODE 

rsap_feiertage_hld1_j1_l10_msat (/) can be loaded by convention
		  parent_key: HK_RSAP_FEIERTAGE_HLD1  >  HK_RSAP_FEIERTAGE_HLD1
		  diff_hash: GH_RSAP_FEIERTAGE_HLD1_J1_L10_MSAT  >  GH_RSAP_FEIERTAGE_HLD1_J1_L10_MSAT
		  content: JSON_TEXT  >  JSON_TEXT 

