{"dvdp_compiler": "dvpdc reference compiler,  release 0.6.2", "dvpi_version": "0.6.2", "compile_timestamp": "2024-11-07 08:04:57", "dvpd_version": "0.6.2", "pipeline_name": "rbbe_order_orderitem_p1", "dvpd_filename": "rbbe_order_orderitem_p1.dvpd.json", "tables": [{"table_name": "rbbe_order_item_hub", "table_stereotype": "hub", "schema_name": "rvlt_billbee", "storage_component": "", "columns": [{"column_name": "MD_INSERTED_AT", "is_nullable": false, "column_class": "meta_load_date", "column_type": "TIMESTAMP"}, {"column_name": "MD_RUN_ID", "is_nullable": false, "column_class": "meta_load_process_id", "column_type": "INT"}, {"column_name": "MD_RECORD_SOURCE", "is_nullable": false, "column_class": "meta_record_source", "column_type": "VARCHAR(255)"}, {"column_name": "HK_RBBE_ORDER_ITEM", "is_nullable": false, "column_class": "key", "column_type": "CHAR(28)"}, {"column_name": "BILLBEEID", "is_nullable": true, "column_class": "business_key", "column_type": "NUMBER(36,0)", "prio_for_column_position": 50000}]}, {"table_name": "rbbe_order_item_order_p1_l10_sat", "table_stereotype": "sat", "schema_name": "rvlt_billbee", "storage_component": "", "has_deletion_flag": true, "is_effectivity_sat": false, "is_enddated": true, "is_multiactive": false, "compare_criteria": "key+current", "uses_diff_hash": true, "columns": [{"column_name": "MD_INSERTED_AT", "is_nullable": false, "column_class": "meta_load_date", "column_type": "TIMESTAMP"}, {"column_name": "MD_RUN_ID", "is_nullable": false, "column_class": "meta_load_process_id", "column_type": "INT"}, {"column_name": "MD_RECORD_SOURCE", "is_nullable": false, "column_class": "meta_record_source", "column_type": "VARCHAR(255)"}, {"column_name": "MD_IS_DELETED", "is_nullable": false, "column_class": "meta_deletion_flag", "column_type": "BOOLEAN"}, {"column_name": "MD_VALID_BEFORE", "is_nullable": false, "column_class": "meta_load_enddate", "column_type": "TIMESTAMP"}, {"column_name": "HK_RBBE_ORDER_ITEM", "is_nullable": false, "column_class": "parent_key", "column_type": "CHAR(28)", "parent_key_column_name": "HK_RBBE_ORDER_ITEM", "parent_table_name": "rbbe_order_item_hub"}, {"column_name": "RH_RBBE_ORDER_ITEM_ORDER_P1_L10_SAT", "is_nullable": false, "column_class": "diff_hash", "column_type": "CHAR(28)"}, {"column_name": "QUANTITY", "is_nullable": true, "column_class": "content", "column_type": "NUMBER(20,2)", "exclude_from_change_detection": false, "prio_for_column_position": 50000}, {"column_name": "TOTALPRICE", "is_nullable": true, "column_class": "content", "column_type": "NUMBER(20,2)", "exclude_from_change_detection": false, "prio_for_column_position": 50000}, {"column_name": "TAXAMOUNT", "is_nullable": true, "column_class": "content", "column_type": "NUMBER(20,2)", "exclude_from_change_detection": false, "prio_for_column_position": 50000}, {"column_name": "TAXINDEX", "is_nullable": true, "column_class": "content", "column_type": "NUMBER(20,2)", "exclude_from_change_detection": false, "prio_for_column_position": 50000}, {"column_name": "DISCOUNT", "is_nullable": true, "column_class": "content", "column_type": "NUMBER(20,2)", "exclude_from_change_detection": false, "prio_for_column_position": 50000}, {"column_name": "UNREBATEDTOTALPRICE", "is_nullable": true, "column_class": "content", "column_type": "NUMBER(20,2)", "exclude_from_change_detection": false, "prio_for_column_position": 50000}, {"column_name": "INVOICESKU", "is_nullable": true, "column_class": "content", "column_type": "VARCHAR(200)", "exclude_from_change_detection": false, "prio_for_column_position": 50000}, {"column_name": "PRODUCT_TITLE", "is_nullable": true, "column_class": "content", "column_type": "VARCHAR(1000)", "exclude_from_change_detection": false, "prio_for_column_position": 50000}, {"column_name": "PRODUCT_SKU", "is_nullable": true, "column_class": "content", "column_type": "VARCHAR(200)", "exclude_from_change_detection": false, "prio_for_column_position": 50000}, {"column_name": "PRODUCT_EAN", "is_nullable": true, "column_class": "content", "column_type": "VARCHAR(200)", "exclude_from_change_detection": false, "prio_for_column_position": 50000}, {"column_name": "PRODUCT_COUNTRYOFORIGIN", "is_nullable": true, "column_class": "content", "column_type": "VARCHAR(200)", "exclude_from_change_detection": false, "prio_for_column_position": 50000}, {"column_name": "PRODUCT_TARICCODE", "is_nullable": true, "column_class": "content", "column_type": "VARCHAR(200)", "exclude_from_change_detection": false, "prio_for_column_position": 50000}, {"column_name": "LASTMODIFIEDAT", "is_nullable": true, "column_class": "content_untracked", "column_type": "TIMESTAMP", "exclude_from_change_detection": "true", "prio_for_column_position": 50000}]}, {"table_name": "rbbe_order_hub", "table_stereotype": "hub", "schema_name": "rvlt_billbee", "storage_component": "", "columns": [{"column_name": "MD_INSERTED_AT", "is_nullable": false, "column_class": "meta_load_date", "column_type": "TIMESTAMP"}, {"column_name": "MD_RUN_ID", "is_nullable": false, "column_class": "meta_load_process_id", "column_type": "INT"}, {"column_name": "MD_RECORD_SOURCE", "is_nullable": false, "column_class": "meta_record_source", "column_type": "VARCHAR(255)"}, {"column_name": "HK_RBBE_ORDER", "is_nullable": false, "column_class": "key", "column_type": "CHAR(28)"}, {"column_name": "BILLBEEORDERID", "is_nullable": true, "column_class": "business_key", "column_type": "NUMBER(36,0)", "prio_for_column_position": 50000}]}, {"table_name": "rbbe_order_item_order_lnk", "table_stereotype": "lnk", "schema_name": "rvlt_billbee", "storage_component": "", "columns": [{"column_name": "MD_INSERTED_AT", "is_nullable": false, "column_class": "meta_load_date", "column_type": "TIMESTAMP"}, {"column_name": "MD_RUN_ID", "is_nullable": false, "column_class": "meta_load_process_id", "column_type": "INT"}, {"column_name": "MD_RECORD_SOURCE", "is_nullable": false, "column_class": "meta_record_source", "column_type": "VARCHAR(255)"}, {"column_name": "HK_RBBE_ORDER_ITEM", "is_nullable": false, "column_class": "parent_key", "column_type": "CHAR(28)", "parent_key_column_name": "HK_RBBE_ORDER_ITEM", "parent_table_name": "rbbe_order_item_hub"}, {"column_name": "HK_RBBE_ORDER", "is_nullable": false, "column_class": "parent_key", "column_type": "CHAR(28)", "parent_key_column_name": "HK_RBBE_ORDER", "parent_table_name": "rbbe_order_hub"}, {"column_name": "LK_RBBE_ORDER_ITEM_ORDER", "is_nullable": false, "column_class": "key", "column_type": "CHAR(28)"}]}, {"table_name": "rbbe_order_item_order_esat", "table_stereotype": "sat", "schema_name": "rvlt_billbee", "storage_component": "", "has_deletion_flag": true, "is_effectivity_sat": true, "is_enddated": true, "is_multiactive": false, "compare_criteria": "key+current", "uses_diff_hash": false, "driving_keys": ["HK_RBBE_ORDER_ITEM"], "columns": [{"column_name": "MD_INSERTED_AT", "is_nullable": false, "column_class": "meta_load_date", "column_type": "TIMESTAMP"}, {"column_name": "MD_RUN_ID", "is_nullable": false, "column_class": "meta_load_process_id", "column_type": "INT"}, {"column_name": "MD_RECORD_SOURCE", "is_nullable": false, "column_class": "meta_record_source", "column_type": "VARCHAR(255)"}, {"column_name": "MD_IS_DELETED", "is_nullable": false, "column_class": "meta_deletion_flag", "column_type": "BOOLEAN"}, {"column_name": "MD_VALID_BEFORE", "is_nullable": false, "column_class": "meta_load_enddate", "column_type": "TIMESTAMP"}, {"column_name": "LK_RBBE_ORDER_ITEM_ORDER", "is_nullable": false, "column_class": "parent_key", "column_type": "CHAR(28)", "parent_key_column_name": "LK_RBBE_ORDER_ITEM_ORDER", "parent_table_name": "rbbe_order_item_order_lnk"}]}, {"table_name": "rbbe_product_hub", "table_stereotype": "hub", "schema_name": "rvlt_billbee", "storage_component": "", "columns": [{"column_name": "MD_INSERTED_AT", "is_nullable": false, "column_class": "meta_load_date", "column_type": "TIMESTAMP"}, {"column_name": "MD_RUN_ID", "is_nullable": false, "column_class": "meta_load_process_id", "column_type": "INT"}, {"column_name": "MD_RECORD_SOURCE", "is_nullable": false, "column_class": "meta_record_source", "column_type": "VARCHAR(255)"}, {"column_name": "HK_RBBE_PRODUCT", "is_nullable": false, "column_class": "key", "column_type": "CHAR(28)"}, {"column_name": "BILLBEEID", "is_nullable": true, "column_class": "business_key", "column_type": "NUMBER(36,0)", "prio_for_column_position": 50000}]}, {"table_name": "rbbe_order_item_product_lnk", "table_stereotype": "lnk", "schema_name": "rvlt_billbee", "storage_component": "", "columns": [{"column_name": "MD_INSERTED_AT", "is_nullable": false, "column_class": "meta_load_date", "column_type": "TIMESTAMP"}, {"column_name": "MD_RUN_ID", "is_nullable": false, "column_class": "meta_load_process_id", "column_type": "INT"}, {"column_name": "MD_RECORD_SOURCE", "is_nullable": false, "column_class": "meta_record_source", "column_type": "VARCHAR(255)"}, {"column_name": "HK_RBBE_ORDER_ITEM", "is_nullable": false, "column_class": "parent_key", "column_type": "CHAR(28)", "parent_key_column_name": "HK_RBBE_ORDER_ITEM", "parent_table_name": "rbbe_order_item_hub"}, {"column_name": "HK_RBBE_PRODUCT", "is_nullable": false, "column_class": "parent_key", "column_type": "CHAR(28)", "parent_key_column_name": "HK_RBBE_PRODUCT", "parent_table_name": "rbbe_product_hub"}, {"column_name": "LK_RBBE_ORDER_ITEM_PRODUCT", "is_nullable": false, "column_class": "key", "column_type": "CHAR(28)"}]}, {"table_name": "rbbe_order_item_product_esat", "table_stereotype": "sat", "schema_name": "rvlt_billbee", "storage_component": "", "has_deletion_flag": true, "is_effectivity_sat": true, "is_enddated": true, "is_multiactive": false, "compare_criteria": "key+current", "uses_diff_hash": false, "driving_keys": ["HK_RBBE_ORDER_ITEM"], "columns": [{"column_name": "MD_INSERTED_AT", "is_nullable": false, "column_class": "meta_load_date", "column_type": "TIMESTAMP"}, {"column_name": "MD_RUN_ID", "is_nullable": false, "column_class": "meta_load_process_id", "column_type": "INT"}, {"column_name": "MD_RECORD_SOURCE", "is_nullable": false, "column_class": "meta_record_source", "column_type": "VARCHAR(255)"}, {"column_name": "MD_IS_DELETED", "is_nullable": false, "column_class": "meta_deletion_flag", "column_type": "BOOLEAN"}, {"column_name": "MD_VALID_BEFORE", "is_nullable": false, "column_class": "meta_load_enddate", "column_type": "TIMESTAMP"}, {"column_name": "LK_RBBE_ORDER_ITEM_PRODUCT", "is_nullable": false, "column_class": "parent_key", "column_type": "CHAR(28)", "parent_key_column_name": "LK_RBBE_ORDER_ITEM_PRODUCT", "parent_table_name": "rbbe_order_item_product_lnk"}]}], "data_extraction": {"fetch_module_name": "billbee api", "parse_module_name": "json_array", "load_module_name": "python_framework", "json_array_path": "$.Data.[*].orderItems"}, "parse_sets": [{"stage_properties": [{"stage_schema": "stage_rvlt", "stage_table_name": "rbbe_order_orderitem_p1_stage", "storage_component": ""}], "record_source_name_expression": "billbee.order.orderItem", "fields": [{"json_path": "<PERSON><PERSON><PERSON><PERSON><PERSON>rId", "json_loop_level": -1, "field_type": "NUMBER(36,0)", "field_position": 1, "needs_encryption": false, "field_name": "BILLBEEORDERID"}, {"json_path": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "field_type": "NUMBER(36,0)", "field_position": 2, "needs_encryption": false, "field_name": "BILLBEEID"}, {"json_path": "Product.Bill<PERSON>Id", "field_type": "NUMBER(36,0)", "field_position": 3, "needs_encryption": false, "field_name": "PRODUCT_BILLBEEID"}, {"json_path": "Quantity", "field_type": "NUMBER(20,2)", "field_position": 4, "needs_encryption": false, "field_name": "QUANTITY"}, {"json_path": "TotalPrice", "field_type": "NUMBER(20,2)", "field_position": 5, "needs_encryption": false, "field_name": "TOTALPRICE"}, {"json_path": "TaxAmount", "field_type": "NUMBER(20,2)", "field_position": 6, "needs_encryption": false, "field_name": "TAXAMOUNT"}, {"json_path": "TaxIndex", "field_type": "NUMBER(20,2)", "field_position": 7, "needs_encryption": false, "field_name": "TAXINDEX"}, {"json_path": "Discount", "field_type": "NUMBER(20,2)", "field_position": 8, "needs_encryption": false, "field_name": "DISCOUNT"}, {"json_path": "UnrebatedTotalPrice", "field_type": "NUMBER(20,2)", "field_position": 9, "needs_encryption": false, "field_name": "UNREBATEDTOTALPRICE"}, {"json_path": "InvoiceSKU", "field_type": "VARCHAR(200)", "field_position": 10, "needs_encryption": false, "field_name": "INVOICESKU"}, {"json_path": "Product.Title", "field_type": "VARCHAR(1000)", "field_position": 11, "needs_encryption": false, "field_name": "PRODUCT_TITLE"}, {"json_path": "Product.SKU", "field_type": "VARCHAR(200)", "field_position": 12, "needs_encryption": false, "field_name": "PRODUCT_SKU"}, {"json_path": "Product.EAN", "field_type": "VARCHAR(200)", "field_position": 13, "needs_encryption": false, "field_name": "PRODUCT_EAN"}, {"json_path": "Product.CountryOfOrigin", "field_type": "VARCHAR(200)", "field_position": 14, "needs_encryption": false, "field_name": "PRODUCT_COUNTRYOFORIGIN"}, {"json_path": "Product.TARICCode", "field_type": "VARCHAR(200)", "field_position": 15, "needs_encryption": false, "field_name": "PRODUCT_TARICCODE"}, {"json_path": "LastModifiedAt", "json_loop_level": -1, "field_type": "TIMESTAMP", "field_position": 16, "needs_encryption": false, "field_name": "LASTMODIFIEDAT"}], "hashes": [{"stage_column_name": "HK_RBBE_ORDER_ITEM", "hash_origin_table": "rbbe_order_item_hub", "column_class": "key", "hash_fields": [{"field_name": "BILLBEEID", "prio_in_key_hash": 0, "field_target_table": "rbbe_order_item_hub", "field_target_column": "BILLBEEID"}], "column_type": "CHAR(28)", "hash_encoding": "BASE64", "hash_function": "sha-1", "hash_concatenation_seperator": "|", "hash_timestamp_format_sqlstyle": "YYYY-MM-DD HH24:MI:SS.US", "hash_null_value_string": "", "model_profile_name": "_default", "hash_name": "KEY_OF_RBBE_ORDER_ITEM_HUB"}, {"stage_column_name": "HK_RBBE_ORDER", "hash_origin_table": "rbbe_order_hub", "column_class": "key", "hash_fields": [{"field_name": "BILLBEEORDERID", "prio_in_key_hash": 0, "field_target_table": "rbbe_order_hub", "field_target_column": "BILLBEEORDERID"}], "column_type": "CHAR(28)", "hash_encoding": "BASE64", "hash_function": "sha-1", "hash_concatenation_seperator": "|", "hash_timestamp_format_sqlstyle": "YYYY-MM-DD HH24:MI:SS.US", "hash_null_value_string": "", "model_profile_name": "_default", "hash_name": "KEY_OF_RBBE_ORDER_HUB"}, {"stage_column_name": "HK_RBBE_PRODUCT", "hash_origin_table": "rbbe_product_hub", "column_class": "key", "hash_fields": [{"field_name": "PRODUCT_BILLBEEID", "prio_in_key_hash": 0, "field_target_table": "rbbe_product_hub", "field_target_column": "BILLBEEID"}], "column_type": "CHAR(28)", "hash_encoding": "BASE64", "hash_function": "sha-1", "hash_concatenation_seperator": "|", "hash_timestamp_format_sqlstyle": "YYYY-MM-DD HH24:MI:SS.US", "hash_null_value_string": "", "model_profile_name": "_default", "hash_name": "KEY_OF_RBBE_PRODUCT_HUB"}, {"stage_column_name": "LK_RBBE_ORDER_ITEM_ORDER", "hash_origin_table": "rbbe_order_item_order_lnk", "column_class": "key", "hash_fields": [{"field_name": "BILLBEEID", "prio_in_key_hash": 0, "field_target_table": "rbbe_order_item_hub", "field_target_column": "BILLBEEID", "parent_declaration_position": 1}, {"field_name": "BILLBEEORDERID", "prio_in_key_hash": 0, "field_target_table": "rbbe_order_hub", "field_target_column": "BILLBEEORDERID", "parent_declaration_position": 2}], "column_type": "CHAR(28)", "hash_encoding": "BASE64", "hash_function": "sha-1", "hash_concatenation_seperator": "|", "hash_timestamp_format_sqlstyle": "YYYY-MM-DD HH24:MI:SS.US", "hash_null_value_string": "", "model_profile_name": "_default", "hash_name": "KEY_OF_RBBE_ORDER_ITEM_ORDER_LNK"}, {"stage_column_name": "LK_RBBE_ORDER_ITEM_PRODUCT", "hash_origin_table": "rbbe_order_item_product_lnk", "column_class": "key", "hash_fields": [{"field_name": "BILLBEEID", "prio_in_key_hash": 0, "field_target_table": "rbbe_order_item_hub", "field_target_column": "BILLBEEID", "parent_declaration_position": 1}, {"field_name": "PRODUCT_BILLBEEID", "prio_in_key_hash": 0, "field_target_table": "rbbe_product_hub", "field_target_column": "BILLBEEID", "parent_declaration_position": 2}], "column_type": "CHAR(28)", "hash_encoding": "BASE64", "hash_function": "sha-1", "hash_concatenation_seperator": "|", "hash_timestamp_format_sqlstyle": "YYYY-MM-DD HH24:MI:SS.US", "hash_null_value_string": "", "model_profile_name": "_default", "hash_name": "KEY_OF_RBBE_ORDER_ITEM_PRODUCT_LNK"}, {"stage_column_name": "RH_RBBE_ORDER_ITEM_ORDER_P1_L10_SAT", "hash_origin_table": "rbbe_order_item_order_p1_l10_sat", "multi_row_content": false, "column_class": "diff_hash", "hash_fields": [{"field_name": "QUANTITY", "prio_in_diff_hash": 0, "prio_for_row_order": 50000, "field_target_table": "rbbe_order_item_order_p1_l10_sat", "field_target_column": "QUANTITY"}, {"field_name": "TOTALPRICE", "prio_in_diff_hash": 0, "prio_for_row_order": 50000, "field_target_table": "rbbe_order_item_order_p1_l10_sat", "field_target_column": "TOTALPRICE"}, {"field_name": "TAXAMOUNT", "prio_in_diff_hash": 0, "prio_for_row_order": 50000, "field_target_table": "rbbe_order_item_order_p1_l10_sat", "field_target_column": "TAXAMOUNT"}, {"field_name": "TAXINDEX", "prio_in_diff_hash": 0, "prio_for_row_order": 50000, "field_target_table": "rbbe_order_item_order_p1_l10_sat", "field_target_column": "TAXINDEX"}, {"field_name": "DISCOUNT", "prio_in_diff_hash": 0, "prio_for_row_order": 50000, "field_target_table": "rbbe_order_item_order_p1_l10_sat", "field_target_column": "DISCOUNT"}, {"field_name": "UNREBATEDTOTALPRICE", "prio_in_diff_hash": 0, "prio_for_row_order": 50000, "field_target_table": "rbbe_order_item_order_p1_l10_sat", "field_target_column": "UNREBATEDTOTALPRICE"}, {"field_name": "INVOICESKU", "prio_in_diff_hash": 0, "prio_for_row_order": 50000, "field_target_table": "rbbe_order_item_order_p1_l10_sat", "field_target_column": "INVOICESKU"}, {"field_name": "PRODUCT_TITLE", "prio_in_diff_hash": 0, "prio_for_row_order": 50000, "field_target_table": "rbbe_order_item_order_p1_l10_sat", "field_target_column": "PRODUCT_TITLE"}, {"field_name": "PRODUCT_SKU", "prio_in_diff_hash": 0, "prio_for_row_order": 50000, "field_target_table": "rbbe_order_item_order_p1_l10_sat", "field_target_column": "PRODUCT_SKU"}, {"field_name": "PRODUCT_EAN", "prio_in_diff_hash": 0, "prio_for_row_order": 50000, "field_target_table": "rbbe_order_item_order_p1_l10_sat", "field_target_column": "PRODUCT_EAN"}, {"field_name": "PRODUCT_COUNTRYOFORIGIN", "prio_in_diff_hash": 0, "prio_for_row_order": 50000, "field_target_table": "rbbe_order_item_order_p1_l10_sat", "field_target_column": "PRODUCT_COUNTRYOFORIGIN"}, {"field_name": "PRODUCT_TARICCODE", "prio_in_diff_hash": 0, "prio_for_row_order": 50000, "field_target_table": "rbbe_order_item_order_p1_l10_sat", "field_target_column": "PRODUCT_TARICCODE"}], "column_type": "CHAR(28)", "hash_encoding": "BASE64", "hash_function": "sha-1", "hash_concatenation_seperator": "|", "hash_timestamp_format_sqlstyle": "YYYY-MM-DD HH24:MI:SS.US", "hash_null_value_string": "", "model_profile_name": "_default", "related_key_hash": "KEY_OF_RBBE_ORDER_ITEM_HUB", "hash_name": "DIFF_OF_RBBE_ORDER_ITEM_ORDER_P1_L10_SAT"}], "load_operations": [{"table_name": "rbbe_order_item_hub", "relation_name": "/", "operation_origin": "field mapping relation", "hash_mappings": [{"hash_class": "key", "column_name": "HK_RBBE_ORDER_ITEM", "is_nullable": false, "hash_name": "KEY_OF_RBBE_ORDER_ITEM_HUB", "stage_column_name": "HK_RBBE_ORDER_ITEM"}], "data_mapping": [{"column_name": "BILLBEEID", "field_name": "BILLBEEID", "column_class": "business_key", "is_nullable": true, "stage_column_name": "BILLBEEID"}]}, {"table_name": "rbbe_order_item_order_p1_l10_sat", "relation_name": "/", "operation_origin": "field mapping relation", "hash_mappings": [{"hash_class": "parent_key", "column_name": "HK_RBBE_ORDER_ITEM", "is_nullable": false, "hash_name": "KEY_OF_RBBE_ORDER_ITEM_HUB", "stage_column_name": "HK_RBBE_ORDER_ITEM"}, {"hash_class": "diff_hash", "column_name": "RH_RBBE_ORDER_ITEM_ORDER_P1_L10_SAT", "is_nullable": false, "hash_name": "DIFF_OF_RBBE_ORDER_ITEM_ORDER_P1_L10_SAT", "stage_column_name": "RH_RBBE_ORDER_ITEM_ORDER_P1_L10_SAT"}], "data_mapping": [{"column_name": "QUANTITY", "field_name": "QUANTITY", "column_class": "content", "is_nullable": true, "stage_column_name": "QUANTITY"}, {"column_name": "TOTALPRICE", "field_name": "TOTALPRICE", "column_class": "content", "is_nullable": true, "stage_column_name": "TOTALPRICE"}, {"column_name": "TAXAMOUNT", "field_name": "TAXAMOUNT", "column_class": "content", "is_nullable": true, "stage_column_name": "TAXAMOUNT"}, {"column_name": "TAXINDEX", "field_name": "TAXINDEX", "column_class": "content", "is_nullable": true, "stage_column_name": "TAXINDEX"}, {"column_name": "DISCOUNT", "field_name": "DISCOUNT", "column_class": "content", "is_nullable": true, "stage_column_name": "DISCOUNT"}, {"column_name": "UNREBATEDTOTALPRICE", "field_name": "UNREBATEDTOTALPRICE", "column_class": "content", "is_nullable": true, "stage_column_name": "UNREBATEDTOTALPRICE"}, {"column_name": "INVOICESKU", "field_name": "INVOICESKU", "column_class": "content", "is_nullable": true, "stage_column_name": "INVOICESKU"}, {"column_name": "PRODUCT_TITLE", "field_name": "PRODUCT_TITLE", "column_class": "content", "is_nullable": true, "stage_column_name": "PRODUCT_TITLE"}, {"column_name": "PRODUCT_SKU", "field_name": "PRODUCT_SKU", "column_class": "content", "is_nullable": true, "stage_column_name": "PRODUCT_SKU"}, {"column_name": "PRODUCT_EAN", "field_name": "PRODUCT_EAN", "column_class": "content", "is_nullable": true, "stage_column_name": "PRODUCT_EAN"}, {"column_name": "PRODUCT_COUNTRYOFORIGIN", "field_name": "PRODUCT_COUNTRYOFORIGIN", "column_class": "content", "is_nullable": true, "stage_column_name": "PRODUCT_COUNTRYOFORIGIN"}, {"column_name": "PRODUCT_TARICCODE", "field_name": "PRODUCT_TARICCODE", "column_class": "content", "is_nullable": true, "stage_column_name": "PRODUCT_TARICCODE"}, {"column_name": "LASTMODIFIEDAT", "field_name": "LASTMODIFIEDAT", "column_class": "content_untracked", "is_nullable": true, "stage_column_name": "LASTMODIFIEDAT"}]}, {"table_name": "rbbe_order_hub", "relation_name": "/", "operation_origin": "field mapping relation", "hash_mappings": [{"hash_class": "key", "column_name": "HK_RBBE_ORDER", "is_nullable": false, "hash_name": "KEY_OF_RBBE_ORDER_HUB", "stage_column_name": "HK_RBBE_ORDER"}], "data_mapping": [{"column_name": "BILLBEEORDERID", "field_name": "BILLBEEORDERID", "column_class": "business_key", "is_nullable": true, "stage_column_name": "BILLBEEORDERID"}]}, {"table_name": "rbbe_order_item_order_lnk", "relation_name": "/", "operation_origin": "induced from satellite rbbe_order_item_order_esat", "hash_mappings": [{"hash_class": "parent_key_1", "column_name": "HK_RBBE_ORDER_ITEM", "is_nullable": false, "hash_name": "KEY_OF_RBBE_ORDER_ITEM_HUB", "stage_column_name": "HK_RBBE_ORDER_ITEM"}, {"hash_class": "parent_key_2", "column_name": "HK_RBBE_ORDER", "is_nullable": false, "hash_name": "KEY_OF_RBBE_ORDER_HUB", "stage_column_name": "HK_RBBE_ORDER"}, {"hash_class": "key", "column_name": "LK_RBBE_ORDER_ITEM_ORDER", "is_nullable": false, "hash_name": "KEY_OF_RBBE_ORDER_ITEM_ORDER_LNK", "stage_column_name": "LK_RBBE_ORDER_ITEM_ORDER"}]}, {"table_name": "rbbe_order_item_order_esat", "relation_name": "/", "operation_origin": "explicitly tracked relation", "hash_mappings": [{"hash_class": "parent_key", "column_name": "LK_RBBE_ORDER_ITEM_ORDER", "is_nullable": false, "hash_name": "KEY_OF_RBBE_ORDER_ITEM_ORDER_LNK", "stage_column_name": "LK_RBBE_ORDER_ITEM_ORDER"}]}, {"table_name": "rbbe_product_hub", "relation_name": "/", "operation_origin": "field mapping relation", "hash_mappings": [{"hash_class": "key", "column_name": "HK_RBBE_PRODUCT", "is_nullable": false, "hash_name": "KEY_OF_RBBE_PRODUCT_HUB", "stage_column_name": "HK_RBBE_PRODUCT"}], "data_mapping": [{"column_name": "BILLBEEID", "field_name": "PRODUCT_BILLBEEID", "column_class": "business_key", "is_nullable": true, "stage_column_name": "PRODUCT_BILLBEEID"}]}, {"table_name": "rbbe_order_item_product_lnk", "relation_name": "/", "operation_origin": "implicit unnamed relation, since all parents are universal", "hash_mappings": [{"hash_class": "parent_key_1", "column_name": "HK_RBBE_ORDER_ITEM", "is_nullable": false, "hash_name": "KEY_OF_RBBE_ORDER_ITEM_HUB", "stage_column_name": "HK_RBBE_ORDER_ITEM"}, {"hash_class": "parent_key_2", "column_name": "HK_RBBE_PRODUCT", "is_nullable": false, "hash_name": "KEY_OF_RBBE_PRODUCT_HUB", "stage_column_name": "HK_RBBE_PRODUCT"}, {"hash_class": "key", "column_name": "LK_RBBE_ORDER_ITEM_PRODUCT", "is_nullable": false, "hash_name": "KEY_OF_RBBE_ORDER_ITEM_PRODUCT_LNK", "stage_column_name": "LK_RBBE_ORDER_ITEM_PRODUCT"}]}, {"table_name": "rbbe_order_item_product_esat", "relation_name": "/", "operation_origin": "following parent operation list", "hash_mappings": [{"hash_class": "parent_key", "column_name": "LK_RBBE_ORDER_ITEM_PRODUCT", "is_nullable": false, "hash_name": "KEY_OF_RBBE_ORDER_ITEM_PRODUCT_LNK", "stage_column_name": "LK_RBBE_ORDER_ITEM_PRODUCT"}]}], "stage_columns": [{"stage_column_name": "MD_INSERTED_AT", "is_nullable": false, "stage_column_class": "meta_load_date", "column_type": "TIMESTAMP"}, {"stage_column_name": "MD_RUN_ID", "is_nullable": false, "stage_column_class": "meta_load_process_id", "column_type": "INT"}, {"stage_column_name": "MD_RECORD_SOURCE", "is_nullable": false, "stage_column_class": "meta_record_source", "column_type": "VARCHAR(255)"}, {"stage_column_name": "MD_IS_DELETED", "is_nullable": false, "stage_column_class": "meta_deletion_flag", "column_type": "BOOLEAN"}, {"stage_column_name": "HK_RBBE_ORDER_ITEM", "stage_column_class": "hash", "is_nullable": false, "hash_name": "KEY_OF_RBBE_ORDER_ITEM_HUB", "column_type": "CHAR(28)"}, {"stage_column_name": "HK_RBBE_ORDER", "stage_column_class": "hash", "is_nullable": false, "hash_name": "KEY_OF_RBBE_ORDER_HUB", "column_type": "CHAR(28)"}, {"stage_column_name": "HK_RBBE_PRODUCT", "stage_column_class": "hash", "is_nullable": false, "hash_name": "KEY_OF_RBBE_PRODUCT_HUB", "column_type": "CHAR(28)"}, {"stage_column_name": "LK_RBBE_ORDER_ITEM_ORDER", "stage_column_class": "hash", "is_nullable": false, "hash_name": "KEY_OF_RBBE_ORDER_ITEM_ORDER_LNK", "column_type": "CHAR(28)"}, {"stage_column_name": "LK_RBBE_ORDER_ITEM_PRODUCT", "stage_column_class": "hash", "is_nullable": false, "hash_name": "KEY_OF_RBBE_ORDER_ITEM_PRODUCT_LNK", "column_type": "CHAR(28)"}, {"stage_column_name": "RH_RBBE_ORDER_ITEM_ORDER_P1_L10_SAT", "stage_column_class": "hash", "is_nullable": false, "hash_name": "DIFF_OF_RBBE_ORDER_ITEM_ORDER_P1_L10_SAT", "column_type": "CHAR(28)"}, {"stage_column_name": "BILLBEEORDERID", "stage_column_class": "data", "field_name": "BILLBEEORDERID", "is_nullable": true, "column_type": "NUMBER(36,0)", "column_classes": ["business_key"]}, {"stage_column_name": "BILLBEEID", "stage_column_class": "data", "field_name": "BILLBEEID", "is_nullable": true, "column_type": "NUMBER(36,0)", "column_classes": ["business_key"]}, {"stage_column_name": "PRODUCT_BILLBEEID", "stage_column_class": "data", "field_name": "PRODUCT_BILLBEEID", "is_nullable": true, "column_type": "NUMBER(36,0)", "column_classes": ["business_key"]}, {"stage_column_name": "QUANTITY", "stage_column_class": "data", "field_name": "QUANTITY", "is_nullable": true, "column_type": "NUMBER(20,2)", "column_classes": ["content"]}, {"stage_column_name": "TOTALPRICE", "stage_column_class": "data", "field_name": "TOTALPRICE", "is_nullable": true, "column_type": "NUMBER(20,2)", "column_classes": ["content"]}, {"stage_column_name": "TAXAMOUNT", "stage_column_class": "data", "field_name": "TAXAMOUNT", "is_nullable": true, "column_type": "NUMBER(20,2)", "column_classes": ["content"]}, {"stage_column_name": "TAXINDEX", "stage_column_class": "data", "field_name": "TAXINDEX", "is_nullable": true, "column_type": "NUMBER(20,2)", "column_classes": ["content"]}, {"stage_column_name": "DISCOUNT", "stage_column_class": "data", "field_name": "DISCOUNT", "is_nullable": true, "column_type": "NUMBER(20,2)", "column_classes": ["content"]}, {"stage_column_name": "UNREBATEDTOTALPRICE", "stage_column_class": "data", "field_name": "UNREBATEDTOTALPRICE", "is_nullable": true, "column_type": "NUMBER(20,2)", "column_classes": ["content"]}, {"stage_column_name": "INVOICESKU", "stage_column_class": "data", "field_name": "INVOICESKU", "is_nullable": true, "column_type": "VARCHAR(200)", "column_classes": ["content"]}, {"stage_column_name": "PRODUCT_TITLE", "stage_column_class": "data", "field_name": "PRODUCT_TITLE", "is_nullable": true, "column_type": "VARCHAR(1000)", "column_classes": ["content"]}, {"stage_column_name": "PRODUCT_SKU", "stage_column_class": "data", "field_name": "PRODUCT_SKU", "is_nullable": true, "column_type": "VARCHAR(200)", "column_classes": ["content"]}, {"stage_column_name": "PRODUCT_EAN", "stage_column_class": "data", "field_name": "PRODUCT_EAN", "is_nullable": true, "column_type": "VARCHAR(200)", "column_classes": ["content"]}, {"stage_column_name": "PRODUCT_COUNTRYOFORIGIN", "stage_column_class": "data", "field_name": "PRODUCT_COUNTRYOFORIGIN", "is_nullable": true, "column_type": "VARCHAR(200)", "column_classes": ["content"]}, {"stage_column_name": "PRODUCT_TARICCODE", "stage_column_class": "data", "field_name": "PRODUCT_TARICCODE", "is_nullable": true, "column_type": "VARCHAR(200)", "column_classes": ["content"]}, {"stage_column_name": "LASTMODIFIEDAT", "stage_column_class": "data", "field_name": "LASTMODIFIEDAT", "is_nullable": true, "column_type": "TIMESTAMP", "column_classes": ["content_untracked"]}]}]}