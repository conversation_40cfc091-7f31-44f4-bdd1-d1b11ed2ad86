# SUND GmbH & Co. KG 数据仓库完整实施指南
## 基于Data Vault 2.0和DVPD框架的企业级解决方案

### 版本信息
- **文档版本**: 1.0
- **项目**: SUND数据仓库PoC
- **DVPD版本**: 0.6.2
- **创建日期**: 2024年
- **维护方**: cimt objects AG

---

## 目录

1. [项目概述](#项目概述)
2. [架构设计](#架构设计)
3. [数据源系统](#数据源系统)
4. [Data Vault建模规范](#data-vault建模规范)
5. [业务转换规则](#业务转换规则)
6. [基础设施部署](#基础设施部署)
7. [开发流程](#开发流程)
8. [运维监控](#运维监控)
9. [安全与合规](#安全与合规)
10. [最佳实践](#最佳实践)

---

## 项目概述

### 业务背景
SUND GmbH & Co. KG作为德国领先的消费品公司，需要构建现代化的数据仓库平台来支持：
- 多源系统数据整合（SAP、BillBee、PIM等）
- 实时业务分析和报告
- GDPR合规的数据管理
- 敏捷的业务需求响应

### 技术选型
- **建模方法**: Data Vault 2.0
- **开发框架**: DVPD (Data Vault Pipeline Definition) 0.6.2
- **云平台**: Microsoft Azure
- **数据库**: Azure SQL Database / Snowflake
- **容器化**: Docker + Azure Container Apps
- **版本控制**: GitHub

### 项目目标
1. **统一数据视图**: 建立企业级的单一数据源
2. **实时数据处理**: 支持近实时的数据更新和分析
3. **可扩展架构**: 支持未来业务增长和新数据源接入
4. **合规性保证**: 满足GDPR等法规要求
5. **成本优化**: 采用云原生架构降低运维成本

---

## 架构设计

### 整体架构
```
┌─────────────────────────────────────────────────────────────────┐
│                        数据源层 (Source Layer)                    │
├─────────────────┬─────────────────┬─────────────────┬─────────────┤
│   SAP Systems   │    BillBee      │      PIM        │   Manual    │
│   (OCRD,ORIN,   │   (Orders,      │   (Products,    │    Data     │
│   RIN1,RIN2,    │   Products)     │    GTIN)        │  (Excel)    │
│   OITM,etc.)    │                 │                 │             │
└─────────────────┴─────────────────┴─────────────────┴─────────────┘
                                    │
                                    ▼
┌─────────────────────────────────────────────────────────────────┐
│                      暂存层 (Staging Layer)                      │
│                     stage_rvlt / stage_bvlt                     │
└─────────────────────────────────────────────────────────────────┘
                                    │
                                    ▼
┌─────────────────────────────────────────────────────────────────┐
│                   原始保险库 (Raw Vault Layer)                    │
├─────────────────┬─────────────────┬─────────────────┬─────────────┤
│   rvlt_sap      │  rvlt_billbee   │   rvlt_pim      │rvlt_general │
│   (SAP数据)      │  (BillBee数据)   │   (PIM数据)      │ (通用数据)   │
└─────────────────┴─────────────────┴─────────────────┴─────────────┘
                                    │
                                    ▼
┌─────────────────────────────────────────────────────────────────┐
│                  业务保险库 (Business Vault Layer)                │
│              统一业务规则和数据质量处理                            │
└─────────────────────────────────────────────────────────────────┘
                                    │
                                    ▼
┌─────────────────────────────────────────────────────────────────┐
│                    数据集市 (Data Mart Layer)                    │
├─────────────────┬─────────────────┬─────────────────┬─────────────┤
│ mart_marketing  │mart_advertising │mart_balanced_   │   其他      │
│                 │                 │   scorecard     │   集市      │
└─────────────────┴─────────────────┴─────────────────┴─────────────┘
```

### 技术架构
```
┌─────────────────────────────────────────────────────────────────┐
│                        开发与部署层                              │
├─────────────────┬─────────────────┬─────────────────┬─────────────┤
│     GitHub      │  Azure DevOps   │  Docker Build   │  Container  │
│   (代码管理)     │   (CI/CD)       │   (镜像构建)     │   Registry  │
└─────────────────┴─────────────────┴─────────────────┴─────────────┘
                                    │
                                    ▼
┌─────────────────────────────────────────────────────────────────┐
│                        运行时环境                                │
├─────────────────┬─────────────────┬─────────────────┬─────────────┤
│ Container Apps  │  Azure Storage  │   Key Vault     │  Monitoring │
│   (作业执行)     │   (数据存储)     │   (密钥管理)     │   (监控)    │
└─────────────────┴─────────────────┴─────────────────┴─────────────┘
```

### 数据流向
1. **数据提取**: 从各源系统提取数据到Azure Blob Storage
2. **数据暂存**: 通过DVPD框架处理到暂存表
3. **Raw Vault加载**: 按Data Vault 2.0规范加载到Raw Vault
4. **Business Vault转换**: 应用业务规则进行数据整合和清洗
5. **Data Mart构建**: 为特定业务场景构建优化的数据集市

---

## 数据源系统

### SAP Business One系统

#### 核心表结构
| 表名 | 描述 | 关键字段 | 更新频率 |
|------|------|----------|----------|
| OCRD | 业务伙伴主数据 | Company, CardCode, CardName | 实时 |
| OCRG | 业务伙伴组 | Company, GroupCode, GroupName | 日更新 |
| ORIN | 贷项凭证头 | Company, DocEntry, DocTotal | 实时 |
| RIN1 | 贷项凭证行 | Company, DocEntry, LineNum | 实时 |
| RIN2 | 贷项凭证行详情 | Company, DocEntry, LineNum, GroupNum | 实时 |
| OITM | 物料主数据 | Company, ItemCode, ItemName, EAN | 日更新 |
| OITB | 物料组 | Company, ItmsGrpCod, ItmsGrpNam | 周更新 |
| ORTT | 汇率 | Company, RateDate, Currency, Rate | 日更新 |
| OOCR | 分销规则 | Company, OcrCode, OcrName | 月更新 |

#### 数据提取策略
```python
# SAP数据提取示例配置
sap_extraction_config = {
    "connection": {
        "server": "sap.sund-group.com",
        "port": 6005,
        "database": "SUND_PROD"
    },
    "tables": {
        "OCRD": {
            "extract_mode": "incremental",
            "delta_column": "UpdateDate",
            "batch_size": 1000
        },
        "RIN2": {
            "extract_mode": "full",
            "schedule": "0 2 * * *",  # 每日2点
            "retention_days": 7
        }
    }
}
```

### BillBee电商平台

#### API接口规范
- **基础URL**: https://app.billbee.io/api/v1/
- **认证方式**: API Key + Basic Auth
- **数据格式**: JSON
- **限流**: 1000 requests/hour

#### 核心数据实体
```json
{
  "orders": {
    "endpoint": "/orders",
    "key_fields": ["BillBeeOrderId", "Customer.Id", "Seller.BillbeeShopId"],
    "gdpr_fields": ["InvoiceAddress", "ShippingAddress", "Customer.Name"],
    "retention_policy": "anonymize_after_2_years"
  },
  "products": {
    "endpoint": "/products",
    "key_fields": ["Id", "EAN", "SKU"],
    "update_frequency": "daily"
  }
}
```

#### GDPR合规处理
```python
# GDPR敏感字段处理
gdpr_sensitive_fields = [
    "Customer.Name",
    "InvoiceAddress.Street",
    "InvoiceAddress.City",
    "ShippingAddress.Street",
    "ShippingAddress.City",
    "Payments.TransactionId"
]

# 只保留业务必需的非敏感字段
allowed_fields = [
    "BillBeeOrderId",
    "TotalCost",
    "Currency",
    "State",
    "CreatedAt",
    "InvoiceAddress.CountryISO2"  # 仅保留国家代码
]
```

### PIM产品信息管理

#### 产品-GTIN映射规则
```python
def extract_gtin_mappings(product_data):
    """提取产品GTIN映射关系"""
    mappings = []

    # 规则1: 直接GTIN映射
    if product_data.get('internationalPidType') == 'gtin':
        mappings.append({
            'product_id': product_data['id'],
            'gtin': product_data['internationalPid'],
            'mapping_source': 'internationalPidType'
        })

    # 规则2: UDX产品EAN映射
    for udx_product in product_data.get('udxProducts', []):
        if udx_product['Name'].startswith('EAN_'):
            mappings.append({
                'product_id': product_data['id'],
                'gtin': udx_product['Value'],
                'mapping_source': udx_product['Name']
            })

    return mappings
```

### 手动数据源

#### Excel文件处理规范
```python
# 销售计划数据Excel解析
def parse_sales_plan_excel(file_path):
    """解析销售计划Excel文件"""

    # 1. 定位数据起始行（包含"年"、"月"、"公司"的行）
    header_row = find_header_row(file_path)

    # 2. 提取属性行（header_row之前的行）
    attributes = extract_attributes(file_path, header_row)

    # 3. 反透视化处理
    normalized_data = []
    for row in data_rows:
        for attr_combination in attributes:
            normalized_data.append({
                'year': row['年'],
                'month': row.get('月'),
                'company': row['公司'],
                'plan_value': row[attr_combination['column']],
                'sub_grouping': json.dumps(attr_combination['attributes'])
            })

    return normalized_data
```

---

## Data Vault建模规范

### Schema命名约定

#### Schema分类体系
```
# Raw Vault Schemas
rvlt_sap          # SAP系统数据
rvlt_billbee      # BillBee电商数据
rvlt_pim          # PIM产品数据
rvlt_general      # 通用参考数据
rvlt_manual_data  # 手动数据

# Business Vault Schemas
bvlt_sap          # SAP业务规则处理
bvlt_billbee      # BillBee业务规则处理
bvlt_general      # 通用业务规则

# Data Mart Schemas
mart_marketing    # 营销数据集市
mart_advertising  # 广告数据集市
mart_balanced_scorecard  # 平衡计分卡

# 支撑Schemas
stage_rvlt        # Raw Vault暂存
stage_bvlt        # Business Vault暂存
encryption_keys   # 加密密钥管理
```

#### Schema标记系统
```python
# 5字符标记规则: [层级1字符][Schema缩写3字符]
schema_markers = {
    'rvlt_sap': 'RSAP',      # R(Raw) + SAP
    'rvlt_billbee': 'RBBE',  # R(Raw) + BBE(BillBee)
    'rvlt_pim': 'RPIM',      # R(Raw) + PIM
    'bvlt_sap': 'BSAP',      # B(Business) + SAP
    'mart_marketing': 'MMKT', # M(Mart) + MKT(Marketing)
}
```

### 表命名规范

#### Hub表命名
```sql
-- 模式: <Schema标记>_<业务实体>_HUB
RSAP_BUSINESS_PARTNER_OCRD_HUB     -- SAP业务伙伴
RSAP_CREDIT_MEMO_LINE_RIN1_HUB     -- SAP贷项凭证行
RBBE_ORDER_HUB                     -- BillBee订单
RBBE_PRODUCT_HUB                   -- BillBee产品
RPIM_PRODUCT_HUB                   -- PIM产品
RGNR_GLOBAL_PRODUCT_HUB            -- 全球产品(通用)
```

#### Link表命名
```sql
-- 标准Link: <Schema标记>_<Hub1>_<Hub2>_LNK
RSAP_CREDIT_MEMO_LINE_RIN1_ITEM_OITM_LNK

-- 依赖Link: <Schema标记>_<父Hub>_<子实体>_DLNK
RSAP_CREDIT_MEMO_LINE_RIN1_RIN2_DLNK

-- 跨Schema Link: 包含所有相关Schema标记
BSAP_RSAP_BUSINESS_PARTNER_RBBE_SHOP_LNK
```

#### Satellite表命名
```sql
-- 模式: <Schema标记>_<父表>_<源标识>_<层级>_SAT
RSAP_BUSINESS_PARTNER_OCRD_J1_L10_SAT      -- JSON数据第1组，第10层
RSAP_CREDIT_MEMO_LINE_RIN1_RIN2_J1_L10_SAT -- RIN2详情数据
RBBE_PRODUCT_PRODUCT_J1_L10_SAT            -- 产品基础信息
RBBE_PRODUCT_PRODUCT_J2_L10_SAT            -- 产品图片信息
RBBE_PRODUCT_PRODUCT_J3_L10_SAT            -- 产品销售统计

-- 多活跃Satellite: 后缀_MSAT
RPIM_PRODUCT_RGNR_GLOBAL_PRODUCT_MAPPING_P1_L10_MSAT
```

### 列命名规范

#### 元数据列
```sql
-- 标准元数据列（所有表必需）
MD_INSERTED_AT      TIMESTAMP      NOT NULL  -- 插入时间
MD_RUN_ID           INT            NOT NULL  -- 运行ID
MD_RECORD_SOURCE    VARCHAR(255)   NOT NULL  -- 记录源

-- Satellite特有元数据列
MD_IS_DELETED       BOOLEAN        NOT NULL  -- 删除标记
MD_VALID_BEFORE     TIMESTAMP      NOT NULL  -- 有效期结束时间
```

#### 哈希键列
```sql
-- Hub哈希键: HK_<Hub名称>
HK_RSAP_BUSINESS_PARTNER_OCRD      CHAR(28)  NOT NULL
HK_RSAP_CREDIT_MEMO_LINE_RIN1      CHAR(28)  NOT NULL

-- Link哈希键: LK_<Link名称>
LK_RSAP_CREDIT_MEMO_LINE_RIN1_RIN2 CHAR(28)  NOT NULL

-- 记录哈希: RH_<Satellite名称>
RH_RSAP_BUSINESS_PARTNER_OCRD_J1_L10_SAT CHAR(28) NOT NULL
```

#### 业务键列
```sql
-- 保持源系统原始字段名
COMPANY             VARCHAR(50)    -- 公司代码
CARDCODE            VARCHAR(20)    -- 业务伙伴代码
DOCENTRY            INTEGER        -- 文档编号
LINENUM             INTEGER        -- 行号
BILLBEEORDERID      NUMBER(36,0)   -- BillBee订单ID
```

#### 加密相关列
```sql
-- 加密业务键: 原字段名_EC
CUSTOMER_NUMBER_EC  VARCHAR(100)   -- 加密客户编号

-- 加密索引: EKI_<表名>
EKI_RSAP_BUSINESS_PARTNER_OCRD_J1_L10_SAT INT

-- 业务键哈希: BKH_<键名>
BKH_CUSTOMER        CHAR(28)       -- 未加密业务键哈希
BKH_CUSTOMER_ST     CHAR(28)       -- 加盐哈希（冲突检测）
```

---

## 业务转换规则

### BillBee订单成本分配

#### 成本分配算法
```python
def calculate_cost_distribution(order_data):
    """BillBee订单成本分配算法"""

    # 1. 计算待分配金额
    non_product_items = [item for item in order_data['items']
                        if not item.get('product')]

    zu_verteilender_wert_netto = (
        sum(item['total_price'] - item['tax_amount'] for item in non_product_items) +
        order_data['shipping_cost'] / 1.19
    )

    zu_verteilender_wert_brutto = (
        sum(item['total_price'] for item in non_product_items) +
        order_data['shipping_cost']
    )

    # 2. 计算产品项目总额
    product_items = [item for item in order_data['items']
                    if item.get('product')]
    artikel_total_price_sum = sum(item['total_price'] for item in product_items)

    # 3. 计算分配系数
    if artikel_total_price_sum == 0:
        # 如果总额为0，按数量平均分配
        distribution_factor = 1.0 / len(product_items) if product_items else 0
        distribution_factors = [distribution_factor] * len(product_items)
    else:
        # 按金额比例分配
        distribution_factors = [
            item['total_price'] / artikel_total_price_sum
            for item in product_items
        ]

    # 4. 计算修正后的成本
    corrected_costs = []
    for i, item in enumerate(product_items):
        total_price_netto = item['total_price'] - item['tax_amount']
        distributed_amount = distribution_factors[i] * zu_verteilender_wert_netto

        corrected_costs.append({
            'item_id': item['id'],
            'original_netto': total_price_netto,
            'distributed_amount': distributed_amount,
            'corrected_netto': total_price_netto + distributed_amount,
            'distribution_factor': distribution_factors[i]
        })

    return corrected_costs
```

#### 数据结构
```sql
-- 修正后的订单项目成本表
CREATE TABLE bbbe_order_item_artikelkosten_korrigiert_v1_b10_sat (
    MD_INSERTED_AT                    TIMESTAMP      NOT NULL,
    MD_RUN_ID                         INT            NOT NULL,
    MD_RECORD_SOURCE                  VARCHAR(255)   NOT NULL,
    MD_IS_DELETED                     BOOLEAN        NOT NULL,
    MD_VALID_BEFORE                   TIMESTAMP      NOT NULL,
    HK_RBBE_ORDER_ITEM                CHAR(28)       NOT NULL,
    RH_BBBE_ORDER_ITEM_ARTIKELKOSTEN_KORRIGIERT_V1_B10_SAT CHAR(28) NOT NULL,

    TOTAL_PRICE_NETTO                 NUMERIC(16,2)  NULL,
    ZU_VERTEILENDER_WERT_NETTO        NUMERIC(16,2)  NULL,
    PRODUCT_VERTEILSCHUESSEL          NUMERIC(16,8)  NULL,
    TOTAL_PRICE_NETTO_KORRIGIERT      NUMERIC(16,2)  NULL
);
```

### SAP发票折扣分配

#### 折扣分配规则
```python
def calculate_sap_discount_distribution(invoice_data):
    """SAP发票折扣分配计算"""

    # 1. 计算发票总净额
    total_netto = (
        invoice_data['doc_total'] -          # 总额
        invoice_data['vat_sum'] -            # 税额
        invoice_data['total_expns'] +        # 运费
        invoice_data['disc_sum'] +           # 折扣
        invoice_data['dp_amnt']              # 预付款
    )

    # 2. 处理FIPP_DE特殊情况（负运费作为折扣）
    line_corrections = {}
    if invoice_data['company'] == 'FIPP_DE':
        for line in invoice_data['inv2_lines']:
            if line['expns_code'] in [3, 4]:  # 运费代码3或4
                parent_line = line['doc_entry'] + '_' + str(line['line_num'])
                if parent_line not in line_corrections:
                    line_corrections[parent_line] = 0
                line_corrections[parent_line] += line['line_total']

    # 3. 计算每行的修正成本
    corrected_lines = []
    for line in invoice_data['lines']:
        line_key = str(line['doc_entry']) + '_' + str(line['line_num'])

        # 原始行总额 + FIPP_DE运费修正
        adjusted_line_total = line['line_total'] + line_corrections.get(line_key, 0)

        if total_netto == 0:
            # 数据错误情况，设为0
            corrected_line_total = 0
            discount_portion = 0
            proportion = 0
        else:
            # 计算比例和折扣分配
            proportion = adjusted_line_total / total_netto
            discount_portion = proportion * invoice_data['disc_sum']
            corrected_line_total = adjusted_line_total - discount_portion

        # 取消订单处理（负值）
        if invoice_data.get('canceled') == 'C':
            corrected_line_total = -corrected_line_total

        corrected_lines.append({
            'doc_entry': line['doc_entry'],
            'line_num': line['line_num'],
            'original_line_total': line['line_total'],
            'fipp_adjustment': line_corrections.get(line_key, 0),
            'proportion': proportion,
            'discount_portion': discount_portion,
            'corrected_line_total': corrected_line_total
        })

    return corrected_lines
```

### 产品GTIN关联

#### BillBee产品GTIN默认值算法
```python
def determine_default_gtin(product_id):
    """确定BillBee产品的默认GTIN"""

    # 1. 统计该产品所有订单项目中EAN的使用频率
    ean_frequency = {}

    query = """
    SELECT
        ois.PRODUCT_EAN,
        COUNT(*) as frequency
    FROM rbbe_order_item_order_p1_l10_sat ois
    JOIN rbbe_order_item_hub oih ON ois.hk_rbbe_order_item = oih.hk_rbbe_order_item
    JOIN rbbe_order_item_product_lnk oipl ON oih.hk_rbbe_order_item = oipl.hk_rbbe_order_item
    WHERE oipl.hk_rbbe_product = %s
      AND ois.md_valid_before = '2299-12-30 00:00:00'
      AND ois.PRODUCT_EAN IS NOT NULL
    GROUP BY ois.PRODUCT_EAN
    ORDER BY frequency DESC
    """

    results = execute_query(query, [product_id])

    # 2. 返回使用频率最高的EAN
    if results:
        return results[0]['PRODUCT_EAN']
    else:
        return None

def create_product_gtin_mapping():
    """创建产品GTIN映射"""

    mappings = []

    # 获取所有产品
    products = get_all_billbee_products()

    for product in products:
        # 直接EAN映射
        if product.get('ean'):
            mappings.append({
                'hk_rbbe_product': product['hk_rbbe_product'],
                'gtin': product['ean'],
                'mapping_source': 'direct_ean'
            })
        else:
            # 使用默认GTIN
            default_gtin = determine_default_gtin(product['hk_rbbe_product'])
            if default_gtin:
                mappings.append({
                    'hk_rbbe_product': product['hk_rbbe_product'],
                    'gtin': default_gtin,
                    'mapping_source': 'frequency_based'
                })

    return mappings
```

### 销售计划标准化

#### 日级别分解算法
```python
def normalize_sales_plans_to_daily(plan_data, calendar_data):
    """将销售计划标准化到日级别"""

    daily_plans = []

    for plan_record in plan_data:
        company = plan_record['company']
        year = plan_record['year']
        month = plan_record.get('month')
        plan_value = plan_record['plan_value']
        sub_grouping = plan_record.get('sub_grouping_json', '{}')

        # 解析子分组
        sub_groups = json.loads(sub_grouping) if sub_grouping else {}

        if month:
            # 月度计划：按月工作日分解
            working_days = get_working_days_in_month(company, year, month, calendar_data)
            daily_value = plan_value / working_days if working_days > 0 else 0

            # 生成该月每一天的记录
            for day in range(1, get_days_in_month(year, month) + 1):
                date = datetime(year, month, day)
                is_working_day = is_working_day_for_company(company, date, calendar_data)

                daily_plans.append({
                    'company': company,
                    'plan_date': date,
                    'planned_revenue_eur': daily_value if is_working_day else 0,
                    'sales_area': sub_groups.get('Verkaufsgebiet', '---'),
                    'sales_person': sub_groups.get('Verkäufernummer', '---'),
                    'customer_group': sub_groups.get('Kundengruppe', '---')
                })
        else:
            # 年度计划：按年工作日分解
            working_days = get_working_days_in_year(company, year, calendar_data)
            daily_value = plan_value / working_days if working_days > 0 else 0

            # 生成该年每一天的记录
            for month in range(1, 13):
                for day in range(1, get_days_in_month(year, month) + 1):
                    date = datetime(year, month, day)
                    is_working_day = is_working_day_for_company(company, date, calendar_data)

                    daily_plans.append({
                        'company': company,
                        'plan_date': date,
                        'planned_revenue_eur': daily_value if is_working_day else 0,
                        'sales_area': sub_groups.get('Verkaufsgebiet', '---'),
                        'sales_person': sub_groups.get('Verkäufernummer', '---'),
                        'customer_group': sub_groups.get('Kundengruppe', '---')
                    })

    return daily_plans

def get_working_days_in_month(company, year, month, calendar_data):
    """获取指定公司、年月的工作日数量"""

    working_days = 0
    days_in_month = get_days_in_month(year, month)

    for day in range(1, days_in_month + 1):
        date = datetime(year, month, day)
        if is_working_day_for_company(company, date, calendar_data):
            working_days += 1

    return working_days

def is_working_day_for_company(company, date, calendar_data):
    """判断指定日期对指定公司是否为工作日"""

    # 1. 检查是否为周末
    if date.weekday() >= 5:  # 周六=5, 周日=6
        return False

    # 2. 检查是否为公司节假日
    company_holidays = calendar_data.get(company, [])
    for holiday in company_holidays:
        if holiday['start_date'] <= date <= holiday['end_date']:
            return False

    return True
```

### 中央业务伙伴整合

#### 统一业务伙伴视图
```python
def create_unified_business_partner():
    """创建统一的业务伙伴视图"""

    unified_partners = []

    # 1. SAP业务伙伴
    sap_partners = get_sap_business_partners()
    for partner in sap_partners:
        unified_partners.append({
            'xd_geschaeftspartner_id': f"SAP-{partner['company']}-{partner['cardcode']}",
            'company': partner['company'],
            'sap_cardcode': partner['cardcode'],
            'hk_rsap_business_partner_ocrd': partner['hk_rsap_business_partner_ocrd'],
            'billbee_shop_id': None,
            'hk_rbbe_shop': 'ffffffffffffffffffffffffffff',  # Missing ghost record
            'name': partner['cardname'],
            'gesellschaft': partner['company'],
            'kundengruppe': lookup_customer_group(partner['company'], partner['groupcode']),
            'verkaufsgebiet': partner['u_dim1'],
            'verkaeufernummer': partner['u_dim2'],
            'source_system': 'SAP'
        })

    # 2. BillBee商店
    billbee_shops = get_billbee_shops()
    for shop in billbee_shops:
        unified_partners.append({
            'xd_geschaeftspartner_id': f"SUND_DIGITAL-{shop['billbee_shop_id']}",
            'company': None,
            'sap_cardcode': None,
            'hk_rsap_business_partner_ocrd': 'ffffffffffffffffffffffffffff',  # Missing ghost record
            'billbee_shop_id': shop['billbee_shop_id'],
            'hk_rbbe_shop': shop['hk_rbbe_shop'],
            'name': shop['seller_billbee_shop_name'],
            'gesellschaft': 'SUND_DIGITAL',
            'kundengruppe': '---',
            'verkaufsgebiet': '---',
            'verkaeufernummer': '---',
            'source_system': 'BillBee'
        })

    return unified_partners

def lookup_customer_group(company, group_code):
    """查找客户组名称"""

    query = """
    SELECT groupname
    FROM rsap_card_group_current_ref
    WHERE company = %s AND groupcode = %s
    """

    result = execute_query(query, [company, group_code])
    return result[0]['groupname'] if result else '---'
```

## 基础设施部署

### Azure资源架构

#### 资源组织结构
```
SUND-DWH-PROD (生产环境)
├── rg-sund-dwh-prod              # 主资源组
├── rg-sund-dwh-prod-backup       # 备份资源组
└── rg-sund-dwh-prod-monitoring   # 监控资源组

SUND-DWH-DEV (开发环境)
├── rg-sund-dwh-dev               # 开发资源组
└── rg-sund-dwh-dev-test          # 测试资源组
```

#### 核心Azure服务
```yaml
# Azure服务清单
services:
  compute:
    - Azure Container Apps (作业执行)
    - Azure Container Registry (镜像存储)

  storage:
    - Azure Storage Account (数据湖存储)
    - Azure SQL Database (元数据存储)

  security:
    - Azure Key Vault (密钥管理)
    - User Managed Identity (身份认证)

  networking:
    - Virtual Network (网络隔离)
    - Private Endpoints (安全连接)

  monitoring:
    - Azure Monitor (监控告警)
    - Log Analytics (日志分析)
```

### 部署自动化脚本

#### Azure CLI部署脚本
```bash
#!/bin/bash
# SUND DWH基础设施部署脚本

# 设置变量
SUBSCRIPTION_ID="your-subscription-id"
RESOURCE_GROUP="rg-sund-dwh-dev"
LOCATION="germanywestcentral"
PROJECT_NAME="sund-dwh"
ENVIRONMENT="dev"

# 登录Azure
az login
az account set --subscription $SUBSCRIPTION_ID

# 创建资源组
az group create \
  --name $RESOURCE_GROUP \
  --location $LOCATION \
  --tags Environment=$ENVIRONMENT Project=$PROJECT_NAME

# 创建用户托管身份
IDENTITY_NAME="umi-$PROJECT_NAME-$ENVIRONMENT"
az identity create \
  --resource-group $RESOURCE_GROUP \
  --name $IDENTITY_NAME \
  --location $LOCATION

# 获取身份信息
IDENTITY_ID=$(az identity show --resource-group $RESOURCE_GROUP --name $IDENTITY_NAME --query id --output tsv)
IDENTITY_CLIENT_ID=$(az identity show --resource-group $RESOURCE_GROUP --name $IDENTITY_NAME --query clientId --output tsv)

# 创建虚拟网络
VNET_NAME="vnet-$PROJECT_NAME-$ENVIRONMENT"
az network vnet create \
  --resource-group $RESOURCE_GROUP \
  --name $VNET_NAME \
  --address-prefix 10.0.0.0/16 \
  --subnet-name default \
  --subnet-prefix ********/24

# 创建存储账户
STORAGE_NAME="st${PROJECT_NAME}${ENVIRONMENT}$(date +%s | tail -c 6)"
az storage account create \
  --resource-group $RESOURCE_GROUP \
  --name $STORAGE_NAME \
  --location $LOCATION \
  --sku Standard_LRS \
  --kind StorageV2 \
  --enable-hierarchical-namespace true

# 创建容器
az storage container create \
  --account-name $STORAGE_NAME \
  --name rawdata \
  --auth-mode login

az storage container create \
  --account-name $STORAGE_NAME \
  --name processed \
  --auth-mode login

# 创建Key Vault
KEYVAULT_NAME="kv-$PROJECT_NAME-$ENVIRONMENT-$(date +%s | tail -c 6)"
az keyvault create \
  --resource-group $RESOURCE_GROUP \
  --name $KEYVAULT_NAME \
  --location $LOCATION \
  --enable-rbac-authorization true

# 创建SQL Server
SQL_SERVER_NAME="sql-$PROJECT_NAME-$ENVIRONMENT-$(date +%s | tail -c 6)"
SQL_ADMIN_USER="sqladm"
SQL_ADMIN_PASSWORD="$(openssl rand -base64 32)"

az sql server create \
  --resource-group $RESOURCE_GROUP \
  --name $SQL_SERVER_NAME \
  --location $LOCATION \
  --admin-user $SQL_ADMIN_USER \
  --admin-password $SQL_ADMIN_PASSWORD

# 创建SQL数据库
az sql db create \
  --resource-group $RESOURCE_GROUP \
  --server $SQL_SERVER_NAME \
  --name jobinstanceframework \
  --edition GeneralPurpose \
  --family Gen5 \
  --capacity 2 \
  --compute-model Serverless \
  --auto-pause-delay 60

# 配置防火墙规则
az sql server firewall-rule create \
  --resource-group $RESOURCE_GROUP \
  --server $SQL_SERVER_NAME \
  --name AllowAzureServices \
  --start-ip-address 0.0.0.0 \
  --end-ip-address 0.0.0.0

# 创建Container Registry
ACR_NAME="acr${PROJECT_NAME}${ENVIRONMENT}$(date +%s | tail -c 6)"
az acr create \
  --resource-group $RESOURCE_GROUP \
  --name $ACR_NAME \
  --sku Basic \
  --admin-enabled true

# 创建Container Apps环境
CONTAINERAPPS_ENV="cae-$PROJECT_NAME-$ENVIRONMENT"
az containerapp env create \
  --resource-group $RESOURCE_GROUP \
  --name $CONTAINERAPPS_ENV \
  --location $LOCATION

# 输出重要信息
echo "=== 部署完成 ==="
echo "资源组: $RESOURCE_GROUP"
echo "存储账户: $STORAGE_NAME"
echo "Key Vault: $KEYVAULT_NAME"
echo "SQL Server: $SQL_SERVER_NAME"
echo "SQL管理员: $SQL_ADMIN_USER"
echo "SQL密码: $SQL_ADMIN_PASSWORD"
echo "Container Registry: $ACR_NAME"
echo "托管身份客户端ID: $IDENTITY_CLIENT_ID"
```

#### 权限配置脚本
```bash
#!/bin/bash
# 权限配置脚本

# 为托管身份分配权限
# Storage Blob Data Contributor
az role assignment create \
  --assignee $IDENTITY_CLIENT_ID \
  --role "Storage Blob Data Contributor" \
  --scope "/subscriptions/$SUBSCRIPTION_ID/resourceGroups/$RESOURCE_GROUP/providers/Microsoft.Storage/storageAccounts/$STORAGE_NAME"

# Key Vault Secrets User
az role assignment create \
  --assignee $IDENTITY_CLIENT_ID \
  --role "Key Vault Secrets User" \
  --scope "/subscriptions/$SUBSCRIPTION_ID/resourceGroups/$RESOURCE_GROUP/providers/Microsoft.KeyVault/vaults/$KEYVAULT_NAME"

# Container Registry Pull
az role assignment create \
  --assignee $IDENTITY_CLIENT_ID \
  --role "AcrPull" \
  --scope "/subscriptions/$SUBSCRIPTION_ID/resourceGroups/$RESOURCE_GROUP/providers/Microsoft.ContainerRegistry/registries/$ACR_NAME"
```

### 数据库初始化

#### 元数据框架设置
```sql
-- 在master数据库执行
USE master;

-- 创建登录用户
CREATE LOGIN process_user_metadata
WITH PASSWORD = 'YourSecurePassword123!';

-- 在jobinstanceframework数据库执行
USE jobinstanceframework;

-- 创建Schema
CREATE SCHEMA metadata;

EXEC sp_addextendedproperty
    @name = N'Description',
    @value = N'Schema containing metadata about load and transformation processing',
    @level0type = N'SCHEMA',
    @level0name = N'metadata';

-- 创建数据库用户
CREATE USER process_user_metadata
FOR LOGIN process_user_metadata
WITH DEFAULT_SCHEMA = metadata;

-- 分配权限
EXEC sp_addrolemember N'db_datawriter', N'process_user_metadata';
EXEC sp_addrolemember N'db_datareader', N'process_user_metadata';

-- 创建作业实例状态表
CREATE TABLE metadata.job_instance_status (
    job_instance_id int not null primary key,
    process_instance_id bigint NULL,
    parent_id bigint NULL,
    process_instance_name varchar(255) NULL,
    job_name varchar(255) NULL,
    work_item varchar(1024) NULL,
    time_range_start datetime2 NULL,
    time_range_end datetime2 NULL,
    value_range_start varchar(512) NULL,
    value_range_end varchar(512) NULL,
    job_started_at datetime2 NOT NULL,
    job_ended_at datetime2 NULL,
    count_input bigint NULL,
    count_output bigint NULL,
    count_rejected bigint NULL,
    count_ignored bigint NULL,
    return_code bigint NULL,
    return_message varchar(1024) NULL,
    host_name varchar(255) NULL,
    host_pid bigint NULL,
    host_user varchar(128) NULL
);

-- 创建索引
CREATE INDEX JOB_INSTANCE_STATUS_JOB_NAME
ON metadata.job_instance_status(job_name);

-- 创建序列
CREATE SEQUENCE metadata.job_instance_status_job_instance_id_seq
AS INT
START WITH 1
INCREMENT BY 1;

-- 授予表权限
GRANT INSERT, SELECT, UPDATE ON OBJECT::metadata.job_instance_status TO process_user_metadata;
GRANT SELECT, INSERT, UPDATE ON SCHEMA::metadata TO process_user_metadata;
```

### Container Apps作业配置

#### 作业定义模板
```yaml
# container-app-job-template.yaml
apiVersion: 2023-05-01
type: Microsoft.App/jobs
properties:
  environmentId: /subscriptions/{subscription-id}/resourceGroups/{rg}/providers/Microsoft.App/managedEnvironments/{env-name}
  configuration:
    scheduleTriggerConfig:
      cronExpression: "0 2 * * *"  # 每日2点执行
      parallelism: 1
      completions: 1
    replicaTimeout: 3600  # 1小时超时
    replicaRetryLimit: 3
    manualTriggerConfig:
      parallelism: 1
      completions: 1
  template:
    containers:
    - name: dwh-job
      image: {acr-name}.azurecr.io/sund-dwh:latest
      command: ["python"]
      args: ["./processes/rsap_rin2_j1/__main__.py"]
      resources:
        cpu: 1.0
        memory: 2Gi
      env:
      - name: UMI_CLIENT_ID
        secretRef: umi-client-id
      - name: AZURE_STORAGE_ACCOUNT
        secretRef: storage-account-name
      - name: AZURE_KEYVAULT_URL
        secretRef: keyvault-url
      - name: SQL_CONNECTION_STRING
        secretRef: sql-connection-string
```

#### 批量作业创建脚本
```bash
#!/bin/bash
# 批量创建Container App Jobs

JOBS=(
    "rsap_rin2_j1:0 2 * * *:./processes/rsap_rin2_j1/__main__.py"
    "rbbe_order_p1:0 3 * * *:./processes/rbbe_order_p1/__main__.py"
    "rpim_product_p1:0 4 * * *:./processes/rpim_product_p1/__main__.py"
    "business_vault_daily:0 5 * * *:./processes/business_vault_daily/__main__.py"
)

for job_config in "${JOBS[@]}"; do
    IFS=':' read -r job_name cron_expr script_path <<< "$job_config"

    echo "创建作业: $job_name"

    az containerapp job create \
        --resource-group $RESOURCE_GROUP \
        --name "job-$job_name" \
        --environment $CONTAINERAPPS_ENV \
        --trigger-type Schedule \
        --cron-expression "$cron_expr" \
        --image "$ACR_NAME.azurecr.io/sund-dwh:latest" \
        --command "python" \
        --args "$script_path" \
        --cpu 1.0 \
        --memory 2Gi \
        --user-assigned $IDENTITY_ID \
        --secrets \
            umi-client-id=$IDENTITY_CLIENT_ID \
            storage-account-name=$STORAGE_NAME \
            keyvault-url="https://$KEYVAULT_NAME.vault.azure.net/" \
        --env-vars \
            UMI_CLIENT_ID=secretref:umi-client-id \
            AZURE_STORAGE_ACCOUNT=secretref:storage-account-name \
            AZURE_KEYVAULT_URL=secretref:keyvault-url
done
```

---

## 开发流程

### GitHub工作流

#### 分支策略
```
main (主分支)
├── personal/developer1 (个人开发分支)
├── personal/developer2 (个人开发分支)
├── feature/new-datasource (功能分支)
├── hotfix/critical-bug (热修复分支)
└── release/v1.2.0 (发布分支)
```

#### Pull Request流程
```mermaid
graph TD
    A[开发者创建个人分支] --> B[本地开发和测试]
    B --> C[提交代码到个人分支]
    C --> D[创建Pull Request]
    D --> E[代码审查 - 4眼原则]
    E --> F{审查通过?}
    F -->|是| G[合并到main分支]
    F -->|否| H[修改代码]
    H --> C
    G --> I[触发CI/CD管道]
    I --> J[构建Docker镜像]
    J --> K[推送到Container Registry]
```

#### GitHub Actions配置
```yaml
# .github/workflows/container_build.yml
name: Build and Push Container

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

env:
  REGISTRY: ${{ secrets.ACR_LOGIN_SERVER }}
  IMAGE_NAME: sund-dwh

jobs:
  build-and-push:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write

    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Log in to Container Registry
      uses: azure/docker-login@v1
      with:
        login-server: ${{ secrets.ACR_LOGIN_SERVER }}
        username: ${{ secrets.ACR_USERNAME }}
        password: ${{ secrets.ACR_PASSWORD }}

    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=sha,prefix={{branch}}-

    - name: Build and push Docker image
      uses: docker/build-push-action@v5
      with:
        context: .
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}

    - name: Run security scan
      uses: azure/container-scan@v0
      with:
        image-name: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ github.sha }}
```

### DVPD开发流程

#### 新数据源接入步骤
```python
# 1. 创建DVPD配置文件
# resources/dvpd/new_datasource.dvpd.json
{
    "dvpd_version": "0.6.2",
    "pipeline_name": "new_datasource_p1",
    "record_source_name_expression": "newsystem.entity",
    "stage_properties": [{
        "stage_schema": "stage_rvlt",
        "stage_table_name": "new_datasource_p1_stage"
    }],
    "fields": [
        {
            "field_name": "business_key_1",
            "field_type": "VARCHAR(50)",
            "targets": [{"table_name": "NEW_ENTITY_HUB"}]
        }
    ],
    "data_vault_model": [{
        "schema_name": "rvlt_newsystem",
        "tables": [{
            "table_name": "NEW_ENTITY_HUB",
            "table_stereotype": "hub",
            "hub_key_column_name": "HK_NEW_ENTITY"
        }]
    }]
}

# 2. 生成DDL脚本
python tools/generate_ddl.py --config resources/dvpd/new_datasource.dvpd.json

# 3. 创建ETL处理脚本
# processes/new_datasource_p1/__main__.py
def main():
    job_instance = CimtJobInstance("new_datasource_p1")
    job_instance.start_instance()

    try:
        deploy_datamodel(job_instance)
        process_source_files(job_instance)
        job_instance.end_instance()
    except Exception as e:
        job_instance.end_instance_with_error(1, str(e))
        raise

# 4. 配置Container App Job
az containerapp job create \
    --name "job-new-datasource-p1" \
    --cron-expression "0 6 * * *" \
    --image "acr.azurecr.io/sund-dwh:latest" \
    --args "./processes/new_datasource_p1/__main__.py"
```

#### 代码质量检查
```python
# tools/quality_check.py
def validate_dvpd_config(config_file):
    """验证DVPD配置文件"""

    with open(config_file, 'r') as f:
        config = json.load(f)

    # 检查必需字段
    required_fields = ['dvpd_version', 'pipeline_name', 'fields', 'data_vault_model']
    for field in required_fields:
        if field not in config:
            raise ValueError(f"Missing required field: {field}")

    # 检查命名规范
    pipeline_name = config['pipeline_name']
    if not re.match(r'^[a-z][a-z0-9_]*_p\d+$', pipeline_name):
        raise ValueError(f"Invalid pipeline name format: {pipeline_name}")

    # 检查表命名规范
    for model in config['data_vault_model']:
        for table in model['tables']:
            table_name = table['table_name']
            if not validate_table_name(table_name, table['table_stereotype']):
                raise ValueError(f"Invalid table name: {table_name}")

    return True

def validate_table_name(table_name, stereotype):
    """验证表名是否符合命名规范"""

    patterns = {
        'hub': r'^[A-Z]{4}_[A-Z_]+_HUB$',
        'lnk': r'^[A-Z]{4}_[A-Z_]+_LNK$',
        'sat': r'^[A-Z]{4}_[A-Z_]+_[A-Z0-9]+_[A-Z0-9]+_SAT$'
    }

    pattern = patterns.get(stereotype)
    if pattern and re.match(pattern, table_name):
        return True

    return False
```

---