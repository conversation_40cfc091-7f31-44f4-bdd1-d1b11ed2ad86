CREATE or REPLACE VIEW bvlt_sap.bsap_invoice_line_inv1_korrigiert_v1_b10_current_sat AS

with gesamtnetto_current as (
	select head_s.hk_rsap_invoice_oinv ,
		coalesce (head_s.doctotal,0)
		- coalesce (head_s.totalexpns,0)
		- coalesce (head_s.vatsum ,0)
		+ coalesce (head_s.discsum ,0)
		+ coalesce (head_s.dpmamnt ,0) as gesamtnetto
		,head_s.discsum 
		,case when head_s.canceled ='C'
			then -1
			else 1 end   richtungsfaktor
	from rvlt_sap.rsap_invoice_oinv_p1_l20_sat head_s 
	where head_s.md_valid_before =lib.dwh_far_future_date()
	and not  head_s.md_is_deleted 
)
,line_fipp_bonus_current as (
select 
		line_inv2_l.hk_rsap_invoice_line_inv1 ,
		sum(line_inv2_s.linetotal) inv2_linetotal_sum
from rvlt_sap.rsap_invoice_line_inv1_inv2_p1_l20_sat line_inv2_s
join rvlt_sap.rsap_invoice_line_inv1_inv2_lnk line_inv2_l
    on line_inv2_l.lk_rsap_invoice_line_inv1_inv2 = line_inv2_s.lk_rsap_invoice_line_inv1_inv2 
join rvlt_sap.rsap_invoice_line_inv1_hub line_h
    on line_h.hk_rsap_invoice_line_inv1 = line_inv2_l.hk_rsap_invoice_line_inv1 
where  line_inv2_s.md_valid_before =lib.dwh_far_future_date()
		and not line_inv2_s.md_is_deleted 
		and line_h.company = 'FIPP_DE'
		and line_inv2_s.expnscode in (3,4)
group by 1		
)
,netto_anteil as (
	select 
		line_s.hk_rsap_invoice_line_inv1 ,
		gc.discsum,
		gc.richtungsfaktor,
		line_s.linetotal+coalesce(lfpc.inv2_linetotal_sum,0) linetotal_mit_inv2,
		case when gc.gesamtnetto<>0  
			then (line_s.linetotal+coalesce(lfpc.inv2_linetotal_sum,0))/ gc.gesamtnetto
			else 0 end 							as anteil_am_gesamtnetto,
	from gesamtnetto_current gc
	join rvlt_sap.rsap_invoice_line_inv1_invoice_oinv_lnk head_line_l
	     on head_line_l.hk_rsap_invoice_oinv =gc.hk_rsap_invoice_oinv
	join rvlt_sap.rsap_invoice_line_inv1_p1_l20_sat line_s
		on line_s.hk_rsap_invoice_line_inv1 =head_line_l.hk_rsap_invoice_line_inv1 
		and line_s.md_valid_before =lib.dwh_far_future_date()
		and not line_s.md_is_deleted 
		and line_s.linetype ='R' 
	left join line_fipp_bonus_current lfpc
		on lfpc.hk_rsap_invoice_line_inv1=line_s.hk_rsap_invoice_line_inv1
)
-- finale struktur
select
	hk_rsap_invoice_line_inv1,
	anteil_am_gesamtnetto 										as anteil_am_gesamtnetto,
	case when discsum >0
		then linetotal_mit_inv2 - discsum*anteil_am_gesamtnetto
		else linetotal_mit_inv2 end 	* richtungsfaktor				as linetotal_korrigiert
from netto_anteil;

/*
 *  Select * from bvlt_sap.bsap_invoice_line_inv1_korrigiert_v1_b10_current_sat order by 3
 */ 
