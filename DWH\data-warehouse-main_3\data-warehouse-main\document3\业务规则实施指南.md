# 业务规则实施指南
## SUND数据仓库Business Vault层转换规则详解

### 版本信息
- **文档版本**: 1.0
- **项目**: SUND数据仓库
- **适用层级**: Business Vault Layer
- **创建日期**: 2024年

---

## 目录

1. [Business Vault概述](#business-vault概述)
2. [成本分配规则](#成本分配规则)
3. [产品关联规则](#产品关联规则)
4. [时间标准化规则](#时间标准化规则)
5. [业务伙伴整合规则](#业务伙伴整合规则)
6. [数据质量规则](#数据质量规则)
7. [实施框架](#实施框架)

---

## Business Vault概述

### 设计理念
Business Vault层是Data Vault 2.0架构中的业务规则层，负责：
- 应用企业级业务规则
- 整合多源系统数据
- 提供一致的业务视图
- 支持复杂的业务计算

### 核心原则
```python
business_vault_principles = {
    "数据整合": "统一来自不同源系统的相同业务实体",
    "业务规则": "应用企业级的业务逻辑和计算规则",
    "数据清洗": "标准化和清洗数据以确保质量",
    "历史保留": "维护完整的业务规则变更历史",
    "性能优化": "为下游应用提供优化的数据结构"
}
```

### 实施策略
```
Raw Vault (源系统数据)
    ↓
Business Rules Engine (业务规则引擎)
    ↓
Business Vault (业务视图)
    ↓
Data Marts (应用层)
```

---

## 成本分配规则

### BillBee订单成本分配

#### 业务背景
BillBee订单包含产品项目和非产品项目（如运费、手续费等），需要将非产品成本按比例分配到产品项目上，以获得真实的产品成本。

#### 分配算法
```python
class BillBeeOrderCostDistribution:
    """BillBee订单成本分配算法"""

    def __init__(self, order_data):
        self.order_data = order_data
        self.product_items = [item for item in order_data['items'] if item.get('product')]
        self.non_product_items = [item for item in order_data['items'] if not item.get('product')]

    def calculate_distribution(self):
        """计算成本分配"""

        # 1. 计算待分配金额
        zu_verteilender_wert_netto = self._calculate_distributable_amount_net()
        zu_verteilender_wert_brutto = self._calculate_distributable_amount_gross()

        # 2. 计算产品项目总额
        artikel_total_price_sum = sum(item['total_price'] for item in self.product_items)

        # 3. 计算分配系数
        distribution_factors = self._calculate_distribution_factors(artikel_total_price_sum)

        # 4. 应用分配
        corrected_costs = []
        for i, item in enumerate(self.product_items):
            corrected_cost = self._apply_distribution(item, distribution_factors[i], zu_verteilender_wert_netto)
            corrected_costs.append(corrected_cost)

        return corrected_costs

    def _calculate_distributable_amount_net(self):
        """计算待分配净额"""
        non_product_net = sum(
            item['total_price'] - item['tax_amount']
            for item in self.non_product_items
        )
        shipping_net = self.order_data['shipping_cost'] / 1.19  # 德国VAT 19%
        return non_product_net + shipping_net

    def _calculate_distributable_amount_gross(self):
        """计算待分配总额"""
        non_product_gross = sum(item['total_price'] for item in self.non_product_items)
        return non_product_gross + self.order_data['shipping_cost']

    def _calculate_distribution_factors(self, artikel_total_price_sum):
        """计算分配系数"""
        if artikel_total_price_sum == 0:
            # 按数量平均分配
            factor = 1.0 / len(self.product_items) if self.product_items else 0
            return [factor] * len(self.product_items)
        else:
            # 按金额比例分配
            return [
                item['total_price'] / artikel_total_price_sum
                for item in self.product_items
            ]

    def _apply_distribution(self, item, factor, distributable_amount):
        """应用分配到单个项目"""
        original_net = item['total_price'] - item['tax_amount']
        distributed_amount = factor * distributable_amount

        return {
            'item_id': item['id'],
            'original_net': original_net,
            'distributed_amount': distributed_amount,
            'corrected_net': original_net + distributed_amount,
            'distribution_factor': factor
        }
```

#### 数据结构实现
```sql
-- Business Vault成本分配结果表
CREATE TABLE bbbe_order_item_artikelkosten_korrigiert_v1_b10_sat (
    MD_INSERTED_AT                    TIMESTAMP      NOT NULL,
    MD_RUN_ID                         INT            NOT NULL,
    MD_RECORD_SOURCE                  VARCHAR(255)   NOT NULL,
    MD_IS_DELETED                     BOOLEAN        NOT NULL,
    MD_VALID_BEFORE                   TIMESTAMP      NOT NULL,

    -- 父表键
    HK_RBBE_ORDER_ITEM                CHAR(28)       NOT NULL,

    -- 记录哈希
    RH_BBBE_ORDER_ITEM_ARTIKELKOSTEN_KORRIGIERT_V1_B10_SAT CHAR(28) NOT NULL,

    -- 业务字段
    TOTAL_PRICE_NETTO                 NUMERIC(16,2)  NULL,  -- 原始净价
    ZU_VERTEILENDER_WERT_NETTO        NUMERIC(16,2)  NULL,  -- 待分配净额
    PRODUCT_VERTEILSCHUESSEL          NUMERIC(16,8)  NULL,  -- 分配系数
    TOTAL_PRICE_NETTO_KORRIGIERT      NUMERIC(16,2)  NULL,  -- 修正后净价

    PRIMARY KEY (HK_RBBE_ORDER_ITEM, MD_INSERTED_AT)
);
```

### SAP发票折扣分配

#### 业务背景
SAP发票可能包含整单折扣，需要按行项目金额比例分配到各行，同时处理FIPP_DE公司的特殊运费处理逻辑。

#### 分配算法
```python
class SAPInvoiceDiscountDistribution:
    """SAP发票折扣分配算法"""

    def __init__(self, invoice_data):
        self.invoice_data = invoice_data
        self.company = invoice_data['company']
        self.is_fipp_de = (self.company == 'FIPP_DE')

    def calculate_discount_distribution(self):
        """计算折扣分配"""

        # 1. 计算发票总净额
        total_netto = self._calculate_total_netto()

        # 2. 处理FIPP_DE特殊运费逻辑
        line_corrections = self._calculate_fipp_corrections() if self.is_fipp_de else {}

        # 3. 分配折扣到各行
        corrected_lines = []
        for line in self.invoice_data['lines']:
            if line['line_type'] == 'R':  # 只处理常规行项目
                corrected_line = self._apply_discount_to_line(
                    line, total_netto, line_corrections
                )
                corrected_lines.append(corrected_line)

        return corrected_lines

    def _calculate_total_netto(self):
        """计算发票总净额"""
        return (
            self.invoice_data['doc_total'] -      # 总额
            self.invoice_data['vat_sum'] -        # 税额
            self.invoice_data['total_expns'] +    # 运费
            self.invoice_data['disc_sum'] +       # 折扣
            self.invoice_data['dp_amnt']          # 预付款
        )

    def _calculate_fipp_corrections(self):
        """计算FIPP_DE运费修正"""
        corrections = {}

        for inv2_line in self.invoice_data.get('inv2_lines', []):
            if inv2_line['expns_code'] in [3, 4]:  # 运费代码
                parent_key = f"{inv2_line['doc_entry']}_{inv2_line['line_num']}"
                if parent_key not in corrections:
                    corrections[parent_key] = 0
                corrections[parent_key] += inv2_line['line_total']

        return corrections

    def _apply_discount_to_line(self, line, total_netto, line_corrections):
        """应用折扣到单行"""
        line_key = f"{line['doc_entry']}_{line['line_num']}"

        # 原始行总额 + 运费修正
        adjusted_line_total = line['line_total'] + line_corrections.get(line_key, 0)

        if total_netto == 0:
            # 数据错误情况
            corrected_line_total = 0
            proportion = 0
            discount_portion = 0
        else:
            # 正常分配
            proportion = adjusted_line_total / total_netto
            discount_portion = proportion * self.invoice_data['disc_sum']
            corrected_line_total = adjusted_line_total - discount_portion

        # 处理取消订单
        if self.invoice_data.get('canceled') == 'C':
            corrected_line_total = -corrected_line_total

        return {
            'doc_entry': line['doc_entry'],
            'line_num': line['line_num'],
            'original_line_total': line['line_total'],
            'fipp_adjustment': line_corrections.get(line_key, 0),
            'proportion': proportion,
            'discount_portion': discount_portion,
            'corrected_line_total': corrected_line_total
        }
```

---

## 产品关联规则

### BillBee产品GTIN映射

#### 业务背景
BillBee产品需要与全球产品(Global Product)建立关联，以便与PIM系统的产品属性进行整合。关联基于GTIN/EAN码。

#### 映射算法
```python
class ProductGTINMapping:
    """产品GTIN映射算法"""

    def __init__(self, data_access):
        self.data_access = data_access

    def create_billbee_product_mapping(self):
        """创建BillBee产品GTIN映射"""

        mappings = []
        products = self.data_access.get_all_billbee_products()

        for product in products:
            gtin = self._determine_product_gtin(product)
            if gtin:
                mappings.append({
                    'hk_rbbe_product': product['hk_rbbe_product'],
                    'gtin': gtin,
                    'mapping_source': self._get_mapping_source(product, gtin),
                    'confidence_score': self._calculate_confidence(product, gtin)
                })

        return mappings

    def _determine_product_gtin(self, product):
        """确定产品的GTIN"""

        # 1. 优先使用产品直接EAN
        if product.get('ean'):
            return product['ean']

        # 2. 使用频率最高的EAN
        return self._get_most_frequent_ean(product['hk_rbbe_product'])

    def _get_most_frequent_ean(self, product_hk):
        """获取使用频率最高的EAN"""

        query = """
        SELECT
            ois.PRODUCT_EAN,
            COUNT(*) as frequency
        FROM rbbe_order_item_order_p1_l10_sat ois
        JOIN rbbe_order_item_hub oih ON ois.hk_rbbe_order_item = oih.hk_rbbe_order_item
        JOIN rbbe_order_item_product_lnk oipl ON oih.hk_rbbe_order_item = oipl.hk_rbbe_order_item
        WHERE oipl.hk_rbbe_product = %s
          AND ois.md_valid_before = '2299-12-30 00:00:00'
          AND ois.PRODUCT_EAN IS NOT NULL
          AND ois.PRODUCT_EAN != ''
        GROUP BY ois.PRODUCT_EAN
        ORDER BY frequency DESC, ois.PRODUCT_EAN
        LIMIT 1
        """

        result = self.data_access.execute_query(query, [product_hk])
        return result[0]['PRODUCT_EAN'] if result else None

    def _get_mapping_source(self, product, gtin):
        """获取映射来源"""
        if product.get('ean') == gtin:
            return 'direct_ean'
        else:
            return 'frequency_based'

    def _calculate_confidence(self, product, gtin):
        """计算映射置信度"""
        if product.get('ean') == gtin:
            return 1.0  # 直接EAN，100%置信度
        else:
            # 基于使用频率计算置信度
            frequency = self._get_ean_frequency(product['hk_rbbe_product'], gtin)
            total_orders = self._get_total_orders(product['hk_rbbe_product'])
            return frequency / total_orders if total_orders > 0 else 0.0
```

### PIM产品GTIN映射

#### 业务背景
PIM系统中的产品通过多种方式关联GTIN，需要提取所有可能的GTIN映射关系。

#### 映射规则
```python
class PIMProductGTINMapping:
    """PIM产品GTIN映射"""

    def extract_gtin_mappings(self, product_data):
        """提取GTIN映射关系"""

        mappings = []

        # 规则1: 直接国际产品ID映射
        if (product_data.get('internationalPidType') == 'gtin' and
            product_data.get('internationalPid')):
            mappings.append({
                'product_id': product_data['id'],
                'gtin': product_data['internationalPid'],
                'mapping_source': 'internationalPidType',
                'priority': 1
            })

        # 规则2: UDX产品EAN映射
        for udx_product in product_data.get('udxProducts', []):
            if udx_product.get('Name', '').startswith('EAN_'):
                mappings.append({
                    'product_id': product_data['id'],
                    'gtin': udx_product['Value'],
                    'mapping_source': udx_product['Name'],
                    'priority': 2
                })

        return mappings

    def create_multiactive_satellite_records(self, mappings):
        """创建多活跃卫星表记录"""

        records = []
        for mapping in mappings:
            records.append({
                'hk_rpim_product': self._calculate_product_hash(mapping['product_id']),
                'hk_rgnr_global_product': self._calculate_gtin_hash(mapping['gtin']),
                'mapping_source': mapping['mapping_source'],
                'priority': mapping['priority'],
                'gtin': mapping['gtin'],
                'md_inserted_at': datetime.now(),
                'md_record_source': 'pim.product.gtin_mapping'
            })

        return records
```

---

## 时间标准化规则

### 工作日历系统

#### 业务背景
不同公司有不同的节假日安排，需要建立统一的工作日历系统来支持销售计划的日级别分解。

#### 日历规则实现
```python
class WorkingDayCalendar:
    """工作日历系统"""

    def __init__(self, holiday_data):
        self.holiday_data = holiday_data
        self.company_holiday_mapping = {
            'SAP_AUSTRIA': 'Feiertage AT',
            'SAP_BERASIT': 'Feiertage HH',
            'SAP_BINGOLD': 'Feiertage HH',
            'SAP_DEISS': 'Feiertage HH',
            'SAP_FIPP': 'Feiertage HH',
            'SAP_NEDERLAND': 'Feestdagen NL',
            'SAP_SWISS': 'Feiertage CH'
        }

    def generate_calendar(self, company, year):
        """生成指定公司和年份的日历"""

        calendar_entries = []
        holiday_list = self.company_holiday_mapping.get(company, 'Feiertage HH')

        # 生成一年中的每一天
        start_date = datetime(year, 1, 1)
        end_date = datetime(year, 12, 31)
        current_date = start_date

        while current_date <= end_date:
            is_working_day = self._is_working_day(current_date, company, holiday_list)
            remarks = self._get_holiday_remarks(current_date, company, holiday_list)

            calendar_entries.append({
                'company': company,
                'calendar_date': current_date,
                'is_working_day': is_working_day,
                'day_of_week': current_date.weekday() + 1,  # 1=Monday, 7=Sunday
                'remarks': remarks
            })

            current_date += timedelta(days=1)

        return calendar_entries

    def _is_working_day(self, date, company, holiday_list):
        """判断是否为工作日"""

        # 1. 检查是否为周末
        if date.weekday() >= 5:  # Saturday=5, Sunday=6
            return False

        # 2. 检查是否为节假日
        company_holidays = self.holiday_data.get(holiday_list, [])
        for holiday in company_holidays:
            if holiday['start_date'] <= date <= holiday['end_date']:
                return False

        return True

    def _get_holiday_remarks(self, date, company, holiday_list):
        """获取节假日备注"""

        remarks = []
        company_holidays = self.holiday_data.get(holiday_list, [])

        for holiday in company_holidays:
            if holiday['start_date'] <= date <= holiday['end_date']:
                remarks.append(holiday['remarks'])

        return '|'.join(remarks) if remarks else None

    def get_working_days_count(self, company, start_date, end_date):
        """获取指定期间的工作日数量"""

        working_days = 0
        current_date = start_date
        holiday_list = self.company_holiday_mapping.get(company, 'Feiertage HH')

        while current_date <= end_date:
            if self._is_working_day(current_date, company, holiday_list):
                working_days += 1
            current_date += timedelta(days=1)

        return working_days
```

### 销售计划标准化

#### 业务背景
销售计划数据以年度或月度形式提供，需要标准化到日级别以支持精细化分析。

#### 标准化算法
```python
class SalesPlanNormalization:
    """销售计划标准化"""

    def __init__(self, calendar_system):
        self.calendar_system = calendar_system

    def normalize_to_daily(self, plan_data):
        """标准化到日级别"""

        daily_plans = []

        for plan_record in plan_data:
            # 解析子分组
            sub_groups = self._parse_sub_grouping(plan_record.get('sub_grouping_json'))

            if plan_record.get('month'):
                # 月度计划处理
                daily_records = self._normalize_monthly_plan(plan_record, sub_groups)
            else:
                # 年度计划处理
                daily_records = self._normalize_yearly_plan(plan_record, sub_groups)

            daily_plans.extend(daily_records)

        return daily_plans

    def _parse_sub_grouping(self, sub_grouping_json):
        """解析子分组JSON"""
        if not sub_grouping_json:
            return {
                'sales_area': '---',
                'sales_person': '---',
                'customer_group': '---'
            }

        try:
            sub_groups = json.loads(sub_grouping_json)
            return {
                'sales_area': sub_groups.get('Verkaufsgebiet', '---'),
                'sales_person': sub_groups.get('Verkäufernummer', '---'),
                'customer_group': sub_groups.get('Kundengruppe', '---')
            }
        except json.JSONDecodeError:
            return {
                'sales_area': '---',
                'sales_person': '---',
                'customer_group': '---'
            }

    def _normalize_monthly_plan(self, plan_record, sub_groups):
        """标准化月度计划"""

        company = plan_record['company']
        year = plan_record['year']
        month = plan_record['month']
        plan_value = plan_record['plan_value']

        # 计算该月工作日数
        start_date = datetime(year, month, 1)
        end_date = datetime(year, month, calendar.monthrange(year, month)[1])
        working_days = self.calendar_system.get_working_days_count(company, start_date, end_date)

        # 计算日均值
        daily_value = plan_value / working_days if working_days > 0 else 0

        # 生成每日记录
        daily_records = []
        current_date = start_date

        while current_date <= end_date:
            is_working_day = self.calendar_system._is_working_day(
                current_date, company,
                self.calendar_system.company_holiday_mapping.get(company, 'Feiertage HH')
            )

            daily_records.append({
                'company': company,
                'plan_date': current_date,
                'planned_revenue_eur': daily_value if is_working_day else 0,
                'sales_area': sub_groups['sales_area'],
                'sales_person': sub_groups['sales_person'],
                'customer_group': sub_groups['customer_group'],
                'source_period': f"{year}-{month:02d}",
                'source_plan_value': plan_value,
                'working_days_in_period': working_days
            })

            current_date += timedelta(days=1)

        return daily_records

    def _normalize_yearly_plan(self, plan_record, sub_groups):
        """标准化年度计划"""

        company = plan_record['company']
        year = plan_record['year']
        plan_value = plan_record['plan_value']

        # 计算该年工作日数
        start_date = datetime(year, 1, 1)
        end_date = datetime(year, 12, 31)
        working_days = self.calendar_system.get_working_days_count(company, start_date, end_date)

        # 计算日均值
        daily_value = plan_value / working_days if working_days > 0 else 0

        # 生成每日记录
        daily_records = []
        current_date = start_date

        while current_date <= end_date:
            is_working_day = self.calendar_system._is_working_day(
                current_date, company,
                self.calendar_system.company_holiday_mapping.get(company, 'Feiertage HH')
            )

            daily_records.append({
                'company': company,
                'plan_date': current_date,
                'planned_revenue_eur': daily_value if is_working_day else 0,
                'sales_area': sub_groups['sales_area'],
                'sales_person': sub_groups['sales_person'],
                'customer_group': sub_groups['customer_group'],
                'source_period': str(year),
                'source_plan_value': plan_value,
                'working_days_in_period': working_days
            })

            current_date += timedelta(days=1)

        return daily_records
```

---

## 业务伙伴整合规则

### 统一业务伙伴视图

#### 业务背景
需要将来自SAP和BillBee的业务伙伴数据整合成统一视图，避免ID冲突并提供一致的业务伙伴信息。

#### 整合算法
```python
class BusinessPartnerIntegration:
    """业务伙伴整合"""

    def __init__(self, data_access):
        self.data_access = data_access

    def create_unified_view(self):
        """创建统一业务伙伴视图"""

        unified_partners = []

        # 1. 处理SAP业务伙伴
        sap_partners = self._process_sap_partners()
        unified_partners.extend(sap_partners)

        # 2. 处理BillBee商店
        billbee_partners = self._process_billbee_partners()
        unified_partners.extend(billbee_partners)

        return unified_partners

    def _process_sap_partners(self):
        """处理SAP业务伙伴"""

        sap_partners = []
        raw_partners = self.data_access.get_sap_business_partners()

        for partner in raw_partners:
            # 查找客户组信息
            customer_group = self._lookup_customer_group(
                partner['company'],
                partner['groupcode']
            )

            unified_partner = {
                'xd_geschaeftspartner_id': f"SAP-{partner['company']}-{partner['cardcode']}",
                'source_system': 'SAP',
                'company': partner['company'],
                'sap_cardcode': partner['cardcode'],
                'hk_rsap_business_partner_ocrd': partner['hk_rsap_business_partner_ocrd'],
                'billbee_shop_id': None,
                'hk_rbbe_shop': 'ffffffffffffffffffffffffffff',  # Missing ghost record
                'name': partner['cardname'],
                'gesellschaft': partner['company'],
                'kundengruppe': customer_group,
                'verkaufsgebiet': partner.get('u_dim1', '---'),
                'verkaeufernummer': partner.get('u_dim2', '---'),
                'is_active': True,
                'created_from': 'sap_ocrd'
            }

            sap_partners.append(unified_partner)

        return sap_partners

    def _process_billbee_partners(self):
        """处理BillBee商店"""

        billbee_partners = []
        raw_shops = self.data_access.get_billbee_shops()

        for shop in raw_shops:
            unified_partner = {
                'xd_geschaeftspartner_id': f"SUND_DIGITAL-{shop['billbee_shop_id']}",
                'source_system': 'BillBee',
                'company': None,
                'sap_cardcode': None,
                'hk_rsap_business_partner_ocrd': 'ffffffffffffffffffffffffffff',  # Missing ghost record
                'billbee_shop_id': shop['billbee_shop_id'],
                'hk_rbbe_shop': shop['hk_rbbe_shop'],
                'name': shop['seller_billbee_shop_name'],
                'gesellschaft': 'SUND_DIGITAL',
                'kundengruppe': '---',
                'verkaufsgebiet': '---',
                'verkaeufernummer': '---',
                'is_active': True,
                'created_from': 'billbee_shop'
            }

            billbee_partners.append(unified_partner)

        return billbee_partners

    def _lookup_customer_group(self, company, group_code):
        """查找客户组名称"""

        if not group_code:
            return '---'

        query = """
        SELECT groupname
        FROM rsap_card_group_current_ref
        WHERE company = %s AND groupcode = %s
        """

        result = self.data_access.execute_query(query, [company, group_code])
        return result[0]['groupname'] if result else '---'

    def detect_potential_duplicates(self, unified_partners):
        """检测潜在重复项"""

        duplicates = []
        name_groups = {}

        # 按名称分组
        for partner in unified_partners:
            name_key = partner['name'].lower().strip()
            if name_key not in name_groups:
                name_groups[name_key] = []
            name_groups[name_key].append(partner)

        # 查找可能的重复项
        for name_key, partners in name_groups.items():
            if len(partners) > 1:
                # 检查是否来自不同系统
                systems = set(p['source_system'] for p in partners)
                if len(systems) > 1:
                    duplicates.append({
                        'name': name_key,
                        'partners': partners,
                        'confidence': self._calculate_duplicate_confidence(partners)
                    })

        return duplicates

    def _calculate_duplicate_confidence(self, partners):
        """计算重复置信度"""

        # 简单的相似度计算
        if len(partners) != 2:
            return 0.0

        p1, p2 = partners[0], partners[1]

        # 名称完全匹配
        name_match = 1.0 if p1['name'].lower() == p2['name'].lower() else 0.0

        # 地区匹配
        area_match = 1.0 if p1['verkaufsgebiet'] == p2['verkaufsgebiet'] else 0.0

        # 综合置信度
        confidence = (name_match * 0.7 + area_match * 0.3)

        return confidence
```

---

## 数据质量规则

### 数据一致性检查

#### 跨系统一致性验证
```python
class DataQualityRules:
    """数据质量规则"""

    def __init__(self, data_access):
        self.data_access = data_access

    def validate_cross_system_consistency(self):
        """验证跨系统数据一致性"""

        validation_results = []

        # 1. 产品GTIN一致性检查
        gtin_consistency = self._check_gtin_consistency()
        validation_results.append(gtin_consistency)

        # 2. 业务伙伴重复检查
        partner_duplicates = self._check_partner_duplicates()
        validation_results.append(partner_duplicates)

        # 3. 金额计算一致性检查
        amount_consistency = self._check_amount_consistency()
        validation_results.append(amount_consistency)

        return validation_results

    def _check_gtin_consistency(self):
        """检查GTIN一致性"""

        query = """
        WITH gtin_conflicts AS (
            SELECT
                gp.gtin,
                COUNT(DISTINCT bp.hk_rbbe_product) as billbee_products,
                COUNT(DISTINCT pp.hk_rpim_product) as pim_products
            FROM rgnr_global_product_hub gp
            LEFT JOIN bbbe_product_rgnr_global_product_lnk bp ON gp.hk_rgnr_global_product = bp.hk_rgnr_global_product
            LEFT JOIN rpim_product_rgnr_global_product_lnk pp ON gp.hk_rgnr_global_product = pp.hk_rgnr_global_product
            GROUP BY gp.gtin
            HAVING COUNT(DISTINCT bp.hk_rbbe_product) > 1
               OR COUNT(DISTINCT pp.hk_rpim_product) > 1
        )
        SELECT COUNT(*) as conflict_count FROM gtin_conflicts
        """

        result = self.data_access.execute_query(query)
        conflict_count = result[0]['conflict_count']

        return {
            'rule_name': 'GTIN一致性检查',
            'status': 'PASS' if conflict_count == 0 else 'FAIL',
            'conflict_count': conflict_count,
            'description': f"发现{conflict_count}个GTIN冲突"
        }

    def _check_partner_duplicates(self):
        """检查业务伙伴重复"""

        query = """
        WITH partner_name_groups AS (
            SELECT
                LOWER(TRIM(name)) as normalized_name,
                COUNT(*) as partner_count,
                COUNT(DISTINCT source_system) as system_count
            FROM bgnr_geschaeftspartner_p1_b10_sat
            WHERE md_valid_before = '2299-12-30 00:00:00'
            GROUP BY LOWER(TRIM(name))
            HAVING COUNT(*) > 1 AND COUNT(DISTINCT source_system) > 1
        )
        SELECT COUNT(*) as duplicate_groups FROM partner_name_groups
        """

        result = self.data_access.execute_query(query)
        duplicate_count = result[0]['duplicate_groups']

        return {
            'rule_name': '业务伙伴重复检查',
            'status': 'WARNING' if duplicate_count > 0 else 'PASS',
            'duplicate_count': duplicate_count,
            'description': f"发现{duplicate_count}组可能重复的业务伙伴"
        }

    def _check_amount_consistency(self):
        """检查金额计算一致性"""

        # 检查BillBee成本分配的一致性
        query = """
        WITH cost_distribution_check AS (
            SELECT
                hk_rbbe_order,
                SUM(total_price_netto_korrigiert) as distributed_total,
                SUM(zu_verteilender_wert_netto) as distributable_amount,
                ABS(SUM(total_price_netto_korrigiert) - SUM(zu_verteilender_wert_netto)) as difference
            FROM bbbe_order_item_artikelkosten_korrigiert_v1_b10_sat
            WHERE md_valid_before = '2299-12-30 00:00:00'
            GROUP BY hk_rbbe_order
            HAVING ABS(SUM(total_price_netto_korrigiert) - SUM(zu_verteilender_wert_netto)) > 0.01
        )
        SELECT COUNT(*) as inconsistent_orders FROM cost_distribution_check
        """

        result = self.data_access.execute_query(query)
        inconsistent_count = result[0]['inconsistent_orders']

        return {
            'rule_name': '金额计算一致性检查',
            'status': 'PASS' if inconsistent_count == 0 else 'FAIL',
            'inconsistent_count': inconsistent_count,
            'description': f"发现{inconsistent_count}个金额计算不一致的订单"
        }
```

### 数据完整性规则

#### 必填字段检查
```python
class DataCompletenessRules:
    """数据完整性规则"""

    def __init__(self, data_access):
        self.data_access = data_access

    def check_required_fields(self):
        """检查必填字段"""

        completeness_results = []

        # 定义必填字段规则
        required_field_rules = {
            'rsap_business_partner_ocrd_hub': ['company', 'cardcode'],
            'rbbe_order_hub': ['billbeeorderid'],
            'rpim_product_hub': ['id'],
            'rgnr_global_product_hub': ['gtin']
        }

        for table_name, required_fields in required_field_rules.items():
            result = self._check_table_completeness(table_name, required_fields)
            completeness_results.append(result)

        return completeness_results

    def _check_table_completeness(self, table_name, required_fields):
        """检查单表完整性"""

        total_records = self._get_record_count(table_name)
        incomplete_records = 0

        for field in required_fields:
            null_count = self._get_null_count(table_name, field)
            incomplete_records = max(incomplete_records, null_count)

        completeness_rate = (total_records - incomplete_records) / total_records if total_records > 0 else 1.0

        return {
            'table_name': table_name,
            'total_records': total_records,
            'incomplete_records': incomplete_records,
            'completeness_rate': completeness_rate,
            'status': 'PASS' if completeness_rate >= 0.95 else 'FAIL'
        }

    def _get_record_count(self, table_name):
        """获取记录总数"""
        query = f"SELECT COUNT(*) as count FROM {table_name}"
        result = self.data_access.execute_query(query)
        return result[0]['count']

    def _get_null_count(self, table_name, field_name):
        """获取空值数量"""
        query = f"SELECT COUNT(*) as count FROM {table_name} WHERE {field_name} IS NULL"
        result = self.data_access.execute_query(query)
        return result[0]['count']
```

---

## 实施框架

### Business Vault ETL框架

#### 框架结构
```python
class BusinessVaultETLFramework:
    """Business Vault ETL框架"""

    def __init__(self, config):
        self.config = config
        self.data_access = DataAccess(config)
        self.job_instance = None

    def execute_business_rules(self, rule_set_name):
        """执行业务规则集"""

        self.job_instance = CimtJobInstance(f"business_vault_{rule_set_name}")
        self.job_instance.start_instance()

        try:
            # 1. 加载规则配置
            rules = self._load_rule_configuration(rule_set_name)

            # 2. 执行数据质量检查
            quality_results = self._execute_quality_checks(rules)

            # 3. 应用业务转换规则
            transformation_results = self._apply_transformation_rules(rules)

            # 4. 验证结果
            validation_results = self._validate_results(rules)

            # 5. 记录执行结果
            self._log_execution_results(quality_results, transformation_results, validation_results)

            self.job_instance.end_instance()

        except Exception as e:
            self.job_instance.end_instance_with_error(1, str(e))
            raise

    def _load_rule_configuration(self, rule_set_name):
        """加载规则配置"""

        rule_configurations = {
            'cost_distribution': {
                'rules': [
                    'billbee_order_cost_distribution',
                    'sap_invoice_discount_distribution'
                ],
                'dependencies': ['raw_vault_loaded'],
                'output_tables': [
                    'bbbe_order_item_artikelkosten_korrigiert_v1_b10_sat',
                    'bsap_invoice_line_inv1_korrigiert_v1_b10_sat'
                ]
            },
            'product_integration': {
                'rules': [
                    'billbee_product_gtin_mapping',
                    'pim_product_gtin_mapping'
                ],
                'dependencies': ['raw_vault_loaded'],
                'output_tables': [
                    'bbbe_product_rgnr_global_product_lnk',
                    'rpim_product_rgnr_global_product_lnk'
                ]
            },
            'partner_integration': {
                'rules': [
                    'unified_business_partner_view'
                ],
                'dependencies': ['raw_vault_loaded'],
                'output_tables': [
                    'bgnr_geschaeftspartner_p1_b10_sat'
                ]
            }
        }

        return rule_configurations.get(rule_set_name, {})

    def _execute_quality_checks(self, rules):
        """执行数据质量检查"""

        quality_checker = DataQualityRules(self.data_access)
        results = []

        for rule_name in rules.get('rules', []):
            if hasattr(quality_checker, f'check_{rule_name}'):
                check_method = getattr(quality_checker, f'check_{rule_name}')
                result = check_method()
                results.append(result)

        return results

    def _apply_transformation_rules(self, rules):
        """应用转换规则"""

        results = []

        for rule_name in rules.get('rules', []):
            transformer_class = self._get_transformer_class(rule_name)
            if transformer_class:
                transformer = transformer_class(self.data_access)
                result = transformer.execute()
                results.append(result)

        return results

    def _get_transformer_class(self, rule_name):
        """获取转换器类"""

        transformer_mapping = {
            'billbee_order_cost_distribution': BillBeeOrderCostDistribution,
            'sap_invoice_discount_distribution': SAPInvoiceDiscountDistribution,
            'billbee_product_gtin_mapping': ProductGTINMapping,
            'pim_product_gtin_mapping': PIMProductGTINMapping,
            'unified_business_partner_view': BusinessPartnerIntegration
        }

        return transformer_mapping.get(rule_name)

    def _validate_results(self, rules):
        """验证结果"""

        validator = DataCompletenessRules(self.data_access)
        validation_results = []

        for table_name in rules.get('output_tables', []):
            result = validator.validate_table_output(table_name)
            validation_results.append(result)

        return validation_results

    def _log_execution_results(self, quality_results, transformation_results, validation_results):
        """记录执行结果"""

        total_quality_issues = sum(1 for r in quality_results if r['status'] != 'PASS')
        total_transformation_errors = sum(1 for r in transformation_results if not r.get('success', True))
        total_validation_errors = sum(1 for r in validation_results if r['status'] != 'PASS')

        self.job_instance.count_input(sum(r.get('input_count', 0) for r in transformation_results))
        self.job_instance.count_output(sum(r.get('output_count', 0) for r in transformation_results))
        self.job_instance.count_rejected(total_quality_issues + total_transformation_errors + total_validation_errors)

        # 记录详细日志
        logger.info(f"Business Vault执行完成:")
        logger.info(f"  质量检查问题: {total_quality_issues}")
        logger.info(f"  转换错误: {total_transformation_errors}")
        logger.info(f"  验证错误: {total_validation_errors}")
```

---

*文档版本: 1.0*
*最后更新: 2024年*
*适用项目: SUND数据仓库*
