import json

from lib.json_utils import get_json_text_content_column, sort_json_content_column
from lib.blobstorage_utils import fetch_json_source_file_from_blob_container
from lib.cimtjobinstance import CimtJobInstance, cimtjobinstance_job
from lib.connection_snf import  connection_snf_for_dwh_connection_type
from lib.connection_azrblob import connection_azrblob

from lib.dvf_basics import dvf_assemble_datavault_hash, Dvf_dwh_connection_type
from lib.dvf_sqlByConvention_snf import dvf_get_datavault_hub_elt_sql, \
    dvf_get_datavault_sat_elt_sql, dvf_execute_elt_statement_list, dvf_get_check_hash_collision_hub_elt_sql, dvf_get_check_singularity_sat_elt_sql, \
    dvf_get_datavault_dlnk_elt_sql,dvf_get_check_hash_collision_dlnk_elt_sql

from lib.dbutils_snf import get_snf_dict_insert_sql, execute_snf_dict_bulk_insert



@cimtjobinstance_job
def fetch_and_stage_data(parent_job_instance, dwh_connection, **kwargs):

    my_job_instance = CimtJobInstance(kwargs['instance_job_name'], parent_job_instance)
    my_job_instance.start_instance()

    try:
        # ### FRAMEWORK PHASE: do processing here

        # prepare stage insert operation
        dwh_cursor = dwh_connection.cursor()
        dwh_cursor.execute("TRUNCATE TABLE stage_bvlt.bgnr_kalender_p1_stage")
        insert_statement = get_snf_dict_insert_sql(dwh_connection, "stage_bvlt", "bgnr_kalender_p1_stage")
        stage_data_row = {'md_inserted_at': my_job_instance.get_job_started_at().isoformat(),
                    'md_record_source': 'bgnr_kalender_p1',
                    'md_run_id': my_job_instance.get_job_instance_id(),
                    'md_is_deleted': False
                   }

        # connect to source, and order data
        source_connection = connection_snf_for_dwh_connection_type(Dvf_dwh_connection_type.owner)
        source_cursor = source_connection.cursor()

        src_sql = """ begin
                        use schema stage_bvlt;
                        create or replace temporary table holiday_src as
                            select company, strdate, enddate, rmrks
                            from rvlt_sap.rsap_feiertage_hld1_hub h 
                            join rvlt_sap.rsap_feiertage_hld1_p1_l20_msat ms on ms.hk_rsap_feiertage_hld1=h.hk_rsap_feiertage_hld1
                                and ms.md_valid_before = DV_D_MAIN_DATABASE.LIB.DWH_FAR_FUTURE_DATE()
                                and not ms.md_is_deleted
                                and h.hldcode in ('Feiertage HH', 'Feiertage AT', 'Feestdagen NL', 'Feiertage CH');
                        end;"""
        source_cursor.execute(src_sql)

        # Calculate and fetch the calculated number of days
        source_cursor.execute("SELECT DATEDIFF(day, DATE_TRUNC(YEAR, MIN(strdate)), LAST_DAY(MAX(strdate),YEAR)) + 1 AS num_days FROM holiday_src;")
        num_days = source_cursor.fetchone()[0]  # Fetch the calculated number of days

        source_sql = f"""WITH parameters AS (
                            SELECT 
                                DATE_TRUNC(YEAR, MIN(strdate)) AS start_date,
                                LAST_DAY(MAX(strdate),YEAR) AS end_date
                            FROM holiday_src
                        ), date_range AS (
                            SELECT 
                                DATEADD( day, -ROW_NUMBER() OVER (ORDER BY NULL), DATEADD(day, 1, (SELECT end_date FROM parameters))) AS date
                            FROM TABLE(GENERATOR(ROWCOUNT => {num_days}))
                        ), full_year_calender_per_company as (
                            select distinct *
                            from date_range
                            cross join (select distinct company from holiday_src)
                        )
                        select fyc.company,
                            fyc.date as tagesdatum,
                            CASE WHEN DAYNAME(fyc.date) in ('Sat','Sun') THEN FALSE
                                WHEN s.company IS NOT NULL THEN FALSE ELSE TRUE END AS ist_werktag,
                                LISTAGG(s.rmrks, '|') WITHIN GROUP (ORDER BY s.rmrks) AS rmrks
                        from full_year_calender_per_company fyc
                        left join holiday_src s on s.company = fyc.company
                            and fyc.date between s.strdate and s.enddate
                        group by all
                        order by fyc.date desc;"""
        source_cursor.execute(source_sql)

        # transform  and stage the source rows into stage
        source_rows = source_cursor.fetchall()

        stage_data_rows = []


        for source_row in source_rows:
            stage_data_row_temp = dict(stage_data_row)

            stage_data_row_temp['company'] = source_row[0]
            stage_data_row_temp['tagesdatum'] = source_row[1]
            stage_data_row_temp['ist_werktag'] = source_row[2]
            stage_data_row_temp['rmrks'] = source_row[3]


            ### hashes
            stage_data_row_temp['hk_rgnr_gesellschaft'] = dvf_assemble_datavault_hash([stage_data_row_temp['company']])
            stage_data_row_temp['lk_rgnr_gesellschaft_kalender'] = dvf_assemble_datavault_hash([stage_data_row_temp['tagesdatum'],stage_data_row_temp['company']])
            stage_data_row_temp['rh_bgnr_gesellschaft_kalender_p1_b10_sat'] = dvf_assemble_datavault_hash([stage_data_row_temp['ist_werktag'],
                                                                                                       stage_data_row_temp['rmrks']])

            stage_data_rows.append(stage_data_row_temp)
            my_job_instance.count_input(1)

        execute_snf_dict_bulk_insert(dwh_cursor, insert_statement, stage_data_rows)
        dwh_connection.commit()

    # ### FRAMEWORK PHASE: Log any exception to instance
    except Exception:
        my_job_instance.end_instance_with_error(1, 'something went wrong')
        raise

    # # ### FRAMEWORK PHASE: End the instance normally
    my_job_instance.end_instance()


@cimtjobinstance_job
def load_data_to_vault(parent_job_instance, dwh_connection, file_name=None, **kwargs):


    # ### FRAMEWORK PHASE: setup your job_instance
    my_job_instance = CimtJobInstance(kwargs['instance_job_name'], parent_job_instance)
    my_job_instance.set_work_item(file_name)
    my_job_instance.start_instance()
    try:

        # ### FRAMEWORK PHASE: do processing here
        # BEGIN LOAD DATA TO VLT PART
        # general constants
        stage_schema = 'stage_bvlt'
        stage_table = 'bgnr_kalender_p1_stage'


        dwh_cursor = dwh_connection.cursor()


        hash_collision_check_statement_list = dvf_get_check_hash_collision_hub_elt_sql(vault_table='rgnr_gesellschaft_hub',
        vault_schema='rvlt_general',
        stage_hk_column='HK_RGNR_GESELLSCHAFT',
        stage_bk_column_list=['COMPANY'],
        db_connection=dwh_connection,
        stage_schema=stage_schema,
        stage_table=stage_table)

        dvf_execute_elt_statement_list(dwh_cursor, hash_collision_check_statement_list)

        statement_list = dvf_get_datavault_hub_elt_sql(vault_table='rgnr_gesellschaft_hub',
        vault_schema='rvlt_general',
        stage_hk_column='HK_RGNR_GESELLSCHAFT',
        stage_bk_column_list=['COMPANY'],
        db_connection=dwh_connection,
        stage_schema=stage_schema,
        stage_table=stage_table,
        meta_job_instance_id = my_job_instance.get_job_instance_id(),
        meta_inserted_at = my_job_instance.get_job_started_at() )

        dvf_execute_elt_statement_list(dwh_cursor, statement_list)

        # ----------


        hash_collision_check_statement_list = dvf_get_check_hash_collision_dlnk_elt_sql(vault_table='rgnr_gesellschaft_kalender_dlnk',
        vault_schema='rvlt_general',
        stage_lk_column='LK_RGNR_GESELLSCHAFT_KALENDER',
        stage_hk_column_list=['HK_RGNR_GESELLSCHAFT'],
        stage_dc_column_list=['TAGESDATUM'],
        db_connection=dwh_connection,
        stage_schema=stage_schema,
        stage_table=stage_table)

        dvf_execute_elt_statement_list(dwh_cursor, hash_collision_check_statement_list)

        statement_list = dvf_get_datavault_dlnk_elt_sql(vault_table='rgnr_gesellschaft_kalender_dlnk',
        vault_schema='rvlt_general',
        stage_lk_column='LK_RGNR_GESELLSCHAFT_KALENDER',
        stage_hk_column_list=['HK_RGNR_GESELLSCHAFT'],
        stage_dc_column_list=['TAGESDATUM'],
        db_connection=dwh_connection,
        stage_schema=stage_schema,
        stage_table=stage_table,
        meta_job_instance_id = my_job_instance.get_job_instance_id(),
        meta_inserted_at = my_job_instance.get_job_started_at() )

        dvf_execute_elt_statement_list(dwh_cursor, statement_list)

        # ----------


        singularity_check_statement_list = dvf_get_check_singularity_sat_elt_sql(vault_table='bgnr_gesellschaft_kalender_p1_b10_sat',
        stage_hk_column='LK_RGNR_GESELLSCHAFT_KALENDER',
        stage_rh_column='RH_BGNR_GESELLSCHAFT_KALENDER_P1_B10_SAT',
        stage_schema=stage_schema,
        stage_table=stage_table )

        dvf_execute_elt_statement_list(dwh_cursor, singularity_check_statement_list)

        statement_list = dvf_get_datavault_sat_elt_sql(vault_table='bgnr_gesellschaft_kalender_p1_b10_sat',
        vault_schema='bvlt_general',
        stage_hk_column='LK_RGNR_GESELLSCHAFT_KALENDER',
        stage_rh_column='RH_BGNR_GESELLSCHAFT_KALENDER_P1_B10_SAT',
        stage_content_column_list=['RMRKS', 'IST_WERKTAG'],
        with_deletion_detection=False,
        db_connection=dwh_connection,
        stage_schema=stage_schema,
        stage_table=stage_table,
        meta_job_instance_id = my_job_instance.get_job_instance_id(),
        meta_inserted_at = my_job_instance.get_job_started_at() )

        dvf_execute_elt_statement_list(dwh_cursor, statement_list)

        # ----------


        # END LOAD DATA TO VLT PART

        dwh_connection.commit()
    # ### FRAMEWORK PHASE: Log any exception to instance
    except Exception:
        my_job_instance.end_instance_with_error(1, 'something went wrong')
        raise
    # # ### FRAMEWORK PHASE: End the instance normally
    my_job_instance.end_instance()



@cimtjobinstance_job
def load_vault_1(parent_job_instance=None, **kwargs):

    # ### FRAMEWORK PHASE: setup your job_instance
    my_job_instance = CimtJobInstance(kwargs['instance_job_name'], parent_job_instance)
    #my_job_instance.set_work_item(file_name)
    my_job_instance.start_instance()

    try:
        # ### FRAMEWORK PHASE: do processing here
        dwh_connection = connection_snf_for_dwh_connection_type(Dvf_dwh_connection_type.raw_vault)
        fetch_and_stage_data(my_job_instance, dwh_connection)
        load_data_to_vault(my_job_instance, dwh_connection)
        dwh_connection.close()

    # ### FRAMEWORK PHASE: Log any exception to instance
    except Exception:
        my_job_instance.end_instance_with_error(1, 'some processing failed')
        raise

    # # ### FRAMEWORK PHASE: End the instance normally
    my_job_instance.end_instance()


if __name__ == '__main__':
    # local execution
    load_vault_1()