{"dvpd_version": "0.6.2", "stage_properties": [{"stage_schema": "stage_rvlt", "stage_table_name": "rsap_oitb_j1_stage"}], "pipeline_name": "rsap_oitb_j1", "record_source_name_expression": "sap.oitb", "data_extraction": {"fetch_module_name": "none - ddl generation only"}, "fields": [{"field_name": "company", "field_type": "VARCHAR(50)", "targets": [{"table_name": "RSAP_ITEM_GROUP_OITB_HUB"}]}, {"field_name": "itmsgrpcod", "field_type": "VARCHAR(20)", "targets": [{"table_name": "RSAP_ITEM_GROUP_OITB_HUB"}]}, {"field_name": "json_text", "field_type": "VARCHAR", "targets": [{"table_name": "RSAP_ITEM_GROUP_OITB_J1_L10_SAT"}]}], "data_vault_model": [{"schema_name": "rvlt_sap", "tables": [{"table_name": "RSAP_ITEM_GROUP_OITB_HUB", "table_stereotype": "hub", "hub_key_column_name": "HK_RSAP_ITEM_GROUP_OITB"}, {"table_name": "RSAP_ITEM_GROUP_OITB_J1_L10_SAT", "table_stereotype": "sat", "satellite_parent_table": "RSAP_ITEM_GROUP_OITB_HUB", "diff_hash_column_name": "RH_RSAP_ITEM_GROUP_OITB_J1_L10_SAT"}]}]}