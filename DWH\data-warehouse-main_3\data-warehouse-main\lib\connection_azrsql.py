import pyodbc
import sys
import time
from lib.configuration import configuration_read
from lib.dvf_basics import Dvf_dwh_connection_type


def error_print(*args, **kwargs):
    """Print a message to stderr"""
    print(*args, file=sys.stderr, **kwargs)

def connection_azrsql_for_dwh_connection_type(dwh_connection_type):
    return connection_azrsql(dwh_connection_type.value)

def connection_azrsql(object):
    """Reads configuration for the resource from environment and returns a new Azure sql connection"""
    connection = None
    system = 'dwh_azrsql'
    driver = '{ODBC Driver 18 for SQL Server}'
    server = None
    database = None
    user = None

    #first retry after 5 secs, the next one grows exponentially
    delay = 5
    max_delay = 120

    while delay < max_delay:
        try:
            params = configuration_read(system, object,['database','user','password','server'])
            server = params['server']
            database = params['database']
            user = params['user']
            password = params['password']
            conn_string = f'DRIVER={driver};SERVER={server};DATABASE={database};UID={user};PWD={password};Encrypt=yes'
            connection = pyodbc.connect(conn_string)
            break
        except pyodbc.Error as error:
            if error.args[0] == '08001' or error.args[0] == 'HYT00' or error.args[0] == 'HYT01': #if connection timeout error
                time.sleep(delay)
                error_print('Connection Error:', error)
                error_print('Object used:', object)
                delay *= 2
            else:
                raise
    return connection

if __name__ == '__main__':
    # small test
    conn = connection_azrsql_for_dwh_connection_type(Dvf_dwh_connection_type.metadata)
    conn_cursor = conn.cursor()
    output = conn_cursor.execute("Select * from metadata.job_instance_status")
    print(len(output.fetchall()))
    conn.close()