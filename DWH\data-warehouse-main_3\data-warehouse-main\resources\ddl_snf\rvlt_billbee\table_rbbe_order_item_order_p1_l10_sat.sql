-- generated script for rvlt_billbee.rbbe_order_item_order_p1_l10_sat

-- DROP TABLE rvlt_billbee.rbbe_order_item_order_p1_l10_sat;

CREATE TABLE rvlt_billbee.rbbe_order_item_order_p1_l10_sat (
MD_INSERTED_AT TIMESTAMP NOT NULL,
MD_RUN_ID INT NOT NULL,
MD_RECORD_SOURCE VARCHAR(255) NOT NULL,
MD_IS_DELETED BOOLEAN NOT NULL,
MD_VALID_BEFORE TIMESTAMP NOT NULL,
HK_RBBE_ORDER_ITEM CHAR(28) NOT NULL,
RH_RBBE_ORDER_ITEM_ORDER_P1_L10_SAT CHAR(28) NOT NULL,
QUANTITY NUMBER(20,2) NULL,
TOTALPRICE NUMBER(20,2) NULL,
TAXAMOUNT NUMBER(20,2) NULL,
TAXINDEX NUMBER(20,2) NULL,
DISCOUNT NUMBER(20,2) NULL,
UNREBATEDTOTALPRICE NUMBER(20,2) NULL,
INVOICESKU VARCHAR(200) NULL,
PRODUCT_TITLE VARCHAR(1000) NULL,
PRODUCT_SKU VARCHAR(200) NULL,
PRODUCT_EAN VARCHAR(200) NULL,
PRODUCT_COUNTRYOFORIGIN VARCHAR(200) NULL,
PRODUCT_TARICCODE VARCHAR(200) NULL,
LASTMODIFIEDAT TIMESTAMP NULL
);

--COMMENT STATEMENTS

-- end of script --