/* <PERSON><PERSON><PERSON>eb<PERSON> */
SELECT GESELLSCHAFT,
	VER<PERSON>UFSGEBIET,
	VERKAE<PERSON><PERSON><PERSON><PERSON>MM<PERSON>, 
	KUNDENGRUPPE,
	year(TAGESDATUM) JAHR,
	round(sum(GEPLANTER_UMSATZ_EUR),0) GEPLANTER_UMSATZ_EUR
From MART_AUFTRAEGE.F_UMSATZPLANZAHL
group by 1,2,3,4,5
order by 1,2,3,4,5;


/* <PERSON><PERSON><PERSON><PERSON> */
SELECT GESELLSCHAFT,
	VERKAUFSGEBIET,
	VERKAEUFERNUMMER, 
	KUNDENGRUPPE,
	year(TAGESDATUM) JAHR,
	month(TAGESDATUM) MONAT,
	round(sum(GEPLANTER_UMSATZ_EUR),0) GEPLANTER_UMSATZ_EUR
From MART_AUFTRAEGE.F_UMSATZPLANZAHL
group by 1,2,3,4,5,6
order by 1,2,3,4,5,6;
