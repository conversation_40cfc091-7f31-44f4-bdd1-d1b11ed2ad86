"""

This is only to provide data in the demo source table

"""
import random

from lib.connection_pg import  connection_pg_for_dwh_connection_type
from lib.dvf_basics import  Dvf_dwh_connection_type
from lib.dbutils_pg import get_psycop2_dict_insert_sql, execute_psycop2_dict_insert

from lib.cimtjobinstance import CimtJobInstance, cimtjobinstance_job

@cimtjobinstance_job
def mock_data(number_of_rows, offset, parent_job_instance=None, **kwargs):
    my_job_instance = CimtJobInstance(kwargs['instance_job_name'], parent_job_instance)
    my_job_instance.start_instance()
    try:
        db_connection = connection_pg_for_dwh_connection_type(Dvf_dwh_connection_type.owner)
        db_cursor = db_connection.cursor()
        target_schema="zz_source_demo"
        target_table="aaa"

        db_cursor.execute(f"TRUNCATE TABLE {target_schema}.{target_table}")

        insert_statement = get_psycop2_dict_insert_sql(db_connection, target_schema, target_table)
        data_row = {
                    }

        random.seed(0)

        c1_choice=['mmmmm','ooooo','ppppp','rrrrr','sssss','ttttt']
        c2_choice='abcdefghijklmnopqrsztuvxyzabcdefg'

        for i in range(number_of_rows):
                # mock busniesskeys of template hub
                data_row['aaa_bk1'] = mock_aaa_bk1(i)
                data_row['aaa_bk2f'] = mock_aaa_bk2f(i)
                data_row['bbb_bk1f'] = mock_bbb_bk1f(i)

                base_vector1=(random.randrange(0,10000)+offset)%10000

                c1_vector= int((base_vector1/500)) % len(c1_choice)
                data_row['aaa_p1_c1']=c1_choice[c1_vector]

                c2_vector= int(base_vector1/188) % 26
                c2_length= (int(base_vector1/321) % 5)+4
                data_row['aaa_p1_c2']=c2_choice[c2_vector:c2_vector+c2_length]

                data_row['aaa_p1_c3']=15+int(base_vector1/2317)%15
                data_row['aaa_p1_c4f']=(int(base_vector1/1232)%2)+1

                # write mocked data to source data table
                execute_psycop2_dict_insert(db_cursor, insert_statement, data_row)

        db_connection.commit()
        db_connection.close()
    # ### FRAMEWORK PHASE: Log any exception to instance
    except Exception:
        my_job_instance.end_instance_with_error(1, 'something went wrong')
        raise

    # # ### FRAMEWORK PHASE: End the instance normally
    my_job_instance.end_instance()


def mock_aaa_bk1(i: int) -> int:
    return int(i % 7)+1  # walk through numbers from 1 to 7

def mock_aaa_bk2f(i: int) -> int:
    return i*39  # climb up an arbirtrary amount

def mock_bbb_bk1f(i: int) -> int:
    return 2000+i


if __name__ == '__main__':
    # local execution
    mock_data(200,0)
