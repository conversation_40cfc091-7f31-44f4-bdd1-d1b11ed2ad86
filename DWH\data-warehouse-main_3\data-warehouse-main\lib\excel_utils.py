from openpyxl import load_workbook
import json
import os
# Function to find a header cell by its value
def find_cell_by_value(sheet, search_value):
    for row in sheet.iter_rows(values_only=False):  # iter_rows returns cell objects if values_only=False
        for cell in row:
            if cell.value == search_value:
                return cell.row, cell.column
    return None, None  # Return None if header not found

def find_position_of_first_value_in_first_row(worksheet):
    for cell in worksheet[1]:  # worksheet[1] accesses the first row
        if cell.value is not None:
            return cell.column

def get_headers(worksheet, min_row=None, max_row=None, min_col=None, max_col=None):
    headers = []
    for row in worksheet.iter_rows(min_row=min_row, max_row=max_row, min_col=min_col, max_col=max_col):
        for cell in row:
            row_data = cell.value
            headers.append(row_data)
    return headers

def get_subgrouping_data(worksheet, min_row=None, max_row=None, min_col=None, max_col=None):
    subgrouping_data = []
    for row in worksheet.iter_rows(min_row=min_row, max_row=max_row, min_col=min_col, max_col=max_col):
        row_data = [cell.value for cell in row]
        subgrouping_data.append(row_data)
    return subgrouping_data

def get_plan_numbers_data(worksheet, min_row=None, max_row=None, min_col=None, max_col=None):
    plan_numbers = []
    for row in worksheet.iter_rows(min_row=min_row, max_row=max_row, min_col=min_col, max_col=max_col):
        row_data = [cell.value for cell in row]
        plan_numbers.append(row_data)
    return plan_numbers

def get_dimensions(worksheet, min_row=None, max_row=None, min_col=None, max_col=None):
    dimensions = []
    for row in worksheet.iter_rows(min_row=min_row, max_row=max_row, min_col=min_col, max_col=max_col):
        row_data = [cell.value for cell in row]
        dimensions.append(row_data)
    return dimensions

def get_yearly_plan_numbers_dict(dimensions, plan_numbers, subgrouping_data):
    output_rows = []
    for company in range(len(dimensions)):
        for plan_group in range(len(plan_numbers[company])):
            output_row = dict()
            output_row['jahr'] = dimensions[company][0]
            output_row['company'] = dimensions[company][1]
            output_row['planwert'] = plan_numbers[company][plan_group]
            if len(subgrouping_data) != 0:
                output_row['untergruppierung '] = json.dumps({g[0].lower(): g[plan_group + 1] for g in subgrouping_data})
            output_rows.append(output_row)
    return output_rows

def get_monthly_plan_numbers_dict(dimensions, plan_numbers, subgrouping_data):
    output_rows = []
    for month in range(len(dimensions)):
        for plan_group in range(len(plan_numbers[month])):
            output_row = dict()
            output_row['jahr'] = dimensions[month][0]
            output_row['monat'] = dimensions[month][1]
            output_row['company'] = dimensions[month][2]
            output_row['planwert'] = plan_numbers[month][plan_group]
            if len(subgrouping_data) != 0:
                output_row['untergruppierung'] = json.dumps({g[0].lower(): g[plan_group + 1] for g in subgrouping_data})
            output_rows.append(output_row)
    return output_rows

def get_plan_numbers_dict(headers, dimensions, plan_numbers, subgrouping_data):
    output_rows = []
    #for dimension in range(len(dimensions)):
    for i_dim, dimension in enumerate(dimensions):
        #for plan_number_index in range(len(plan_numbers[i_dim])):
        for i_plan, plan_number in enumerate(plan_numbers[i_dim]):
            output_row = dict()
            for i, h in enumerate(headers):
                output_row[h] = dimension[i]
            output_row['planwert'] = plan_number
            #plan_numbers[i_dim][i_plan]
            if len(subgrouping_data) != 0:
                output_row['untergruppe_json'] = json.dumps({g[0].lower(): g[i_plan + 1] for g in subgrouping_data})
            output_rows.append(output_row)
    return output_rows

def get_plan_numbers_from_excel(excel_file):
    workbook = load_workbook(excel_file)
    worksheet = workbook.worksheets[0] #access the first sheet

    # find the position of "Jahr" to know where data starts
    first_row, first_col = find_cell_by_value(worksheet, "Jahr")
    # find the position of the planned numbers
    plan_number_row, plan_number_col = find_cell_by_value(worksheet, "Planwert")
    # find which first column in the first row has a value, important for "untergruppe_json"
    first_value_first_row_col = find_position_of_first_value_in_first_row(worksheet)

    try:
        #get dimensions (jahr,(monat), firma) data
        dimensions = get_dimensions(worksheet, min_row=first_row + 1, max_row=worksheet.max_row, min_col=first_col, max_col=plan_number_col-1)
        #get planned numbers data
        plan_numbers = get_plan_numbers_data(worksheet, min_row=plan_number_row + 1, max_row=worksheet.max_row, min_col=plan_number_col)
        #get headers names (jahr,(monat), firma)
        headers = get_headers(worksheet, min_row=first_row, max_row=first_row, min_col=first_col, max_col=plan_number_col-1)
        #get data for "untergruppe_json"
        subgrouping_data = get_subgrouping_data(worksheet, min_row=1, max_row=first_row-1, min_col=first_value_first_row_col, max_col=first_col-1) if first_row != 1 else []

        output_rows = get_plan_numbers_dict(headers, dimensions, plan_numbers, subgrouping_data)
    except Exception as e:
        raise e

    return output_rows, headers



if __name__ == '__main__':
    file = None # add path
    source_rows = get_plan_numbers_from_excel(file)
