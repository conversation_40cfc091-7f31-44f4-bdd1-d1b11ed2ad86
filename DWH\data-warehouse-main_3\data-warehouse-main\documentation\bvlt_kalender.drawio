<mxfile host="Electron" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/24.7.17 Chrome/128.0.6613.36 Electron/32.0.1 Safari/537.36" version="24.7.17">
  <diagram name="Seite-1" id="AUpRBTa56OUczgRDtuuW">
    <mxGraphModel dx="1372" dy="884" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1169" pageHeight="827" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="0MrAooonQZi-3dDYej8w-10" value="Schema" style="" parent="0" />
        <mxCell id="0MrAooonQZi-3dDYej8w-12" value="rvlt_general (rgnr)" style="rounded=0;whiteSpace=wrap;html=1;align=left;verticalAlign=top;fillColor=none;" parent="0MrAooonQZi-3dDYej8w-10" vertex="1">
          <mxGeometry x="170" y="290" width="580" height="100" as="geometry" />
        </mxCell>
        <mxCell id="0MrAooonQZi-3dDYej8w-26" value="bvlt_general (bgnr)" style="rounded=0;whiteSpace=wrap;strokeColor=none;strokeWidth=2;fillColor=#E6E6E6;fontFamily=Helvetica;fontSize=11;fontColor=default;labelBackgroundColor=none;verticalAlign=bottom;align=left;" parent="0MrAooonQZi-3dDYej8w-10" vertex="1">
          <mxGeometry x="400" y="426" width="350" height="100" as="geometry" />
        </mxCell>
        <mxCell id="SBj07qDLgGWxCxRsHNQa-18" value="&lt;span style=&quot;font-size: 11px; text-align: center; background-color: rgb(255, 255, 255);&quot;&gt;rvlt_sap (rsap)&lt;/span&gt;" style="rounded=0;whiteSpace=wrap;html=1;align=left;verticalAlign=top;fillColor=none;" parent="0MrAooonQZi-3dDYej8w-10" vertex="1">
          <mxGeometry x="170" y="170" width="580" height="100" as="geometry" />
        </mxCell>
        <mxCell id="stfk9wxAbCaLNZUYsJFp-1" style="edgeStyle=orthogonalEdgeStyle;shape=connector;rounded=1;jumpStyle=none;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=default;align=center;verticalAlign=middle;fontFamily=Helvetica;fontSize=11;fontColor=default;labelBackgroundColor=default;endArrow=none;endFill=0;" edge="1" parent="0MrAooonQZi-3dDYej8w-10" source="stfk9wxAbCaLNZUYsJFp-2" target="stfk9wxAbCaLNZUYsJFp-3">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="stfk9wxAbCaLNZUYsJFp-2" value="feiertage_hld1&#xa;(company,hldCode)" style="strokeWidth=2;shape=mxgraph.flowchart.start_1;whiteSpace=wrap;strokeColor=none;fontColor=#ffffff;fillColor=#002C82;fontFamily=Helvetica;fontSize=11;" vertex="1" parent="0MrAooonQZi-3dDYej8w-10">
          <mxGeometry x="260" y="190" width="110" height="60" as="geometry" />
        </mxCell>
        <mxCell id="stfk9wxAbCaLNZUYsJFp-3" value="feiertage_hld1_p1_l20" style="rounded=1;whiteSpace=wrap;html=1;strokeColor=#36393d;fillColor=#FFCF5E;strokeWidth=2;align=center;verticalAlign=middle;fontFamily=Helvetica;fontSize=11;fontColor=default;labelBackgroundColor=none;dashed=1;" vertex="1" parent="0MrAooonQZi-3dDYej8w-10">
          <mxGeometry x="410" y="205" width="190" height="30" as="geometry" />
        </mxCell>
        <mxCell id="0MrAooonQZi-3dDYej8w-11" value="Quelle" parent="0" />
        <mxCell id="1" value="Modell" parent="0" />
        <mxCell id="0MrAooonQZi-3dDYej8w-2" value="gesellschaft&#xa;(company)" style="strokeWidth=2;shape=mxgraph.flowchart.start_1;whiteSpace=wrap;strokeColor=none;fontColor=#ffffff;fillColor=#002C82;fontFamily=Helvetica;fontSize=11;" parent="1" vertex="1">
          <mxGeometry x="260" y="320" width="110" height="50" as="geometry" />
        </mxCell>
        <mxCell id="0MrAooonQZi-3dDYej8w-43" style="edgeStyle=orthogonalEdgeStyle;shape=connector;rounded=1;jumpStyle=none;orthogonalLoop=1;jettySize=auto;html=1;opacity=40;strokeColor=default;strokeWidth=2;align=center;verticalAlign=middle;fontFamily=Helvetica;fontSize=11;fontColor=default;labelBackgroundColor=default;endArrow=none;endFill=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="0MrAooonQZi-3dDYej8w-44" target="0MrAooonQZi-3dDYej8w-45" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="0MrAooonQZi-3dDYej8w-44" value="gesellschaft_kalender&lt;div&gt;(tagesdatum)&lt;/div&gt;" style="verticalLabelPosition=middle;verticalAlign=middle;html=1;shape=hexagon;perimeter=hexagonPerimeter2;arcSize=6;size=0.16666666666666666;fillColor=#cce5ff;strokeColor=#36393d;strokeWidth=2;labelPosition=center;align=center;fontFamily=Helvetica;fontSize=11;fontColor=default;labelBackgroundColor=none;" parent="1" vertex="1">
          <mxGeometry x="400" y="320" width="160" height="50" as="geometry" />
        </mxCell>
        <mxCell id="0MrAooonQZi-3dDYej8w-45" value="gesellschaft_kalender_p1_b10" style="rounded=1;whiteSpace=wrap;html=1;strokeColor=#36393d;fillColor=#FFF3A8;strokeWidth=2;" parent="1" vertex="1">
          <mxGeometry x="540" y="480" width="180" height="30" as="geometry" />
        </mxCell>
        <mxCell id="0MrAooonQZi-3dDYej8w-46" style="edgeStyle=orthogonalEdgeStyle;shape=connector;rounded=1;jumpStyle=none;orthogonalLoop=1;jettySize=auto;html=1;opacity=40;strokeColor=default;strokeWidth=2;align=center;verticalAlign=middle;fontFamily=Helvetica;fontSize=11;fontColor=default;labelBackgroundColor=default;endArrow=none;endFill=0;" parent="1" source="0MrAooonQZi-3dDYej8w-2" target="0MrAooonQZi-3dDYej8w-44" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="880" y="185" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="SBj07qDLgGWxCxRsHNQa-14" style="edgeStyle=orthogonalEdgeStyle;shape=connector;rounded=1;jumpStyle=none;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=default;align=center;verticalAlign=middle;fontFamily=Helvetica;fontSize=11;fontColor=default;labelBackgroundColor=default;endArrow=none;endFill=0;exitX=0;exitY=0.5;exitDx=0;exitDy=0;" parent="1" target="SBj07qDLgGWxCxRsHNQa-12" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="590" y="140" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="SBj07qDLgGWxCxRsHNQa-16" style="edgeStyle=orthogonalEdgeStyle;shape=connector;rounded=1;jumpStyle=none;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=default;align=center;verticalAlign=middle;fontFamily=Helvetica;fontSize=11;fontColor=default;labelBackgroundColor=default;endArrow=none;endFill=0;elbow=vertical;exitX=0;exitY=0.5;exitDx=0;exitDy=0;" parent="1" target="SBj07qDLgGWxCxRsHNQa-13" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="590" y="225" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="0MrAooonQZi-3dDYej8w-30" value="datenfluss" parent="0" />
        <mxCell id="SBj07qDLgGWxCxRsHNQa-25" style="shape=connector;rounded=1;jumpStyle=none;orthogonalLoop=1;jettySize=auto;html=1;dashed=1;dashPattern=1 1;strokeColor=#969696;strokeWidth=2;align=center;verticalAlign=middle;fontFamily=Helvetica;fontSize=11;fontColor=#C2C2C2;labelBackgroundColor=default;endArrow=classicThin;endFill=1;" parent="0MrAooonQZi-3dDYej8w-30" source="SBj07qDLgGWxCxRsHNQa-21" target="0MrAooonQZi-3dDYej8w-45" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="SBj07qDLgGWxCxRsHNQa-21" value="" style="shape=parallelogram;perimeter=parallelogramPerimeter;whiteSpace=wrap;html=1;fixedSize=1;fontColor=#C2C2C2;strokeColor=#000000;strokeWidth=1;fillColor=#E0E0E0;" parent="0MrAooonQZi-3dDYej8w-30" vertex="1">
          <mxGeometry x="590" y="400" width="80" height="50" as="geometry" />
        </mxCell>
        <mxCell id="SBj07qDLgGWxCxRsHNQa-23" style="edgeStyle=orthogonalEdgeStyle;shape=connector;curved=1;rounded=1;jumpStyle=none;orthogonalLoop=1;jettySize=auto;html=1;dashed=1;dashPattern=1 1;strokeColor=#969696;strokeWidth=2;align=center;verticalAlign=middle;fontFamily=Helvetica;fontSize=11;fontColor=#C2C2C2;labelBackgroundColor=default;endArrow=classicThin;endFill=1;entryX=0.75;entryY=0;entryDx=0;entryDy=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;" parent="0MrAooonQZi-3dDYej8w-30" source="stfk9wxAbCaLNZUYsJFp-3" target="SBj07qDLgGWxCxRsHNQa-21" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="800" y="410" as="targetPoint" />
            <mxPoint x="760" y="140" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
