<!DOCTYPE html>
<html>
<head>
<style>
table, th, td {border: 1px solid;}
table {border-collapse: collapse;}
body { font-family: Verdana, Geneva, Tahoma, sans-serif;}</style>
   <title>rbbe_ORDER_P1</title>
</head>
<body>
<h1>Pipeline: rbbe_ORDER_P1
</h1><p>Record source: billbee.order
</p><p>Json loop path: $.Data
</p>   <table>
       <tr>
           <th>field</th>
           <th>type</th>
           <th>mapping</th>
       </tr>
       <tr>
           <td>BillBeeOrderId</td>
           <td>NUMBER(36,0)</td>
           <td>RBBE_ORDER_HUB</td>
       </tr>
       <tr>
           <td>BillBeeParentOrderId</td>
           <td>NUMBER(36,0)</td>
           <td>RBBE_ORDER_HUB.[BillBeeOrderId] (relation: parent)</td>
       </tr>
       <tr>
           <td>Customer.Id</td>
           <td>NUMBER(36,0)</td>
           <td>RBBE_CUSTOMER_HUB.[ID]</td>
       </tr>
       <tr>
           <td>Seller.BillbeeShopId</td>
           <td>NUMBER(36,0)</td>
           <td>RBBE_SHOP_HUB.[BILLBEESHOPID]</td>
       </tr>
       <tr>
           <td>OrderNumber</td>
           <td>VARCHAR(200)</td>
           <td>RBBE_ORDER_ORDER_P1_L10_SAT</td>
       </tr>
       <tr>
           <td>TotalCost</td>
           <td>NUMBER(20,2)</td>
           <td>RBBE_ORDER_ORDER_P1_L10_SAT</td>
       </tr>
       <tr>
           <td>AdjustmentCost</td>
           <td>NUMBER(20,2)</td>
           <td>RBBE_ORDER_ORDER_P1_L10_SAT</td>
       </tr>
       <tr>
           <td>Currency</td>
           <td>VARCHAR(200)</td>
           <td>RBBE_ORDER_ORDER_P1_L10_SAT</td>
       </tr>
       <tr>
           <td>ShippingCost</td>
           <td>NUMBER(20,2)</td>
           <td>RBBE_ORDER_ORDER_P1_L10_SAT</td>
       </tr>
       <tr>
           <td>InvoiceAddress.CountryISO2</td>
           <td>VARCHAR(20)</td>
           <td>RBBE_ORDER_ORDER_P1_L10_SAT.[INVOICEADDRESS_COUNTRYISO2]</td>
       </tr>
       <tr>
           <td>State</td>
           <td>NUMBER(36,0)</td>
           <td>RBBE_ORDER_ORDER_P2_L10_SAT</td>
       </tr>
       <tr>
           <td>CreatedAt</td>
           <td>TIMESTAMP</td>
           <td>RBBE_ORDER_ORDER_P2_L10_SAT</td>
       </tr>
       <tr>
           <td>ShippedAt</td>
           <td>TIMESTAMP</td>
           <td>RBBE_ORDER_ORDER_P2_L10_SAT</td>
       </tr>
       <tr>
           <td>ConfirmedAt</td>
           <td>TIMESTAMP</td>
           <td>RBBE_ORDER_ORDER_P2_L10_SAT</td>
       </tr>
       <tr>
           <td>InvoiceDate</td>
           <td>TIMESTAMP</td>
           <td>RBBE_ORDER_ORDER_P2_L10_SAT</td>
       </tr>
       <tr>
           <td>PayedAt</td>
           <td>TIMESTAMP</td>
           <td>RBBE_ORDER_ORDER_P2_L10_SAT</td>
       </tr>
       <tr>
           <td>UpdatedAt</td>
           <td>TIMESTAMP</td>
               <td>RBBE_ORDER_ORDER_P1_L10_SAT (not in comparison),<br/> RBBE_ORDER_ORDER_P2_L10_SAT (not in comparison)</td>
       </tr>
       <tr>
           <td>LastModifiedAt</td>
           <td>TIMESTAMP</td>
               <td>RBBE_ORDER_ORDER_P1_L10_SAT (not in comparison),<br/> RBBE_ORDER_ORDER_P2_L10_SAT (not in comparison)</td>
       </tr>
       <tr>
           <td>Seller.BillbeeShopName</td>
           <td>VARCHAR(200)</td>
           <td>RBBE_ORDER_SHOP_P1_L10_SAT.[SELLER_BILLBEESHOPNAME]</td>
       </tr>
   </table>
</body>
</html>