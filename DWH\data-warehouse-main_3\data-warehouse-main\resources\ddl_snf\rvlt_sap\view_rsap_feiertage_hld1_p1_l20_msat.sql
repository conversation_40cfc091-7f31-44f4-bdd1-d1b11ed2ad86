CREATE VIEW rvlt_sap.rsap_feiertage_hld1_p1_l20_msat (
    MD_INSERTED_AT,
	MD_RUN_ID,
	MD_RECORD_SOURCE,
    MD_IS_DELETED,
    MD_VALID_BEFORE,
    HK_RSAP_FEIERTAGE_HLD1,
    GH_RSAP_FEIERTAGE_HLD1_J1_L10_MSAT,
    STRDATE,
    ENDDATE,
    RMRKS
)
AS
SELECT MD_INSERTED_AT,
	MD_RUN_ID,
	MD_RECORD_SOURCE,
    MD_IS_DELETED,
    MD_VALID_BEFORE,
    HK_RSAP_FEIERTAGE_HLD1,
    GH_RSAP_FEIERTAGE_HLD1_J1_L10_MSAT,
    TO_DATE(JSON_EXTRACT_PATH_TEXT(JSON_TEXT, 'StrDate')) as STRDATE,
    TO_DATE(JSON_EXTRACT_PATH_TEXT(JSON_TEXT, 'EndDate')) as ENDDA<PERSON>,
    JSO<PERSON>_EXTRACT_PATH_TEXT(JSON_TEXT, 'Rmrks') as R<PERSON><PERSON>,
FROM rsap_feiertage_hld1_j1_l10_msat;