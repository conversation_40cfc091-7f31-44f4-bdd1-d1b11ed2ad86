/* This script implements necessary Grants to schemas and their default setting for new objects
 * for all users and roles of the data vault.
 * Must be executed as "DV_D_OWNER_DATABASE"
 * It will be needed...
     * after adding a new role or technical user
     * after adding a new schema, must be executed before adding objects to the schema 
     * to repair access problems  
 * In case of existing objects in schemas this will only provide the initial acess to the schema. 
 * Access to existing objects will be granted with the "grant_existing_objects.sql" script
 * Keep in mind, that the final statment generates the necessary grant statments, wich then have
 * to be executed. 
 */

/* 
 * Direct Execution  Part
 */

-- DV_D_ACCESS_WRITE raw vault
--
GRANT USAGE ON SCHEMA stage_rvlt to DV_D_ACCESS_WRITE;
GRANT SELECT, INSERT, DELETE, UPDATE, TRUNCATE ON all TABLES IN SCHEMA stage_rvlt TO DV_D_ACCESS_WRITE;
GRANT SELECT ON all VIEWS IN SCHEMA stage_rvlt TO DV_D_ACCESS_WRITE;

--
-- DV_D_ACCESS_WRITE business vault
--
GRANT USAGE ON SCHEMA stage_bvlt to DV_D_ACCESS_WRITE;
GRANT SELECT, INSERT, DELETE, UPDATE, TRUNCATE ON all TABLES IN SCHEMA stage_bvlt TO DV_D_ACCESS_WRITE;
GRANT SELECT ON all VIEWS IN SCHEMA stage_bvlt TO DV_D_ACCESS_WRITE;
--

GRANT USAGE ON SCHEMA stage_rvlt to DV_D_ACCESS_VAULT_READ;
GRANT USAGE ON SCHEMA stage_bvlt to DV_D_ACCESS_VAULT_READ;
GRANT SELECT ON all TABLES IN SCHEMA stage_bvlt TO DV_D_ACCESS_VAULT_READ;
GRANT SELECT ON all VIEWS IN SCHEMA stage_bvlt TO DV_D_ACCESS_VAULT_READ;



GRANT USAGE ON SCHEMA lib to role DV_D_ACCESS_WRITE;
GRANT USAGE ON ALL FUNCTIONS in SCHEMA lib TO ROLE DV_D_ACCESS_WRITE;

