Data vault pipeline developer cheat sheet 
rendered from  rbbe_order_orderitem_p1.dvpi

pipeline name:  rbbe_order_orderitem_p1

------------------------------------------------------
record source:  billbee.order.orderItem

Json array path: $.Data.[*].orderItems

Source fields:
   -1. <PERSON><PERSON><PERSON><PERSON>rderId         ->       BILLBEEORDERID           NUMBER(36,0)
       BillBeeId              ->       BILLBEEID                NUMBER(36,0)
       Product.BillbeeId      ->       PRODUCT_BILLBEEID        NUMBER(36,0)
       Quantity               ->       QUANTITY                 NUMBER(20,2)
       TotalPrice             ->       TOTALPRICE               NUMBER(20,2)
       TaxAmount              ->       TAXAMOUNT                NUMBER(20,2)
       TaxIndex               ->       TAXINDEX                 NUMBER(20,2)
       Discount               ->       DISCOUNT                 NUMBER(20,2)
       UnrebatedTotalPrice    ->       UNREBATEDTOTALPRICE      NUMBER(20,2)
       InvoiceSKU             ->       INVOICESKU               VARCHAR(200)
       Product.Title          ->       PRODUCT_TITLE            VARCHAR(1000)
       Product.SKU            ->       PRODUCT_SKU              VARCHAR(200)
       Product.EAN            ->       PRODUCT_EAN              VARCHAR(200)
       Product.CountryOfOrigin->       PRODUCT_COUNTRYOFORIGIN  VARCHAR(200)
       Product.TARICCode      ->       PRODUCT_TARICCODE        VARCHAR(200)
   -1. LastModifiedAt         ->       LASTMODIFIEDAT           TIMESTAMP


------------------------------------------------------
Table List:
stage_table.rvlt_billbee.rbbe_order_orderitem_p1_stage
table.rvlt_billbee.rbbe_order_item_hub
table.rvlt_billbee.rbbe_order_item_order_p1_l10_sat
table.rvlt_billbee.rbbe_order_hub
table.rvlt_billbee.rbbe_order_item_order_lnk
table.rvlt_billbee.rbbe_order_item_order_esat
table.rvlt_billbee.rbbe_product_hub
table.rvlt_billbee.rbbe_order_item_product_lnk
table.rvlt_billbee.rbbe_order_item_product_esat

------------------------------------------------------
stage table:  stage_rvlt.rbbe_order_orderitem_p1_stage
Field to Stage mapping:
	--business keys,
		BILLBEEID                >  BILLBEEID,
		BILLBEEORDERID           >  BILLBEEORDERID,
		PRODUCT_BILLBEEID        >  PRODUCT_BILLBEEID,

	--content,
		DISCOUNT                 >  DISCOUNT,
		INVOICESKU               >  INVOICESKU,
		PRODUCT_COUNTRYOFORIGIN  >  PRODUCT_COUNTRYOFORIGIN,
		PRODUCT_EAN              >  PRODUCT_EAN,
		PRODUCT_SKU              >  PRODUCT_SKU,
		PRODUCT_TARICCODE        >  PRODUCT_TARICCODE,
		PRODUCT_TITLE            >  PRODUCT_TITLE,
		QUANTITY                 >  QUANTITY,
		TAXAMOUNT                >  TAXAMOUNT,
		TAXINDEX                 >  TAXINDEX,
		TOTALPRICE               >  TOTALPRICE,
		UNREBATEDTOTALPRICE      >  UNREBATEDTOTALPRICE,

	--content untracked,
		LASTMODIFIEDAT           >  LASTMODIFIEDAT

------------------------------------------------------
Hash value composition

HK_RBBE_ORDER_ITEM (key)
		BILLBEEID 

HK_RBBE_ORDER (key)
		BILLBEEORDERID 

HK_RBBE_PRODUCT (key)
		PRODUCT_BILLBEEID 

LK_RBBE_ORDER_ITEM_ORDER (key)
		BILLBEEID 
		BILLBEEORDERID 

LK_RBBE_ORDER_ITEM_PRODUCT (key)
		BILLBEEID 
		PRODUCT_BILLBEEID 

RH_RBBE_ORDER_ITEM_ORDER_P1_L10_SAT (diff_hash)
		DISCOUNT 
		INVOICESKU 
		PRODUCT_COUNTRYOFORIGIN 
		PRODUCT_EAN 
		PRODUCT_SKU 
		PRODUCT_TARICCODE 
		PRODUCT_TITLE 
		QUANTITY 
		TAXAMOUNT 
		TAXINDEX 
		TOTALPRICE 
		UNREBATEDTOTALPRICE 


------------------------------------------------------
Table load method
(STAGE >  VAULT)

rbbe_order_item_hub (/) can be loaded by convention
		  key: HK_RBBE_ORDER_ITEM  >  HK_RBBE_ORDER_ITEM
		  business_key: BILLBEEID  >  BILLBEEID 

rbbe_order_item_order_p1_l10_sat (/) can be loaded by convention
		  parent_key: HK_RBBE_ORDER_ITEM  >  HK_RBBE_ORDER_ITEM
		  diff_hash: RH_RBBE_ORDER_ITEM_ORDER_P1_L10_SAT  >  RH_RBBE_ORDER_ITEM_ORDER_P1_L10_SAT
		  content: QUANTITY  >  QUANTITY 
		  content: TOTALPRICE  >  TOTALPRICE 
		  content: TAXAMOUNT  >  TAXAMOUNT 
		  content: TAXINDEX  >  TAXINDEX 
		  content: DISCOUNT  >  DISCOUNT 
		  content: UNREBATEDTOTALPRICE  >  UNREBATEDTOTALPRICE 
		  content: INVOICESKU  >  INVOICESKU 
		  content: PRODUCT_TITLE  >  PRODUCT_TITLE 
		  content: PRODUCT_SKU  >  PRODUCT_SKU 
		  content: PRODUCT_EAN  >  PRODUCT_EAN 
		  content: PRODUCT_COUNTRYOFORIGIN  >  PRODUCT_COUNTRYOFORIGIN 
		  content: PRODUCT_TARICCODE  >  PRODUCT_TARICCODE 
		  content_untracked: LASTMODIFIEDAT  >  LASTMODIFIEDAT 

rbbe_order_hub (/) can be loaded by convention
		  key: HK_RBBE_ORDER  >  HK_RBBE_ORDER
		  business_key: BILLBEEORDERID  >  BILLBEEORDERID 

rbbe_order_item_order_lnk (/) can be loaded by convention
		  parent_key_1: HK_RBBE_ORDER_ITEM  >  HK_RBBE_ORDER_ITEM
		  parent_key_2: HK_RBBE_ORDER  >  HK_RBBE_ORDER
		  key: LK_RBBE_ORDER_ITEM_ORDER  >  LK_RBBE_ORDER_ITEM_ORDER

rbbe_order_item_order_esat (/) can be loaded by convention
		! Driving keys: HK_RBBE_ORDER_ITEM 
		  parent_key: LK_RBBE_ORDER_ITEM_ORDER  >  LK_RBBE_ORDER_ITEM_ORDER

rbbe_product_hub (/) needs explicit loading:
		  key: HK_RBBE_PRODUCT  >  HK_RBBE_PRODUCT
		* business_key: PRODUCT_BILLBEEID  >  BILLBEEID 

rbbe_order_item_product_lnk (/) can be loaded by convention
		  parent_key_1: HK_RBBE_ORDER_ITEM  >  HK_RBBE_ORDER_ITEM
		  parent_key_2: HK_RBBE_PRODUCT  >  HK_RBBE_PRODUCT
		  key: LK_RBBE_ORDER_ITEM_PRODUCT  >  LK_RBBE_ORDER_ITEM_PRODUCT

rbbe_order_item_product_esat (/) can be loaded by convention
		! Driving keys: HK_RBBE_ORDER_ITEM 
		  parent_key: LK_RBBE_ORDER_ITEM_PRODUCT  >  LK_RBBE_ORDER_ITEM_PRODUCT

