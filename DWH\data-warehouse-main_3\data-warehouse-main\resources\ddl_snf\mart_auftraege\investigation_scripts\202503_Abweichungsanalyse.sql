-- Tagessummen nach Gesellschaft
select tagesdatum , gesellschaft ,count(1) ,sum(umsatz)
from MART_AUFTRAEGE.f_umsatz_cube2024 f_umsatz
where f_umsatz.tagesdatum > '2024-12-01'
group by 2,1
order by 2,1


-- Tagessummen DEISS komplett
select tagesdatum , sum(umsatz)
from MART_AUFTRAEGE.f_umsatz
where f_umsatz.tagesdatum > '2024-12-01'
and gesellschaft like 'DEISS%'
group by 1
order by 1  

-- Monatssummen der gesellschaften
select  case when gesellschaft like 'DEISS%' THEN 'DEISS' ELSE gesellschaft end as gesellschaft
,year(f_umsatz.tagesdatum) jahr
,month(f_umsatz.tagesdatum) monat
,sum(umsatz_eur)
from MART_AUFTRAEGE.f_umsatz_cube2024 f_umsatz
where f_umsatz.tagesdatum >= '2023-01-01'
and  f_umsatz.tagesdatum < '2025-01-01'
group by 1,2,3
order by 1,2,3

-- Monatssummen der Verkäufer
select  
case when gesellschaft like 'DEISS%' and gesellschaft <> 'DEISS_BINGOLD' THEN 'DEISS' ELSE gesellschaft end as gesamt_gesellschaft 
,gesellschaft as gesellschaft 
,handelsplatz_aka_u_dim1_von_vertriebsmitarbeiter
,verkaeufernummer
,year(f_umsatz.tagesdatum) jahr
,month(f_umsatz.tagesdatum) monat
,sum(umsatz_eur)
from MART_AUFTRAEGE.f_umsatz_cube2024 f_umsatz
where f_umsatz.tagesdatum >= '2023-01-01'
and  f_umsatz.tagesdatum < '2025-01-01'
group by 1,2,3,4,5,6
--
order by 1,2,3,4,5,6


-- Monatssummen für SUND_DIGITAL
select  
case when gesellschaft like 'DEISS%' and gesellschaft <> 'DEISS_BINGOLD' THEN 'DEISS' ELSE gesellschaft end as gesamt_gesellschaft 
,gesellschaft as gesellschaft 
,kanal
,verkaeufernummer
,year(f_umsatz.tagesdatum) jahr
,month(f_umsatz.tagesdatum) monat
,sum(umsatz_eur)
from MART_AUFTRAEGE.f_umsatz_cube2024 f_umsatz
where f_umsatz.tagesdatum >= '2023-01-01'
and  f_umsatz.tagesdatum < '2025-04-01'
and gesellschaft ='SUND_DIGITAL'
group by 1,2,3,4,5,6
--
order by 1,2,3,4,5,6

-- Tagessummen für SUND_DIGITAL
select  
case when gesellschaft like 'DEISS%' and gesellschaft <> 'DEISS_BINGOLD' THEN 'DEISS' ELSE gesellschaft end as gesamt_gesellschaft 
,gesellschaft as gesellschaft 
,kanal
,verkaeufernummer
,f_umsatz.tagesdatum
,sum(umsatz_eur)
from MART_AUFTRAEGE.f_umsatz_cube2024 f_umsatz
where f_umsatz.tagesdatum >= '2024-12-01'
and f_umsatz.tagesdatum <= '2024-12-31'
and gesellschaft ='SUND_DIGITAL'
group by 1,2,3,4,5
--
order by 1,2,3,4,5


-- Monatssummen für SUND_DIGITAL
select  
year(f_umsatz.tagesdatum) jahr
,sum(umsatz_eur)
from MART_AUFTRAEGE.f_umsatz_cube2024 f_umsatz
where gesellschaft ='SUND_DIGITAL'
group by 1
--
order by 1

-- Detail Billbee
select 
	o_h.billbeeorderid, 
	o_s.ordernumber,
	o2_s.invoicedate ,
	o2_s.payedat ,
	o_s_s.seller_billbeeshopname, 
	oi_h.billbeeid  as order_item_billbee_id,
	oik_s.product_verteilschluessel,
	oik_s.totalprice_netto,
	oik_s.totalprice_netto_korrigiert,
	oi_s.taxamount ,
	o_s.adjustmentcost ,
	o_s.shippingcost,
	o_s.totalcost,
	o2_s.state 
from  BVLT_BILLBEE.BBBE_ORDER_ITEM_PRODUCT_KORRIGIERT_V1_B10_CURRENT_SAT oik_s
join RVLT_BILLBEE.RBBE_ORDER_ITEM_ORDER_P1_L10_SAT oi_s on oi_s.hk_rbbe_order_item  = oiK_s.hk_rbbe_order_item 
		and oi_s.md_valid_before = lib.dwh_far_future_date()
		and not oi_s.md_is_deleted 
join RVLT_BILLBEE.RBBE_ORDER_ITEM_ORDER_LNK oi_o_l on oi_o_l.hk_rbbe_order_item=oiK_s.hk_rbbe_order_item 
join RVLT_BILLBEE.RBBE_ORDER_ITEM_ORDER_esat oi_o_e on oi_o_e.lk_rbbe_order_item_order  = oi_o_l.lk_rbbe_order_item_order
		and oi_o_e.md_valid_before = lib.dwh_far_future_date()
		and not oi_o_e.md_is_deleted 
join RVLT_BILLBEE.RBBE_ORDER_ORDER_P1_L10_SAT o_s on o_s.hk_rbbe_order = oi_o_l.hk_rbbe_order
		and o_s.md_valid_before = lib.dwh_far_future_date()
		and not o_s.md_is_deleted 
join RVLT_BILLBEE.RBBE_ORDER_ORDER_P2_L10_SAT o2_s on o2_s.hk_rbbe_order = oi_o_l.hk_rbbe_order
		and o2_s.md_valid_before = lib.dwh_far_future_date()
		and not o2_s.md_is_deleted 
join RVLT_BILLBEE.RBBE_ORDER_HUB o_h  on o_h.hk_rbbe_order =oi_o_l.hk_rbbe_order
join RVLT_BILLBEE.RBBE_ORDER_ITEM_HUB oi_h using(hk_rbbe_order_item)
left join RVLT_BILLBEE.RBBE_ORDER_SHOP_LNK o_s_l on o_s_l.hk_rbbe_order =oi_o_l.hk_rbbe_order
left join RVLT_BILLBEE.RBBE_ORDER_SHOP_P1_L10_SAT o_s_s on o_s_s.lk_rbbe_order_shop   = o_s_l.lk_rbbe_order_shop
		and o_s_s.md_valid_before = lib.dwh_far_future_date()
		and not o_s_s.md_is_deleted 
--where o_s_s.seller_billbeeshopname ='RE:CIRCLE'		
--where o_s_s.seller_billbeeshopname ='Amazon'		
		

-- Count Histogramm über Gesellschaft, Dim1 , Verkäufer,Kundengruppe
with histogram_ungefiltert as (
select year(tagesdatum )jahr,gesellschaft ,
		case when gesellschaft = 'FIPP_DE' then kundengruppe 
		else concat(handelsplatz_aka_u_dim1_von_vertriebsmitarbeiter,'|' ,coalesce (verkaeufernummer,'#?#' ))
		end ordnungsgruppen
		, count(1) count_ungefiltert,sum(umsatz_eur ) umsatz_ungefiltert
from MART_AUFTRAEGE.f_umsatz_ungefiltert 
group by 1,2,3
)
,histogram_gefiltert as (
select year(tagesdatum )jahr ,gesellschaft ,
		case when gesellschaft = 'FIPP_DE' then kundengruppe 
		else concat(handelsplatz_aka_u_dim1_von_vertriebsmitarbeiter,'|' ,coalesce (verkaeufernummer,'#?#' ))
		end ordnungsgruppen
		, count(1) count_gefiltert,sum(umsatz_eur ) umsatz_gefiltert
from MART_AUFTRAEGE.f_umsatz_cube2024 f_umsatz
group by 1,2,3
)
select 
 jahr,gesellschaft,ordnungsgruppen,coalesce(count_gefiltert,0) count_gefiltert,
 coalesce(count_gefiltert,0)-coalesce(count_ungefiltert,0) count_diff,
 coalesce(umsatz_gefiltert,0)-coalesce(umsatz_ungefiltert,0) umstatz_diff
from histogram_ungefiltert ugf
full outer join histogram_gefiltert gf  using (jahr,gesellschaft,ordnungsgruppen)
order by 1,2,3

-- Erste und letzte Verkaufsdaten je Company und "Handschuh" Artikelgruppe
with artikelgruppen_handschuhe as (
	select company ,itmsgrpcod  
	from bvlt_sap.bsap_item_group_oitb_current_ref group_ref
	where lower(itmsgrpnam) like '%handschuhe%'
	order by company ,itmsgrpcod 
)
select gesellschaft ,artikelgruppe ,min(tagesdatum),max(tagesdatum)
from MART_AUFTRAEGE.f_umsatz_ungefiltert
where (gesellschaft ,artikelgruppe_code) in (select * from artikelgruppen_handschuhe)
group by 1,2
order by 1,2


-- Monatssummen FIPP_DE je Kundengruppe
select  
case when kundengruppe in('Budni','Edeka / Netto') THEN 'Edeka / Netto / Budni' 
	when kundengruppe like 'Rossman%' THEN 'Rossmann'	
	when kundengruppe = 'Lidl Ausland' THEN 'Lidl AL'
	when kundengruppe in('Hamburg','Heku','Diverse kein Umsatz') THEN 'Hamburg Diverse'
     ELSE kundengruppe  end as kundengruppe_final 
,d_geschaeftspartner.kundengruppe
,year(f_umsatz.tagesdatum) jahr
,month(f_umsatz.tagesdatum) monat
,sum(umsatz_eur)
,sum(f_umsatz.bestellpositionen_gezaehlt) bestellpositionen_gezaehlt
from MART_AUFTRAEGE.f_umsatz_cube2024 f_umsatz
join mart_auftraege.d_geschaeftspartner using(dk_geschaeftspartner)
where f_umsatz.tagesdatum >= '2023-01-01'
and  f_umsatz.tagesdatum < '2025-01-01'
and gesellschaft ='FIPP_DE'
group by 1,2,3,4
--
order by 1,2,3,4


-- Monatssummen der Verkäufer und Artikelgruppen
select  case when gesellschaft like 'DEISS%' THEN 'DEISS' ELSE gesellschaft end as gesamt_gesellschaft 
,gesellschaft as gesellschaft 
,handelsplatz_aka_u_dim1_von_vertriebsmitarbeiter
,verkaeufernummer
,artikelgruppe_code 
,year(f_umsatz.tagesdatum) jahr
,month(f_umsatz.tagesdatum) monat
,sum(umsatz_eur)
from MART_AUFTRAEGE.f_umsatz_cube2024 f_umsatz
where f_umsatz.tagesdatum >= '2023-01-01'
and  f_umsatz.tagesdatum < '2025-01-01'
group by 1,2,3,4,5,6,7
order by 1,2,3,4,5,6,7


-- Tagessummen der verkäufer
select  
gesellschaft 
,handelsplatz_aka_u_dim1_von_vertriebsmitarbeiter
,verkaeufernummer
,tagesdatum
,sum(umsatz_eur)
from MART_AUFTRAEGE.f_umsatz_cube2024 f_umsatz
where f_umsatz.tagesdatum > '2025-01-01'
and (gesellschaft <> 'DEISS_DE' OR (handelsplatz_aka_u_dim1_von_vertriebsmitarbeiter in ('GH','HGH','Fabrik')
and ARTIKELGRUPPE_CODE not in ('229','230')))
group by 1,2,3,4
order by 1,2,3,4


-- Tagessummen der FIPP Händler
select  
gesellschaft 
,kundengruppe 
,tagesdatum
,sum(umsatz_eur)
from MART_AUFTRAEGE.f_umsatz_cube2024 f_umsatz
where f_umsatz.tagesdatum > '2025-01-01'
and gesellschaft = 'FIPP_DE'
and ARTIKELGRUPPE_CODE not in ('229','230')
group by 1,2,3
order by 1,2,3

-- Tagesvergleich
select  
gesellschaft 
,handelsplatz_aka_u_dim1_von_vertriebsmitarbeiter
,verkaeufernummer
,artikelgruppe 
,tagesdatum
,sum(umsatz)
from MART_AUFTRAEGE.f_umsatz_cube2024 f_umsatz
where f_umsatz.tagesdatum >= '2025-01-09'
and f_umsatz.tagesdatum <= '2025-01-10'
and verkaeufernummer = 'GH NRW Lutter'
and (gesellschaft <> 'DEISS_DE' OR (handelsplatz_aka_u_dim1_von_vertriebsmitarbeiter in ('GH','HGH','Fabrik')
and ARTIKELGRUPPE_CODE not in ('229','230')))
group by 1,2,3,4,5
order by 1,2,3,4,5


-- verkäufer HK
select hk_bgnr_geschaeftspartner ,gesellschaft,verkaeufernummer 
from bvlt_general.bgnr_geschaeftspartner_p1_b10_sat
 
select *
from BVLT_SAP.BSAP_DISTRIBUTION_RULE_OOCR_CURRENT_REF

-- Historienanalyse
select hk_bgnr_geschaeftspartner, count(1)
from bvlt_general.bgnr_geschaeftspartner_p1_b10_sat b10_sat
group by 1
having count(1) >1


-- alle verkäufe eines zeitraums eines verkäufers
with measures as (
	select
		gesellschaft_lnk.hk_rgnr_gesellschaft 
		,item_lnk.hk_rsap_item_oitm
		,bpartner_semployee_lnk.hk_rsap_business_partner_ocrd 
		,bpartner_semployee_lnk.hk_rsap_sales_employee_oslp 
		,head_sat.docdate
		,line_sat.ocrcode
		,line_sat.ocrcode2
		,line_korr_sat.linetotal_korrigiert 
		,line_sat.currency 
		,null  as quantity  -- aktuell haben wir keine Quantity in den SAP Daten
	from   RVLT_SAP.RSAP_INVOICE_LINE_INV1_P1_L20_SAT line_sat
	join bvlt_sap.bsap_INVOICE_LINE_INV1_korrigiert_v1_b10_current_sat  line_korr_sat
		on line_korr_sat.hk_rsap_INVOICE_LINE_INV1 =line_sat.hk_rsap_INVOICE_LINE_INV1
	join RVLT_SAP.RSAP_INVOICE_LINE_INV1_INVOICE_OINV_LNK head_lnk
		on head_lnk.HK_RSAP_INVOICE_LINE_INV1 = line_sat.HK_RSAP_INVOICE_LINE_INV1
	join RVLT_SAP.RSAP_INVOICE_OINV_P1_L20_SAT head_sat
		on head_sat.hk_rsap_invoice_oinv = head_lnk.hk_rsap_invoice_oinv
		and head_sat.md_valid_before = lib.dwh_far_future_date()
		and not head_sat.md_is_deleted
	join RVLT_SAP.RSAP_INVOICE_OINV_RGNR_GESELLSCHAFT_LNK gesellschaft_lnk
		on gesellschaft_lnk.hk_rsap_invoice_oinv = head_lnk.hk_rsap_invoice_oinv 
	join RVLT_SAP.RSAP_INVOICE_OINV_BUSINESS_PARTNER_OCRD_SALES_EMPLOYEE_OSLP_LNK bpartner_semployee_lnk
		on bpartner_semployee_lnk.hk_rsap_invoice_oinv = head_lnk.hk_rsap_invoice_oinv 
	join RVLT_SAP.RSAP_INVOICE_OINV_BUSINESS_PARTNER_OCRD_SALES_EMPLOYEE_OSLP_ESAT bpartner_semployee_esat
		on bpartner_semployee_esat.LK_RSAP_INVOICE_OINV_BUSINESS_PARTNER_OCRD_SALES_EMPLOYEE_OSLP =bpartner_semployee_lnk.LK_RSAP_INVOICE_OINV_BUSINESS_PARTNER_OCRD_SALES_EMPLOYEE_OSLP 
		and bpartner_semployee_esat.md_valid_before = lib.dwh_far_future_date()
		and not bpartner_semployee_esat.md_is_deleted
	join rvlt_sap.rsap_invoice_line_inv1_item_oitm_lnk item_lnk
		on item_lnk.hk_rsap_invoice_line_inv1 =line_sat.hk_rsap_invoice_line_inv1 
	join rvlt_sap.rsap_invoice_line_inv1_item_oitm_esat item_esat
		on item_esat.lk_rsap_invoice_line_inv1_item_oitm =item_lnk.lk_rsap_invoice_line_inv1_item_oitm 
		and item_esat.md_valid_before = lib.dwh_far_future_date()
		and not head_sat.md_is_deleted 
	join RVLT_SAP.RSAP_INVOICE_LINE_INV1_HUB line_hub
		on line_hub.hk_rsap_invoice_line_inv1 =line_sat.hk_rsap_invoice_line_inv1 
	where line_sat.md_valid_before = lib.dwh_far_future_date()
		and not line_sat.md_is_deleted
        and (head_sat.doctype ='I' and line_hub.company ='DEISS_DE') -- Nur Artikelrechnungen (dann wären es 395.409) bei FIPP_DE nehmen wir allesn, dann sind es  395.527 rows
        and docdate>='2025-01-01'
	--
	union all
	--
	select
		gesellschaft_lnk.hk_rgnr_gesellschaft 
		,item_lnk.hk_rsap_item_oitm
		,bpartner_semployee_lnk.hk_rsap_business_partner_ocrd 
		,bpartner_semployee_lnk.hk_rsap_sales_employee_oslp 
		,head_sat.docdate
		,line_sat.ocrcode
		,line_sat.ocrcode2
		,-line_korr_sat.linetotal_korrigiert  -- negativer Wert, da dieser Teil die Gutschriften liefert
		,line_sat.currency cy
		,null  as quantity  -- aktuell haben wir keine Quantity in den SAP Daten
	from RVLT_SAP.rsap_credit_memo_line_rin1_p1_l20_sat  line_sat
	join bvlt_sap.bsap_credit_memo_line_rin1_korrigiert_v1_b10_current_sat  line_korr_sat
		on line_korr_sat.hk_rsap_credit_memo_line_rin1 =line_sat.hk_rsap_credit_memo_line_rin1 
	join RVLT_SAP.rsap_credit_memo_line_rin1_credit_memo_orin_lnk head_lnk
		on head_lnk.hk_rsap_credit_memo_line_rin1 = line_sat.hk_rsap_credit_memo_line_rin1 
	join RVLT_SAP.rsap_credit_memo_orin_p1_l20_sat head_sat
		on head_sat.hk_rsap_credit_memo_orin = head_lnk.hk_rsap_credit_memo_orin
		and head_sat.md_valid_before = lib.dwh_far_future_date()
		and not head_sat.md_is_deleted
	join RVLT_SAP.rsap_credit_memo_orin_rgnr_gesellschaft_lnk  gesellschaft_lnk
		on gesellschaft_lnk.hk_rsap_credit_memo_orin = head_lnk.hk_rsap_credit_memo_orin 
	join RVLT_SAP.rsap_credit_memo_orin_business_partner_ocrd_SALES_EMPLOYEE_OSLP_lnk bpartner_semployee_lnk
		on bpartner_semployee_lnk.hk_rsap_credit_memo_orin = head_lnk.hk_rsap_credit_memo_orin 
	join RVLT_SAP.rsap_credit_memo_orin_business_partner_ocrd_SALES_EMPLOYEE_OSLP_ESAT bpartner_semployee__esat
		on bpartner_semployee__esat.LK_RSAP_CREDIT_MEMO_ORIN_BUSINESS_PARTNER_OCRD_SALES_EMPLOYEE_OSLP =bpartner_semployee_lnk.LK_RSAP_CREDIT_MEMO_ORIN_BUSINESS_PARTNER_OCRD_SALES_EMPLOYEE_OSLP 
		and bpartner_semployee__esat.md_valid_before = lib.dwh_far_future_date()
		and not bpartner_semployee__esat.md_is_deleted
	join rvlt_sap.rsap_credit_memo_line_rin1_item_oitm_lnk item_lnk
		on item_lnk.hk_rsap_credit_memo_line_rin1 =line_sat.hk_rsap_credit_memo_line_rin1 
	join rvlt_sap.rsap_credit_memo_line_rin1_item_oitm_esat item_esat
		on item_esat.lk_rsap_credit_memo_line_rin1_item_oitm =item_lnk.lk_rsap_credit_memo_line_rin1_item_oitm 
		and item_esat.md_valid_before = lib.dwh_far_future_date()
		and not head_sat.md_is_deleted 
	join RVLT_SAP.rsap_credit_memo_line_rin1_hub line_hub
		on line_hub.hk_rsap_credit_memo_line_rin1 =line_sat.hk_rsap_credit_memo_line_rin1 
	where line_sat.md_valid_before = lib.dwh_far_future_date()
		and not line_sat.md_is_deleted	
        and (head_sat.doctype ='I'  and line_hub.company ='DEISS_DE') -- Nur Artikelrechnungen außer bei FIPP, da alle Rechnungen
        and docdate>='2025-01-01'
        )
, SAP_businesspartner_current as (
	select  lnk.hk_rsap_business_partner_ocrd,  lnk.hk_bgnr_geschaeftspartner,b10_sat.kundengruppe 
	from bvlt_general.bgnr_geschaeftspartner_rsap_business_partner_lnk lnk
	join bvlt_general.bgnr_geschaeftspartner_rsap_business_partner_esat  esat 
		on esat.lk_bgnr_geschaeftspartner_rsap_business_partner = lnk.lk_bgnr_geschaeftspartner_rsap_business_partner 
		and esat.md_valid_before = lib.dwh_far_future_date()
		and not esat.md_is_deleted
	left join bvlt_general.bgnr_geschaeftspartner_p1_b10_sat b10_sat 
		on b10_sat.hk_bgnr_geschaeftspartner = lnk.hk_bgnr_geschaeftspartner 
		and b10_sat.md_valid_before = lib.dwh_far_future_date()
		and not b10_sat.md_is_deleted
))
, sap_items_with_itmsgrpnam  as (
	select item_hub.hk_rsap_item_oitm,group_ref.itmsgrpnam ,item_sat.itmgrpcod,item_sat.itemname 
	from rvlt_sap.rsap_item_oitm_hub item_hub
	join rvlt_sap.rsap_item_oitm_p1_l20_sat item_sat
		on item_sat.hk_rsap_item_oitm =item_hub.hk_rsap_item_oitm 
		and item_sat.md_valid_before = lib.dwh_far_future_date()
		and not item_sat.md_is_deleted
	left join bvlt_sap.bsap_item_group_oitb_current_ref group_ref
		on group_ref.company =item_hub.company 
		and group_ref.itmsgrpcod = item_sat.itmgrpcod 
)		
, fully_joined_and_aggregated_sap_dataset as (
	select measures.hk_rgnr_gesellschaft   			-- 1
		,businesspartner.hk_bgnr_geschaeftspartner  --2
		,businesspartner.kundengruppe				--3
		,DECODE(semp_sat.u_dim1,'10','HGH','20','GH','01','Fabrik','?'||semp_sat.u_dim1||'?') 	handelsplatz						--4
		,measures.docdate							--5
		,measures.ocrcode							--6
		,measures.ocrcode2							--7
		,measures.currency 							--8
		,sap_items.itmsgrpnam						--9
		,sap_items.itmgrpcod 						--10
		,sap_items.itemname
		,sum(measures.linetotal_korrigiert) linetotal_korrigiert_sum
		,sum(quantity)						quantity_sum
	from measures
	join sap_items_with_itmsgrpnam sap_items 
		 on sap_items.hk_rsap_item_oitm=measures.hk_rsap_item_oitm
	left join SAP_businesspartner_current businesspartner	
		on businesspartner.hk_rsap_business_partner_ocrd = measures.hk_rsap_business_partner_ocrd
	left join RVLT_SAP.RSAP_SALES_EMPLOYEE_OSLP_P1_L20_SAT	semp_sat
		on semp_sat.hk_rsap_sales_employee_oslp = measures.hk_rsap_sales_employee_oslp
		and semp_sat.md_valid_before = lib.dwh_far_future_date()
		and not semp_sat.md_is_deleted
		group by 1,2,3,4,5,6,7,8,9,10,11
)		
select 
		gesellschaft_hub.company 								as Gesellschaft
		,fds.docdate											as tagesdatum
		,'---'													as kanal
		,fds.handelsplatz										as handelsplatz_aka_U_Dim1_von_vertriebsmitarbeiter
		,distrule_ref.ocrname									as verkaufsgebiet			
		,distrule_ref2.ocrname									as verkaeufernummer
--		,fds.kundengruppe										as kundengruppe
--		,fds.hk_bgnr_geschaeftspartner 							as dk_geschaeftspartner
		,gsp_sat.name 										as Geschaeftspartner
		,fds.itmsgrpnam										    as artikelgruppe
		,fds.itmgrpcod											as artikelgruppe_code
		,fds.itemname
		,sum(round(fds.linetotal_korrigiert_sum,2))					as umsatz
/*		,fds.currency											as waehrung
		,case 
			  when fds.currency='CHF' and gesellschaft_hub.company='DEISS_CH' -- SAP von DEISS_CH liefer CHF
              then round(fds.linetotal_korrigiert_sum*exch_eur_src.rate,2) -- Eurokurs bei DEISS_CH
              else round(fds.linetotal_korrigiert_sum,2)    -- alle anderen SAP Systeme liefern EUR
			end 												as umsatz_eur
		,case when fds.currency='CHF' and gesellschaft_hub.company='DEISS_CH'
              then exch_eur_src.rate -- Eurokurs bei DEISS_CH
              else 1
			end													as rate
		,quantity_sum											as anzahl */
from fully_joined_and_aggregated_sap_dataset fds
join DV_D_MAIN_DATABASE.RVLT_GENERAL.RGNR_GESELLSCHAFT_HUB gesellschaft_hub
    on gesellschaft_hub.hk_rgnr_gesellschaft = fds.hk_rgnr_gesellschaft
left join BVLT_SAP.BSAP_DISTRIBUTION_RULE_OOCR_CURRENT_REF distrule_ref
	on distrule_ref.company  = gesellschaft_hub.company 
	and distrule_ref.ocrcode =fds.ocrcode
left join BVLT_SAP.BSAP_DISTRIBUTION_RULE_OOCR_CURRENT_REF distrule_ref2
	on distrule_ref2.company  = gesellschaft_hub.company 
	and distrule_ref2.ocrcode =fds.ocrcode2
left join BVLT_SAP.BSAP_EXCHANGE_RATE_ORTT_CURRENT_REF exch_de
		on exch_de.ratedate = fds.docdate 
		and exch_de.currency = fds.currency			-- Fremdwährungskurs bei DEISS
		and exch_de.company = 'DEISS_DE'				-- Fremdwährungskurs bei DEISS
left join BVLT_SAP.BSAP_EXCHANGE_RATE_ORTT_CURRENT_REF exch_eur_src
		on exch_eur_src.ratedate = fds.docdate 
		and exch_eur_src.currency = 'EUR' 					-- Eurokurs der Quelle
		and exch_eur_src.company = gesellschaft_hub.company -- Eurokurs der Quelle
join BVLT_GENERAL.BGNR_GESCHAEFTSPARTNER_P1_B10_SAT gsp_sat 
		on gsp_sat.hk_bgnr_geschaeftspartner = fds.hk_bgnr_geschaeftspartner
		and  gsp_sat.md_valid_before = lib.dwh_far_future_date()
		and  not gsp_sat.md_is_deleted
--where distrule_ref2.ocrname='GH NRW Lutter'   -- <<<<<<<<<<<<<<<<<<<<<<<<<<<< HIER EINSCHRÄNKEN
--where distrule_ref2.ocrname='GH Nord-Ost Treu'  -- <<<<<<<<<<<<<<<<<<<<<<<<<<<< HIER EINSCHRÄNKEN
group by 1,2,3,4,5,6,7,8,9,10
order by fds.docdate ,6


-- SAP_businesspartner_current 
	select  lnk.hk_rsap_business_partner_ocrd,  lnk.hk_bgnr_geschaeftspartner,b10_sat.kundengruppe 
	from bvlt_general.bgnr_geschaeftspartner_rsap_business_partner_lnk lnk
	join bvlt_general.bgnr_geschaeftspartner_rsap_business_partner_esat  esat 
		on esat.lk_bgnr_geschaeftspartner_rsap_business_partner = lnk.lk_bgnr_geschaeftspartner_rsap_business_partner 
		and esat.md_valid_before = lib.dwh_far_future_date()
		and not esat.md_is_deleted
	join bvlt_general.bgnr_geschaeftspartner_p1_b10_sat b10_sat 
		on b10_sat.hk_bgnr_geschaeftspartner = lnk.hk_bgnr_geschaeftspartner 
		and b10_sat.md_valid_before = lib.dwh_far_future_date()
		and not b10_sat.md_is_deleted
	where lnk.hk_bgnr_geschaeftspartner in('XD9DuJHSc0Ycfk6+m7GmYS11yMY=')
	
	-- ergab ohne die Begrenzung des bgnr_geschaeftspartner_p1_b10_sat doppelte Datensätze für einzelne Businesspartner

-- Patch test referenzdatenaufbau
create table zz_verifizierung_referenz.MAFT_F_UMSATZ_KOMPLETT_20250204_1220_POST_PATCH as select * from DV_D_MAIN_DATABASE.MART_AUFTRAEGE.F_UMSATZ;

-- Patch test differenzcheck
with differenz as 
(select * from zz_verifizierung_referenz.maft_f_umsatz_komplett_20250204_1218_pre_patch 
minus
select * from zz_verifizierung_referenz.MAFT_F_UMSATZ_KOMPLETT_20250204_1220_POST_PATCH )
select distinct gesellschaft,verkaeufernummer,date_trunc(month,tagesdatum) monat,count(1),sum(umsatz_eur)
from differenz
where (gesellschaft <> 'DEISS_DE' OR (handelsplatz_aka_u_dim1_von_vertriebsmitarbeiter in ('GH','HGH','Fabrik')
and ARTIKELGRUPPE_CODE not in ('229','230')))
group by 1,2,3
order by 1,2,3


/* ------------------------ Bill be segment ------------------------------------------------------------ */

 with billbee_businesspartner_current as (
	select  lnk.hk_rbbe_shop 
		  , lnk.hk_bgnr_geschaeftspartner
		  ,b10_sat.kundengruppe 
		  ,b10_sat."NAME" 			geschaeftspartner_name
	from bvlt_general.bgnr_geschaeftspartner_rbbe_shop_lnk lnk
	join bvlt_general.bgnr_geschaeftspartner_rbbe_shop_esat  esat 
		on esat.lk_bgnr_geschaeftspartner_rbbe_shop = lnk.lk_bgnr_geschaeftspartner_rbbe_shop 
		and esat.md_valid_before = lib.dwh_far_future_date()
		and not esat.md_is_deleted
	join bvlt_general.bgnr_geschaeftspartner_p1_b10_sat b10_sat 
		on b10_sat.hk_bgnr_geschaeftspartner = lnk.hk_bgnr_geschaeftspartner 
		and b10_sat.md_valid_before = lib.dwh_far_future_date()
		and not b10_sat.md_is_deleted
	where lnk.hk_rbbe_shop <>'ffffffffffffffffffffffffffff' 
		)		
---		
, fully_joined_and_aggregated_billbee_dataset as (
select 
	bp_cur.hk_bgnr_geschaeftspartner
	,bp_cur.geschaeftspartner_name
	,date(coalesce(order_p2_s.invoiceDate,order_p2_s.payedat))						as invoice_date_as_date
	,'##wartet auf pim##' 								as artikelgruppe_source
	,'##wartet auf pim##'								as artikelgruppe_code_source
	,order_p1_s.currency 				
	,sum(item_korr_s.totalprice_netto_korrigiert) 		as total_price_netto_korrigiert_sum
	,sum(item_korr_s.totalprice_netto)					as total_price_sum
	,sum(item_p1_s.quantity)							as quantity
from BVLT_BILLBEE.BBBE_ORDER_ITEM_PRODUCT_KORRIGIERT_V1_B10_CURRENT_SAT item_korr_s
join RVLT_BILLBEE.rbbe_order_item_order_p1_l10_sat item_p1_s
	on item_p1_s.hk_rbbe_order_item = item_korr_s.hk_rbbe_order_item 
   and item_p1_s.md_valid_before =lib.dwh_far_future_date()
   and not item_p1_s.md_is_deleted  
join RVLT_BILLBEE.RBBE_ORDER_ITEM_ORDER_LNK item_order_l 
	on item_order_l.hk_rbbe_order_item =item_korr_s.hk_rbbe_order_item 
join RVLT_BILLBEE.RBBE_ORDER_ITEM_ORDER_esat item_order_e
	on item_order_e.lk_rbbe_order_item_order = item_order_l.lk_rbbe_order_item_order 
   and item_order_e.md_valid_before =lib.dwh_far_future_date()
   and not item_order_e.md_is_deleted
join RVLT_BILLBEE.rbbe_order_order_p1_l10_sat order_p1_s
	on order_p1_s.hk_rbbe_order = item_order_l.hk_rbbe_order 
   and order_p1_s.md_valid_before =lib.dwh_far_future_date()
   and not order_p1_s.md_is_deleted  
join RVLT_BILLBEE.rbbe_order_order_p2_l10_sat order_p2_s
	on order_p2_s.hk_rbbe_order = item_order_l.hk_rbbe_order 
   and order_p2_s.md_valid_before =lib.dwh_far_future_date()
   and not order_p2_s.md_is_deleted  
 join rvlt_billbee.rbbe_order_shop_lnk order_shop_l
 	on order_shop_l.hk_rbbe_order = item_order_l.hk_rbbe_order 
 join rvlt_billbee.rbbe_order_shop_p1_l10_sat order_shop_s
 	on order_shop_s.lk_rbbe_order_shop = order_shop_l.lk_rbbe_order_shop 
   and order_shop_s.md_valid_before =lib.dwh_far_future_date()
   and not order_shop_s.md_is_deleted   
 join billbee_businesspartner_current bp_cur 
    on bp_cur.hk_rbbe_shop=order_shop_l.hk_rbbe_shop 
 where geschaeftspartner_name<>'Testshop manuell' 
   and (order_p2_s.invoiceDate is not null or order_p2_s.payedat is not null)
   and order_p2_s.state <> 8 -- exclude "Storiert"
 group by 1,2,3,4,5,6	
)
-- Final billbee dataset with last lookups, in row calculations and target column naming
--,final_billbee_dataset as(
select 
		'SUND_DIGITAL'			 								as Gesellschaft -- #### Achtung ist hier hard coded
		,fds.invoice_date_as_date								as tagesdatum
		,fds.geschaeftspartner_name								as kanal
		,'---'													as handelsplatz_aka_U_Dim1_von_vertriebsmitarbeiter
		,'---'									as verkaufsgebiet			
		,'---'									as verkaeufernummer
		,'---'										as kundengruppe
		,fds.hk_bgnr_geschaeftspartner 							as dk_geschaeftspartner
		,fds.artikelgruppe_source							    as artikelgruppe
		,fds.artikelgruppe_code_source							as artikelgruppe_code
		,round(fds.total_price_netto_korrigiert_sum,2)					as umsatz
		,fds.currency											as waehrung
		,case when (fds.currency<>'EUR') 
			then round(fds.total_price_netto_korrigiert_sum/exch.rate,2)		
			else round(fds.total_price_netto_korrigiert_sum,2)	
			end 												as umsatz_eur
		,case when (fds.currency<>'EUR') 
			then round(fds.total_price_sum/exch.rate,2)		
			else round(fds.total_price_sum,2)	
			end 												as umsatz_eur_raw
		,exch.rate
		,quantity												as anzahl
from fully_joined_and_aggregated_billbee_dataset fds
left join BVLT_SAP.BSAP_EXCHANGE_RATE_ORTT_CURRENT_REF exch  -- Wechselkurse werden DEISS_DE SAP Daten entnommen
		on exch.ratedate = fds.invoice_date_as_date 
		and exch.currency = fds.currency
		and exch.company = 'DEISS_DE';
	
-- billbee - all positions	
 with billbee_businesspartner_current as (
	select  lnk.hk_rbbe_shop 
		  , lnk.hk_bgnr_geschaeftspartner
		  ,b10_sat.kundengruppe 
		  ,b10_sat."NAME" 			geschaeftspartner_name
	from bvlt_general.bgnr_geschaeftspartner_rbbe_shop_lnk lnk
	join bvlt_general.bgnr_geschaeftspartner_rbbe_shop_esat  esat 
		on esat.lk_bgnr_geschaeftspartner_rbbe_shop = lnk.lk_bgnr_geschaeftspartner_rbbe_shop 
		and esat.md_valid_before = lib.dwh_far_future_date()
		and not esat.md_is_deleted
	join bvlt_general.bgnr_geschaeftspartner_p1_b10_sat b10_sat 
		on b10_sat.hk_bgnr_geschaeftspartner = lnk.hk_bgnr_geschaeftspartner 
		and b10_sat.md_valid_before = lib.dwh_far_future_date()
		and not b10_sat.md_is_deleted
	where lnk.hk_rbbe_shop <>'ffffffffffffffffffffffffffff' 
		)		
,main_data_assembly as (
select 
	--bp_cur.hk_bgnr_geschaeftspartner
	date(coalesce(order_p2_s.invoiceDate,order_p2_s.payedat))						as pay_date -- original
--	date(coalesce(order_p2_s.payedat,order_p2_s.invoiceDate))						as pay_date -- variant
	,item_h.billbeeid 						as item_billbee_id
	,order_p2_s.state 
	,bp_cur.geschaeftspartner_name
	,order_p1_s.currency 	
	,item_p1_s.product_title 
	,item_p1_s.product_ean is not null as has_ean
	,CONTAINS(item_p1_s.product_title, 'BINGOLD') as is_bingold_product
	,item_korr_s.totalprice_netto_korrigiert 		as total_price_netto_korrigiert_sum
	,item_korr_s.totalprice_netto					as total_price_sum
	,item_p1_s.quantity							as quantity
from BVLT_BILLBEE.BBBE_ORDER_ITEM_PRODUCT_KORRIGIERT_V1_B10_CURRENT_SAT item_korr_s
join RVLT_BILLBEE.rbbe_order_item_order_p1_l10_sat item_p1_s
	on item_p1_s.hk_rbbe_order_item = item_korr_s.hk_rbbe_order_item 
   and item_p1_s.md_valid_before =lib.dwh_far_future_date()
   and not item_p1_s.md_is_deleted  
join rvlt_billbee.rbbe_order_item_hub item_h
	on item_h.hk_rbbe_order_item = item_korr_s.hk_rbbe_order_item
join RVLT_BILLBEE.RBBE_ORDER_ITEM_ORDER_LNK item_order_l 
	on item_order_l.hk_rbbe_order_item =item_korr_s.hk_rbbe_order_item 
join RVLT_BILLBEE.RBBE_ORDER_ITEM_ORDER_esat item_order_e
	on item_order_e.lk_rbbe_order_item_order = item_order_l.lk_rbbe_order_item_order 
   and item_order_e.md_valid_before =lib.dwh_far_future_date()
   and not item_order_e.md_is_deleted
join RVLT_BILLBEE.rbbe_order_order_p1_l10_sat order_p1_s
	on order_p1_s.hk_rbbe_order = item_order_l.hk_rbbe_order 
   and order_p1_s.md_valid_before =lib.dwh_far_future_date()
   and not order_p1_s.md_is_deleted  
join RVLT_BILLBEE.rbbe_order_order_p2_l10_sat order_p2_s
	on order_p2_s.hk_rbbe_order = item_order_l.hk_rbbe_order 
   and order_p2_s.md_valid_before =lib.dwh_far_future_date()
   and not order_p2_s.md_is_deleted  
 join rvlt_billbee.rbbe_order_shop_lnk order_shop_l
 	on order_shop_l.hk_rbbe_order = item_order_l.hk_rbbe_order 
 join rvlt_billbee.rbbe_order_shop_p1_l10_sat order_shop_s
 	on order_shop_s.lk_rbbe_order_shop = order_shop_l.lk_rbbe_order_shop 
   and order_shop_s.md_valid_before =lib.dwh_far_future_date()
   and not order_shop_s.md_is_deleted   
 join billbee_businesspartner_current bp_cur 
    on bp_cur.hk_rbbe_shop=order_shop_l.hk_rbbe_shop 
 where geschaeftspartner_name<>'Testshop manuell' 
   and (order_p2_s.invoiceDate is not null or order_p2_s.payedat is not null) -- 
   --and order_p2_s.state <> 8 -- exclude "Storniert"
)
select main_data_assembly.* exclude (total_price_netto_korrigiert_sum,total_price_sum)
,case when (main_data_assembly.currency<>'EUR') 
			then round(main_data_assembly.total_price_netto_korrigiert_sum/exch.rate,2)		
			else round(main_data_assembly.total_price_netto_korrigiert_sum,2)	
			end 												as total_price_netto_korrigiert_sum
,case when (main_data_assembly.currency<>'EUR') 
			then round(main_data_assembly.total_price_sum/exch.rate,2)		
			else round(main_data_assembly.total_price_sum,2)	
			end 												as total_price_sum			
from main_data_assembly
 left join BVLT_SAP.BSAP_EXCHANGE_RATE_ORTT_CURRENT_REF exch  -- Wechselkurse werden DEISS_DE SAP Daten entnommen
		on exch.ratedate = main_data_assembly.pay_date 
		and exch.currency = main_data_assembly.currency
		and exch.company = 'DEISS_DE'
   order by pay_date,geschaeftspartner_name
 		
   
-- FIPP_DE einzelpositionen EDEKA-NETTO - BUDNI - Rewe
with measures as (
	select
		gesellschaft_lnk.hk_rgnr_gesellschaft 
		,item_lnk.hk_rsap_item_oitm
		,bpartner_semployee_lnk.hk_rsap_business_partner_ocrd 
		,bpartner_semployee_lnk.hk_rsap_sales_employee_oslp 
		,line_hub.docentry
		,line_hub.linenum 
		,'invoice' as doctype
		,head_sat.docdate
		,line_sat.ocrcode
		,line_sat.ocrcode2
		,line_sat.linetotal
		,line_korr_sat.linetotal_korrigiert 
		,line_sat.currency 
		,null  as quantity  -- aktuell haben wir keine Quantity in den SAP Daten
		,line_sat.linetype 
		,head_sat.doctype as head_doctype 
		,line_sat.basetype as  line_basetype  
		,line_korr_sat.fipp_de_rabatt_per_inv2  
	from   RVLT_SAP.RSAP_INVOICE_LINE_INV1_P1_L20_SAT line_sat
	join bvlt_sap.bsap_INVOICE_LINE_INV1_korrigiert_v1_b10_current_sat  line_korr_sat
		on line_korr_sat.hk_rsap_INVOICE_LINE_INV1 =line_sat.hk_rsap_INVOICE_LINE_INV1
	join RVLT_SAP.RSAP_INVOICE_LINE_INV1_INVOICE_OINV_LNK head_lnk
		on head_lnk.HK_RSAP_INVOICE_LINE_INV1 = line_sat.HK_RSAP_INVOICE_LINE_INV1
	join RVLT_SAP.RSAP_INVOICE_OINV_P1_L20_SAT head_sat
		on head_sat.hk_rsap_invoice_oinv = head_lnk.hk_rsap_invoice_oinv
		and head_sat.md_valid_before = lib.dwh_far_future_date()
		and not head_sat.md_is_deleted
	join RVLT_SAP.RSAP_INVOICE_OINV_RGNR_GESELLSCHAFT_LNK gesellschaft_lnk
		on gesellschaft_lnk.hk_rsap_invoice_oinv = head_lnk.hk_rsap_invoice_oinv 
	join RVLT_SAP.RSAP_INVOICE_OINV_BUSINESS_PARTNER_OCRD_SALES_EMPLOYEE_OSLP_LNK bpartner_semployee_lnk
		on bpartner_semployee_lnk.hk_rsap_invoice_oinv = head_lnk.hk_rsap_invoice_oinv 
	join RVLT_SAP.RSAP_INVOICE_OINV_BUSINESS_PARTNER_OCRD_SALES_EMPLOYEE_OSLP_ESAT bpartner_semployee_esat
		on bpartner_semployee_esat.LK_RSAP_INVOICE_OINV_BUSINESS_PARTNER_OCRD_SALES_EMPLOYEE_OSLP =bpartner_semployee_lnk.LK_RSAP_INVOICE_OINV_BUSINESS_PARTNER_OCRD_SALES_EMPLOYEE_OSLP 
		and bpartner_semployee_esat.md_valid_before = lib.dwh_far_future_date()
		and not bpartner_semployee_esat.md_is_deleted
	join rvlt_sap.rsap_invoice_line_inv1_item_oitm_lnk item_lnk
		on item_lnk.hk_rsap_invoice_line_inv1 =line_sat.hk_rsap_invoice_line_inv1 
	join rvlt_sap.rsap_invoice_line_inv1_item_oitm_esat item_esat
		on item_esat.lk_rsap_invoice_line_inv1_item_oitm =item_lnk.lk_rsap_invoice_line_inv1_item_oitm 
		and item_esat.md_valid_before = lib.dwh_far_future_date()
		and not head_sat.md_is_deleted 
	join RVLT_SAP.RSAP_INVOICE_LINE_INV1_HUB line_hub
		on line_hub.hk_rsap_invoice_line_inv1 =line_sat.hk_rsap_invoice_line_inv1 
	where line_sat.md_valid_before = lib.dwh_far_future_date()
		and not line_sat.md_is_deleted
        and line_hub.company ='FIPP_DE' -- Nur Artikelrechnungen 
	--
	union all
	--
	select
		gesellschaft_lnk.hk_rgnr_gesellschaft 
		,item_lnk.hk_rsap_item_oitm
		,bpartner_semployee_lnk.hk_rsap_business_partner_ocrd 
		,bpartner_semployee_lnk.hk_rsap_sales_employee_oslp 
		,line_hub.docentry
		,line_hub.linenum 
		,'credit' as doctype
		,head_sat.docdate
		,line_sat.ocrcode
		,line_sat.ocrcode2
		,-line_sat.linetotal
		,-line_korr_sat.linetotal_korrigiert  -- negativer Wert, da dieser Teil die Gutschriften liefert
		,line_sat.currency cy
		,null  as quantity  -- aktuell haben wir keine Quantity in den SAP Daten
		,line_sat.linetype 
		,head_sat.doctype as head_doctype 
		,line_sat.basetype as  line_basetype
		,null as fipp_de_rabatt_per_inv2  
	from RVLT_SAP.rsap_credit_memo_line_rin1_p1_l20_sat  line_sat
	join bvlt_sap.bsap_credit_memo_line_rin1_korrigiert_v1_b10_current_sat  line_korr_sat
		on line_korr_sat.hk_rsap_credit_memo_line_rin1 =line_sat.hk_rsap_credit_memo_line_rin1 
	join RVLT_SAP.rsap_credit_memo_line_rin1_credit_memo_orin_lnk head_lnk
		on head_lnk.hk_rsap_credit_memo_line_rin1 = line_sat.hk_rsap_credit_memo_line_rin1 
	join RVLT_SAP.rsap_credit_memo_orin_p1_l20_sat head_sat
		on head_sat.hk_rsap_credit_memo_orin = head_lnk.hk_rsap_credit_memo_orin
		and head_sat.md_valid_before = lib.dwh_far_future_date()
		and not head_sat.md_is_deleted
	join RVLT_SAP.rsap_credit_memo_orin_rgnr_gesellschaft_lnk  gesellschaft_lnk
		on gesellschaft_lnk.hk_rsap_credit_memo_orin = head_lnk.hk_rsap_credit_memo_orin 
	join RVLT_SAP.rsap_credit_memo_orin_business_partner_ocrd_SALES_EMPLOYEE_OSLP_lnk bpartner_semployee_lnk
		on bpartner_semployee_lnk.hk_rsap_credit_memo_orin = head_lnk.hk_rsap_credit_memo_orin 
	join RVLT_SAP.rsap_credit_memo_orin_business_partner_ocrd_SALES_EMPLOYEE_OSLP_ESAT bpartner_semployee__esat
		on bpartner_semployee__esat.LK_RSAP_CREDIT_MEMO_ORIN_BUSINESS_PARTNER_OCRD_SALES_EMPLOYEE_OSLP =bpartner_semployee_lnk.LK_RSAP_CREDIT_MEMO_ORIN_BUSINESS_PARTNER_OCRD_SALES_EMPLOYEE_OSLP 
		and bpartner_semployee__esat.md_valid_before = lib.dwh_far_future_date()
		and not bpartner_semployee__esat.md_is_deleted
	join rvlt_sap.rsap_credit_memo_line_rin1_item_oitm_lnk item_lnk
		on item_lnk.hk_rsap_credit_memo_line_rin1 =line_sat.hk_rsap_credit_memo_line_rin1 
	join rvlt_sap.rsap_credit_memo_line_rin1_item_oitm_esat item_esat
		on item_esat.lk_rsap_credit_memo_line_rin1_item_oitm =item_lnk.lk_rsap_credit_memo_line_rin1_item_oitm 
		and item_esat.md_valid_before = lib.dwh_far_future_date()
		and not head_sat.md_is_deleted 
	join RVLT_SAP.rsap_credit_memo_line_rin1_hub line_hub
		on line_hub.hk_rsap_credit_memo_line_rin1 =line_sat.hk_rsap_credit_memo_line_rin1 
	where line_sat.md_valid_before = lib.dwh_far_future_date()
		and not line_sat.md_is_deleted	
        and line_hub.company ='FIPP_DE' -- Nur  FIPP
)
, SAP_businesspartner_current as (
	select  lnk.hk_rsap_business_partner_ocrd,  lnk.hk_bgnr_geschaeftspartner,b10_sat.kundengruppe 
	from bvlt_general.bgnr_geschaeftspartner_rsap_business_partner_lnk lnk
	join bvlt_general.bgnr_geschaeftspartner_rsap_business_partner_esat  esat 
		on esat.lk_bgnr_geschaeftspartner_rsap_business_partner = lnk.lk_bgnr_geschaeftspartner_rsap_business_partner 
		and esat.md_valid_before = lib.dwh_far_future_date()
		and not esat.md_is_deleted
	join bvlt_general.bgnr_geschaeftspartner_p1_b10_sat b10_sat 
		on b10_sat.hk_bgnr_geschaeftspartner = lnk.hk_bgnr_geschaeftspartner 
		and b10_sat.md_valid_before = lib.dwh_far_future_date()
		and not b10_sat.md_is_deleted
		and  kundengruppe in('Budni','Edeka / Netto','Rewe')  -- Nur die drei problematischen Kunden
)
, sap_items_with_itmsgrpnam  as (
	select item_hub.hk_rsap_item_oitm,group_ref.itmsgrpnam ,item_sat.itmgrpcod,item_sat.itemname 
	from rvlt_sap.rsap_item_oitm_hub item_hub
	join rvlt_sap.rsap_item_oitm_p1_l20_sat item_sat
		on item_sat.hk_rsap_item_oitm =item_hub.hk_rsap_item_oitm 
		and item_sat.md_valid_before = lib.dwh_far_future_date()
		and not item_sat.md_is_deleted
	left join bvlt_sap.bsap_item_group_oitb_current_ref group_ref
		on group_ref.company =item_hub.company 
		and group_ref.itmsgrpcod = item_sat.itmgrpcod 
)		
, fully_joined_sap_dataset as (
	select measures.hk_rgnr_gesellschaft   			-- 1
		,businesspartner.hk_bgnr_geschaeftspartner  --2
		,businesspartner.kundengruppe				--3
		,DECODE(semp_sat.u_dim1,'10','HGH','20','GH','01','Fabrik','07','Intercompany','09','Ausland','?'||semp_sat.u_dim1||'?') 	handelsplatz						--4
		,measures.docentry							--5
		,measures.linenum 							--6
		,measures.doctype
		,measures.docdate							--7
		,measures.ocrcode							--8
		,measures.ocrcode2							--9
		,measures.currency 							--10
		--,sap_items.itmsgrpnam						--11
		--,sap_items.itmgrpcod 						--12
		,sap_items.itemname
		,measures.linetotal
		,measures.linetotal_korrigiert
		,quantity						
		,linetype 
		,head_doctype 
		,line_basetype  
		,fipp_de_rabatt_per_inv2
	from measures
	join sap_items_with_itmsgrpnam sap_items 
		 on sap_items.hk_rsap_item_oitm=measures.hk_rsap_item_oitm
	join SAP_businesspartner_current businesspartner	
		on businesspartner.hk_rsap_business_partner_ocrd = measures.hk_rsap_business_partner_ocrd
	left join RVLT_SAP.RSAP_SALES_EMPLOYEE_OSLP_P1_L20_SAT	semp_sat
		on semp_sat.hk_rsap_sales_employee_oslp = measures.hk_rsap_sales_employee_oslp
		and semp_sat.md_valid_before = lib.dwh_far_future_date()
		and not semp_sat.md_is_deleted
	--group by 1,2,3,4,5,6,7,8,9,10
		)
-- Final sap dataset with last lookups, in row calculations and target column naming
--,final_sap_dataset as(	
select 
		--gesellschaft_hub.company 								as Gesellschaft
		fds.doctype
		,fds.docentry
		,fds.linenum 		
		,fds.docdate											as tagesdatum
		--,'---'													as kanal
		--,fds.handelsplatz										as handelsplatz_aka_U_Dim1_von_vertriebsmitarbeiter
		--,distrule_ref.ocrname									as verkaufsgebiet			
		--,distrule_ref2.ocrname									as verkaeufernummer
		,fds.kundengruppe										as kundengruppe
		,case when kundengruppe in('Budni','Edeka / Netto') THEN 'Edeka / Netto / Budni' 
				when kundengruppe like 'Rossman%' THEN 'Rossmann'	
				when kundengruppe = 'Lidl Ausland' THEN 'Lidl AL'
				when kundengruppe in('Hamburg','Heku') THEN 'Hamburg Diverse'
			     ELSE kundengruppe  end as kundengruppe_final 
		--,fds.hk_bgnr_geschaeftspartner 							as dk_geschaeftspartner
		--,fds.itmsgrpnam										    as artikelgruppe
		--,fds.itmgrpcod											as artikelgruppe_code
		,itemname
		,fds.linetotal
		,round(fds.linetotal_korrigiert,4)					as umsatz
		,fds.currency											as waehrung
		,case 
			  when fds.currency='CHF' and gesellschaft_hub.company='DEISS_CH' -- SAP von DEISS_CH liefer CHF
              then round(fds.linetotal_korrigiert*exch_eur_src.rate,2) -- Eurokurs bei DEISS_CH
              else round(fds.linetotal_korrigiert,2)    -- alle anderen SAP Systeme liefern EUR
			end 												as umsatz_eur
		,case when fds.currency='CHF' and gesellschaft_hub.company='DEISS_CH'
              then exch_eur_src.rate -- Eurokurs bei DEISS_CH
              else 1
			end													as rate
		,quantity											as anzahl
		,linetype 
		,head_doctype 
		,line_basetype  
		,fipp_de_rabatt_per_inv2
from fully_joined_sap_dataset fds
join DV_D_MAIN_DATABASE.RVLT_GENERAL.RGNR_GESELLSCHAFT_HUB gesellschaft_hub
    on gesellschaft_hub.hk_rgnr_gesellschaft = fds.hk_rgnr_gesellschaft
left join BVLT_SAP.BSAP_DISTRIBUTION_RULE_OOCR_CURRENT_REF distrule_ref
	on distrule_ref.company  = gesellschaft_hub.company 
	and distrule_ref.ocrcode =fds.ocrcode
left join BVLT_SAP.BSAP_DISTRIBUTION_RULE_OOCR_CURRENT_REF distrule_ref2
	on distrule_ref2.company  = gesellschaft_hub.company 
	and distrule_ref2.ocrcode =fds.ocrcode2
left join BVLT_SAP.BSAP_EXCHANGE_RATE_ORTT_CURRENT_REF exch_de
		on exch_de.ratedate = fds.docdate 
		and exch_de.currency = fds.currency			-- Fremdwährungskurs bei DEISS
		and exch_de.company = 'DEISS_DE'				-- Fremdwährungskurs bei DEISS
left join BVLT_SAP.BSAP_EXCHANGE_RATE_ORTT_CURRENT_REF exch_eur_src
		on exch_eur_src.ratedate = fds.docdate 
		and exch_eur_src.currency = 'EUR' 					-- Eurokurs der Quelle
		and exch_eur_src.company = gesellschaft_hub.company -- Eurokurs der Quelle   
where 	fds.docdate >='2023-01-01' 
  and 	fds.docdate <='2025-01-01'
  order by docentry,linenum


