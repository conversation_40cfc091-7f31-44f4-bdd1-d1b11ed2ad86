import json
import io

from lib.connection_azrblob import connection_azrblob

def get_source_files_from_blob_storage(blob_connection, sap_source_object, container_name="rawdata"):
    container_client = blob_connection.get_container_client(container_name)
    blob_list = list(container_client.list_blobs(name_starts_with=sap_source_object))
    if len(blob_list) == 0:
        blob_list_source = container_client.list_blobs()
        for blob in blob_list_source:
            if sap_source_object in blob.name:
                blob_list.append(blob)
    return blob_list

def move_blob(blob_connection, blob_name, source_container_name, destination_container_name):
    try:
        source_blob_client = blob_connection.get_blob_client(source_container_name, blob_name)
        destination_blob_client = blob_connection.get_blob_client(destination_container_name, blob_name)
        destination_blob_client.start_copy_from_url(source_blob_client.url)
        source_blob_client.delete_blob()
        return True
    except Exception as error:
        print(error)
        raise

def move_processed_file_to_processed_container(blob_connection, blob_name, source_container_name='rawdata', destination_container_name='processed'):
    move_blob(blob_connection, blob_name, source_container_name, destination_container_name)

def fetch_source_file_from_blob_container(blob_client):
    try:
        return blob_client.download_blob()
    except Exception as e:
        print(f"An error occurred: {e}")

def fetch_json_source_file_from_blob_container(blob_client):
    try:
        json_data = json.load(fetch_source_file_from_blob_container(blob_client))
        return json_data
    except Exception as e:
        print(f"An error occurred: {e}")

def fetch_excel_source_file_from_blob_container(blob_client):
    try:
        excel_data = io.BytesIO()
        fetch_source_file_from_blob_container(blob_client).readinto(excel_data)
        excel_data.seek(0)
        return excel_data
    except Exception as e:
        print(f"An error occurred: {e}")


if __name__ == '__main__':
    #test
    blob_service_client = connection_azrblob('blob_storage')
    files = get_source_files_from_blob_storage(blob_service_client, 'OOCR', 'processed')



