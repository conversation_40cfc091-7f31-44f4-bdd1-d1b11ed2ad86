/* This script implements Grants to existing objects for all users and roles of the data vault.
 * must be executed as "DV_D_OWNER_DATABASE"
 * It will be needed...
     * after adding a new role or technical user
     * to repair access problems  
 * Keep in mind, that the final statement generates the necessary grant statements, which then have
 * to be executed 
 */

/* 
 * Direct Execution  Part
 */

/* 
 * Generator Part
 */
-- DV_D_ACCESS_WRITE
SELECT
    CASE WHEN lower(table_schema) like 'stage%'
        THEN 'GRANT INSERT, SELECT, UPDATE, DELETE, TRUNCATE ON ' || table_schema ||'.'|| table_name || ' TO DV_D_ACCESS_WRITE;'
        ELSE 'GRANT INSERT, SELECT, UPDATE ON ' || table_schema ||'.'|| table_name || ' TO DV_D_ACCESS_WRITE;' END AS sqlstatement
FROM information_schema.tables
where lower(table_schema) like '%rvlt%' OR lower(table_schema) like '%bvlt%'

UNION ALL
--
-- DVF_Z_READ
--
-- all current bvlt objects
SELECT 'GRANT SELECT ON ' || table_schema ||'.'|| table_name  || ' TO DV_D_ACCESS_VAULT_READ;' as sqlstatement
FROM information_schema.tables
WHERE lower(table_schema) like '%rvlt%' OR lower(table_schema) like '%bvlt%'




