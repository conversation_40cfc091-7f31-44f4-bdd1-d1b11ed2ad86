{"dvpd_version": "0.6.2", "stage_properties": [{"stage_schema": "stage_rvlt", "stage_table_name": "rbbe_order_orderitem_p1_stage"}], "pipeline_name": "rbbe_order_orderitem_p1", "record_source_name_expression": "billbee.order.orderItem", "data_extraction": {"fetch_module_name": "billbee api", "parse_module_name": "json_array", "load_module_name": "python_framework", "json_array_path": "$.Data.[*].orderItems"}, "fields": [{"field_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>rId", "json_path": "<PERSON><PERSON><PERSON><PERSON><PERSON>rId", "json_loop_level": -1, "field_type": "NUMBER(36,0)", "targets": [{"table_name": "RBBE_ORDER_HUB"}]}, {"field_name": "<PERSON><PERSON><PERSON><PERSON>", "json_path": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "field_type": "NUMBER(36,0)", "targets": [{"table_name": "RBBE_ORDER_ITEM_HUB"}]}, {"field_name": "product_BillbeeId", "json_path": "Product.Bill<PERSON>Id", "field_type": "NUMBER(36,0)", "targets": [{"table_name": "RBBE_PRODUCT_HUB", "column_name": "<PERSON><PERSON><PERSON><PERSON>"}]}, {"field_name": "Quantity", "json_path": "Quantity", "field_type": "NUMBER(20,2)", "targets": [{"table_name": "RBBE_ORDER_ITEM_ORDER_P1_L10_SAT"}]}, {"field_name": "TotalPrice", "json_path": "TotalPrice", "field_type": "NUMBER(20,2)", "targets": [{"table_name": "RBBE_ORDER_ITEM_ORDER_P1_L10_SAT"}]}, {"field_name": "TaxAmount", "json_path": "TaxAmount", "field_type": "NUMBER(20,2)", "targets": [{"table_name": "RBBE_ORDER_ITEM_ORDER_P1_L10_SAT"}]}, {"field_name": "TaxIndex", "json_path": "TaxIndex", "field_type": "NUMBER(20,2)", "targets": [{"table_name": "RBBE_ORDER_ITEM_ORDER_P1_L10_SAT"}]}, {"field_name": "Discount", "json_path": "Discount", "field_type": "NUMBER(20,2)", "targets": [{"table_name": "RBBE_ORDER_ITEM_ORDER_P1_L10_SAT"}]}, {"field_name": "UnrebatedTotalPrice", "json_path": "UnrebatedTotalPrice", "field_type": "NUMBER(20,2)", "targets": [{"table_name": "RBBE_ORDER_ITEM_ORDER_P1_L10_SAT"}]}, {"field_name": "InvoiceSKU", "json_path": "InvoiceSKU", "field_type": "VARCHAR(200)", "targets": [{"table_name": "RBBE_ORDER_ITEM_ORDER_P1_L10_SAT"}]}, {"field_name": "Product_Title", "json_path": "Product.Title", "field_type": "VARCHAR(1000)", "targets": [{"table_name": "RBBE_ORDER_ITEM_ORDER_P1_L10_SAT"}]}, {"field_name": "Product_SKU", "json_path": "Product.SKU", "field_type": "VARCHAR(200)", "targets": [{"table_name": "RBBE_ORDER_ITEM_ORDER_P1_L10_SAT"}]}, {"field_name": "Product_EAN", "json_path": "Product.EAN", "field_type": "VARCHAR(200)", "targets": [{"table_name": "RBBE_ORDER_ITEM_ORDER_P1_L10_SAT"}]}, {"field_name": "Product_CountryOfOrigin", "json_path": "Product.CountryOfOrigin", "field_type": "VARCHAR(200)", "targets": [{"table_name": "RBBE_ORDER_ITEM_ORDER_P1_L10_SAT"}]}, {"field_name": "Product_TARICCode", "json_path": "Product.TARICCode", "field_type": "VARCHAR(200)", "targets": [{"table_name": "RBBE_ORDER_ITEM_ORDER_P1_L10_SAT"}]}, {"field_name": "LastModifiedAt", "json_path": "LastModifiedAt", "json_loop_level": -1, "field_type": "TIMESTAMP", "targets": [{"table_name": "RBBE_ORDER_ITEM_ORDER_P1_L10_SAT", "exclude_from_change_detection": "true"}]}], "data_vault_model": [{"schema_name": "rvlt_billbee", "tables": [{"table_name": "RBBE_ORDER_ITEM_HUB", "table_stereotype": "hub", "hub_key_column_name": "HK_RBBE_ORDER_ITEM"}, {"table_name": "RBBE_ORDER_ITEM_ORDER_P1_L10_SAT", "table_stereotype": "sat", "satellite_parent_table": "RBBE_ORDER_ITEM_HUB", "diff_hash_column_name": "RH_RBBE_ORDER_ITEM_ORDER_P1_L10_SAT"}, {"table_name": "RBBE_ORDER_HUB", "table_stereotype": "hub", "hub_key_column_name": "HK_RBBE_ORDER"}, {"table_name": "RBBE_ORDER_ITEM_ORDER_LNK", "table_stereotype": "lnk", "link_key_column_name": "LK_RBBE_ORDER_ITEM_ORDER", "link_parent_tables": ["RBBE_ORDER_ITEM_HUB", "RBBE_ORDER_HUB"]}, {"table_name": "RBBE_ORDER_ITEM_ORDER_ESAT", "table_stereotype": "sat", "satellite_parent_table": "RBBE_ORDER_ITEM_ORDER_LNK", "tracked_relation_name": "/", "driving_keys": ["HK_RBBE_ORDER_ITEM"]}, {"table_name": "RBBE_PRODUCT_HUB", "table_stereotype": "hub", "hub_key_column_name": "HK_RBBE_PRODUCT"}, {"table_name": "RBBE_ORDER_ITEM_PRODUCT_LNK", "table_stereotype": "lnk", "link_key_column_name": "LK_RBBE_ORDER_ITEM_PRODUCT", "link_parent_tables": ["RBBE_ORDER_ITEM_HUB", "RBBE_PRODUCT_HUB"]}, {"table_name": "RBBE_ORDER_ITEM_PRODUCT_ESAT", "table_stereotype": "sat", "satellite_parent_table": "RBBE_ORDER_ITEM_PRODUCT_LNK", "driving_keys": ["HK_RBBE_ORDER_ITEM"]}]}]}