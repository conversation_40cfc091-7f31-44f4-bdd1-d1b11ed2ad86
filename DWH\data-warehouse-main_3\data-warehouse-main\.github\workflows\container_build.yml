name: Build and Push Docker Image

on:
  push:
    branches:
      - main

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout repository
      uses: actions/checkout@v2

    - name: Log in to Azure Container Registry
      uses: azure/docker-login@v1
      with:
        login-server: ${{ secrets.ACR_LOGIN_SERVER }}
        username: ${{ secrets.ACR_USERNAME }}
        password: ${{ secrets.ACR_PASSWORD }}

    - name: Build Docker image
      run: |
        docker build -t ${{ secrets.ACR_LOGIN_SERVER }}/sund-di:${{ github.sha }} .

    - name: Push Docker image
      run: |
        docker push ${{ secrets.ACR_LOGIN_SERVER }}/sund-di:${{ github.sha }}
