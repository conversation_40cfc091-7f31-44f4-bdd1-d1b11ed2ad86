Data vault pipeline developer cheat sheet 
rendered from  rsap_rin1_j1.dvpi

pipeline name:  rsap_rin1_j1

------------------------------------------------------
record source:  sap.rin1

Source fields:
       COMPANY    Varchar(50)
       DOCENTRY   INTEGER
       LINENUM    INTEGER
       ITEMCODE   Varchar(50)
       JSON_TEXT  VARCHAR


------------------------------------------------------
Table List:
stage_table.rvlt_sap.rsap_rin1_j1_stage
table.rvlt_sap.rsap_credit_memo_line_rin1_hub
table.rvlt_sap.rsap_credit_memo_line_rin1_j1_l10_sat
table.rvlt_sap.rsap_item_oitm_hub
table.rvlt_sap.rsap_credit_memo_line_rin1_item_oitm_lnk
table.rvlt_sap.rsap_credit_memo_line_rin1_item_oitm_esat

------------------------------------------------------
stage table:  stage_rvlt.rsap_rin1_j1_stage
Field to Stage mapping:
	--business keys,
		COMPANY    >  COMPANY,
		DOCENTRY   >  DOCENTRY,
		ITEMCODE   >  ITEMCODE,
		LINENUM    >  LINENUM,

	--content,
		JSON_TEXT  >  JSON_TEXT

------------------------------------------------------
Hash value composition

HK_RSAP_CREDIT_MEMO_LINE_RIN1 (key)
		COMPANY 
		DOCENTRY 
		LINENUM 

HK_RSAP_ITEM_OITM (key)
		COMPANY 
		ITEMCODE 

LK_RSAP_CREDIT_MEMO_LINE_RIN1_ITEM_OITM (key)
		COMPANY 
		DOCENTRY 
		LINENUM 
		COMPANY 
		ITEMCODE 

RH_RSAP_CREDIT_MEMO_LINE_RIN1_J1_L10_SAT (diff_hash)
		JSON_TEXT 


------------------------------------------------------
Table load method
(STAGE >  VAULT)

rsap_credit_memo_line_rin1_hub (/) can be loaded by convention
		  key: HK_RSAP_CREDIT_MEMO_LINE_RIN1  >  HK_RSAP_CREDIT_MEMO_LINE_RIN1
		  business_key: COMPANY  >  COMPANY 
		  business_key: DOCENTRY  >  DOCENTRY 
		  business_key: LINENUM  >  LINENUM 

rsap_credit_memo_line_rin1_j1_l10_sat (/) can be loaded by convention
		  parent_key: HK_RSAP_CREDIT_MEMO_LINE_RIN1  >  HK_RSAP_CREDIT_MEMO_LINE_RIN1
		  diff_hash: RH_RSAP_CREDIT_MEMO_LINE_RIN1_J1_L10_SAT  >  RH_RSAP_CREDIT_MEMO_LINE_RIN1_J1_L10_SAT
		  content: JSON_TEXT  >  JSON_TEXT 

rsap_item_oitm_hub (/) can be loaded by convention
		  key: HK_RSAP_ITEM_OITM  >  HK_RSAP_ITEM_OITM
		  business_key: COMPANY  >  COMPANY 
		  business_key: ITEMCODE  >  ITEMCODE 

rsap_credit_memo_line_rin1_item_oitm_lnk (/) can be loaded by convention
		  parent_key_1: HK_RSAP_CREDIT_MEMO_LINE_RIN1  >  HK_RSAP_CREDIT_MEMO_LINE_RIN1
		  parent_key_2: HK_RSAP_ITEM_OITM  >  HK_RSAP_ITEM_OITM
		  key: LK_RSAP_CREDIT_MEMO_LINE_RIN1_ITEM_OITM  >  LK_RSAP_CREDIT_MEMO_LINE_RIN1_ITEM_OITM

rsap_credit_memo_line_rin1_item_oitm_esat (/) can be loaded by convention
		! Driving keys: HK_RSAP_CREDIT_MEMO_LINE_RIN1 
		  parent_key: LK_RSAP_CREDIT_MEMO_LINE_RIN1_ITEM_OITM  >  LK_RSAP_CREDIT_MEMO_LINE_RIN1_ITEM_OITM

