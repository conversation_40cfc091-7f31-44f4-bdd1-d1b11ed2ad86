# data-warehouse

## Project file structure and configuration

| type of file/filegroup                             | location in the project directory structure                       | name pattern                   |
|----------------------------------------------------|-------------------------------------------------------------------|--------------------------------|
| python script with library function                | `/lib`                                                            |                                |
| python script with entry point of process          | `/processes/<name of pipeline>`                                   | `/__main__.py`                 |
| ddl script of dwh database object (table/view/...) | `/resources/<database specific ddl directory>/<schema directory>` |                                |
| dvpd documents                                     | `/resources/dvpd`                                                 | `<pipeline_name>.dvpd.json`    |
| dvpi documents                                     | `/resources/dvpi`                                                 | `<pipeline_name>.dvpi.json`    |
| developer sheets                                   | `/resources/documentation`                                        | `<pipeline_name>.devsheet.txt` | 
| configuration                                      | `/config`                                                         | `<system>.ini`                 |




## Config process
The reading of configuration parameters in the script `lib/configuration.py`.
A unified approach to manage application configurations from Azure Key Vault, environment variables, and INI files is provided which allows loading configurations from these sources in a prioritized order and returns them into a single key-value dictionary.

- configuration properties are identified via three layers
  - system
  - object
  - property name
- configuration data will be loaded defining system and object 
- configuration data is available in the process as dictionary of property names and values.
- property names will be the same for the same kind of object (e.g. the property names for a database connection will be the same)

####  System
The systems are defined by the overall architecture of the dwh platform and the connected sources and targets. The DWH by itself
is also a system, or even might consist of multiple systems, e.g. Snowflake, Azure SQL, BlobStorage etc.

It is recommended to manage the different environments by providing different values for the same System Name. This allows hard coded
names in the python scrips.

Nevertheless, system can also be used for separation of stage (dev/test/prod) by defining the system name, depending on the environment. 


#### Object
Systems might provide different objects of the same kind. (e.g. a database might be connected with different users, depending on
the purpose of the connection (owner / writer / reader))


#### Property name
The property names are defined by the module, that consumes the configuration data. e.g. the function to create a postgresql
database connection always expects specific property names, regardless of the system or object it builds the connection to.

### Examples 

Configuration property

| Description                         | system.object.property_name                                                              | dictionary                              |
|-------------------------------------|------------------------------------------------------------------------------------------|-----------------------------------------|
| DB connection to snowflake as owner | dwh_snf.owner.host<br/>dwh_snf.owner.port<br/>dwh_snf.owner.user<br/>dwh_snf.owner.password | host:<br/>port:<br/>user:<br/>password: |


### 1. **Azure Key Vault**
Azure Key Vault is the highest priority source. The necessary configuration data are being fetched from Azure Key Vault where they are stored in key-value format.

*Examples*
```
{"data_owner": {"host": "abc",
    "port": "my_port",
    "user": "my_user",
    "password" : "my_password"}
```

### 2. **Environment Variables**
The next source is environment variables. The environment variable names follow the pattern `PE.<SYSTEM>.<OBJECT><PROPERTY_NAME>`. If the required configuration is not found in Azure Key Vault, the module will attempt to load it from environment variables.

*Examples*
```
PE.DWH_SNF.OWNER.DATABASE
PE.DWH_SNF.OWNER.HOST
PE.DWH_SNF.OWNER.USER
PE.AZRKEYVAULT.KEY_VAULT.URL
```

### 3. **ini Files**
Ini files are used as a last-resort backup for local development or testing. They are stored in a `config` directory of the project root and are only considered if the configurations are not found in Azure Key Vault or environment variables. Templates for the INI file are provided in the config_template folder. These templates should be copied to the config directory and filled with the appropriate configuration values.

### 4. **Configuration Consolidation via `configuration_read`**
The `configuration_read` function attempts to load the configuration data from these sources in the following order:
1. **Azure Key Vault**: First, it checks if the configuration is available in Azure Key Vault via `configuration_load_azrkeyvault`.
2. **Environment variables**: If the configuration is not found in Key Vault, it then checks the environment variables via `configuration_load_env`.
3. **Ini files**: As a last resort, the function checks the INI files via `configuration_load_ini`.

**python code to use it**:

````python
    # just retrieve settings for the owner connection
    owner_connection_param=configuration_load("dwh_snf","data_owner")
    
    # call snowflake connection builder, that will read configuration internally
    connection=connection_snf("dwh_snf","data_owner", mandatory_elements=['host', 'port', 'user', 'password'])
````

The function ensures that all mandatory configuration elements are found in one source. **If some mandatory elements are found in one source, the remaining elements will not be searched in other sources**, and an error will be raised. No partial configurations use is allowed.


## Pipeline structure and execution

All pipelines are located in `processes/` folder. Entry point of each pipeline is `__main__.py` file. The loading logic is written in `load_vault_1.py`.

The execution logic of `__main__.py` file is set in `main()` function. There are main steps that are always present (see any pipeline in `\processes`:
1. cimt job instance setup and initialisation,
2. deployment of datamodel
3. load of data to data vault
4. end cimt job instance

The execution logic of `load_vault_1.py` is set in `load_vault_1()` function. Main steps are:
1. Fetch and stage data (depending on datasource, fetch data step can also take place in already in `main()` of `__main__.py`)
2. Load data to data vault


Depending on a data source and possibilities to fetch data from it, additional steps could be incorporated. Such "specialties" are commented directly in code of following pipelines per system:
1. processes\rsap_hld1_j1
2. processes\rbbe_order_p1
3. processes\rmud_planzahlen_jaehrlich_p1
4. processes\--TODO PIM

## Common steps
1. Create DVPD.
2. Create DDLs. 
3. Create new pipeline in `processes`.
4. Set pipeline up (datamodel deployment, data staging, data vault loading etc).
5. Test pipeline.
6. Deploy pipeline.