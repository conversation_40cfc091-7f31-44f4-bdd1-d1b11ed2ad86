import os
from enum import Enum

import snowflake.connector as snow
import sys
from lib.configuration import configuration_read
from lib.dvf_basics import Dvf_dwh_connection_type


def error_print(*args, **kwargs):
    """Print a message to stderr"""
    print(*args, file=sys.stderr, **kwargs)

def connection_snf_for_dwh_connection_type(dwh_connection_type):
    return connection_snf(dwh_connection_type.value)

def connection_snf(object):
    """Reads configuration for the resource from environment and returns a new snowflake connection"""
    system = 'dwh_snf'
    database = None
    user = None
    host = None
    port = None
    role = None
    warehouse = None
    try:
        # for LOCAL postgres
        params = configuration_read(system, object,['database','user','password','account','role', 'warehouse'])
        account = params['account']
        database = params['database']
        user = params['user']
        password = params['password']
        role = params['role']
        warehouse = params['warehouse']
        connection = snow.connect(database=database, user=user, account=account, password=password, role=role, warehouse=warehouse)
    except Exception as error:
        error_print('Connection Error:', error)
        #error_print('ini file used:', ini_file_name)
        error_print('Object used:', object)
        raise
    return connection


if __name__ == '__main__':
    # small test
    conn = connection_snf_for_dwh_connection_type(Dvf_dwh_connection_type.owner)