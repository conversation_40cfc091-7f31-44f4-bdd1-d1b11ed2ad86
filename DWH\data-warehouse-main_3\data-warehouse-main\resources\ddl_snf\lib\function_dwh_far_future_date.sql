CREATE OR REPLACE FUNCTION LIB.DWH_FAR_FUTURE_DATE()
RETURNS TIMESTAMP_NTZ(9)
LANGUAGE SQL
MEMOIZABLE COMMENT='Provides the date constant, that is used as load enddate in the currently valid rows'
AS '
    ''2299-12-30 00:00:00.000''::TIMESTAMP
';

--select dwh_far_future_date();

--/* result should show              1234|             12.34|  [NULL]            | [NULL]        | [NULL]           */
      
