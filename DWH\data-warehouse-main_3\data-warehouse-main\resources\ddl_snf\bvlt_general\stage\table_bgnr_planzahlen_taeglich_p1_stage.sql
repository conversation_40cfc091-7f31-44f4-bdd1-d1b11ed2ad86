-- generated script for stage_bvlt.bgnr_planzahlen_taeglich_p1_stage

-- DROP TABLE stage_bvlt.bgnr_planzahlen_taeglich_p1_stage;

CREATE TABLE stage_bvlt.bgnr_planzahlen_taeglich_p1_stage (
--metadata,
MD_INSERTED_AT TIMESTAMP NOT NULL,
MD_RUN_ID INT NOT NULL,
MD_RECORD_SOURCE VARCHAR(255) NOT NULL,
MD_IS_DELETED BOOLEAN NOT NULL,
--hash keys,
GH_BGNR_GESELLSCHAFT_TAGESPLANZAHL_V1_B10_MSAT CHAR(28) NOT NULL,
HK_RGNR_GESELLSCHAFT CHAR(28) NOT NULL,
LK_RGNR_GESELLSCHAFT_KALENDER CHAR(28) NOT NULL,
--business keys,
COMPANY VARCHAR(50) NULL,
TAGESDATUM DATE NULL,
--content,
GEPLANTER_UMSATZ NUMBER(20,2) NULL,
KUNDENGRUPPE VARCHAR(100) NULL,
V<PERSON><PERSON><PERSON><PERSON><PERSON>ERNUMMER VARCHAR(100) NULL,
<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>BI<PERSON> VARCHAR(100) NULL
);
-- end of script --