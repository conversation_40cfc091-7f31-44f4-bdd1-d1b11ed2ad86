import json

from lib.json_utils import get_json_text_content_column, sort_json_content_column
from lib.blobstorage_utils import fetch_json_source_file_from_blob_container
from lib.cimtjobinstance import CimtJobInstance, cimtjobinstance_job
from lib.connection_snf import  connection_snf_for_dwh_connection_type

from lib.dvf_basics import dvf_assemble_datavault_hash, Dvf_dwh_connection_type
from lib.dvf_sqlByConvention_snf import dvf_get_datavault_hub_elt_sql, \
    dvf_get_datavault_msat_elt_sql, dvf_execute_elt_statement_list, dvf_get_check_hash_collision_hub_elt_sql, dvf_get_check_singularity_sat_elt_sql, \
    dvf_get_datavault_dlnk_elt_sql, dvf_get_check_hash_collision_dlnk_elt_sql

from lib.dbutils_snf import get_snf_dict_insert_sql, execute_snf_dict_bulk_insert



@cimtjobinstance_job
def fetch_and_stage_data(parent_job_instance, dwh_connection, **kwargs):

    my_job_instance = CimtJobInstance(kwargs['instance_job_name'], parent_job_instance)
    my_job_instance.start_instance()

    try:
        # ### FRAMEWORK PHASE: do processing here

        # prepare stage insert operation
        dwh_cursor = dwh_connection.cursor()
        dwh_cursor.execute("TRUNCATE TABLE stage_bvlt.bgnr_planzahlen_taeglich_p1_stage")
        insert_statement = get_snf_dict_insert_sql(dwh_connection, "stage_bvlt", "bgnr_planzahlen_taeglich_p1_stage")
        stage_data_row = {'md_inserted_at': my_job_instance.get_job_started_at().isoformat(),
                    'md_record_source': 'bgnr_planzahlen_taeglich_p1',
                    'md_run_id': my_job_instance.get_job_instance_id(),
                    'md_is_deleted': False
                   }

        # connect to source, and order data
        source_connection = connection_snf_for_dwh_connection_type(Dvf_dwh_connection_type.owner)
        source_cursor = source_connection.cursor()

        source_sql = """ with anzahl_wertage_pro_monat as (
                            select g_h.company, year(tagesdatum) as jahr, month(tagesdatum) as monat, count_if(ist_werktag) as anzahl_werktage
                            from rvlt_general.rgnr_gesellschaft_hub g_h
                            join rvlt_general.rgnr_gesellschaft_kalender_dlnk k_dlnk on k_dlnk.hk_rgnr_gesellschaft= g_h.hk_rgnr_gesellschaft
                            join bvlt_general.bgnr_gesellschaft_kalender_p1_b10_sat k_sat on k_sat.lk_rgnr_gesellschaft_kalender = k_dlnk.lk_rgnr_gesellschaft_kalender
                                and k_sat.md_valid_before = lib.dwh_far_future_date()
                                and not k_sat.md_is_deleted
                                and k_sat.md_record_source <> 'SYSTEM'
                            group by all
                        ), anzahl_wertage_pro_jahr as (
                            select company, jahr, sum(anzahl_werktage) as anzahl_werktage
                            from anzahl_wertage_pro_monat
                            group by all
                        ),geplanter_jahresumsatz_pro_tag as (                 
                            select g_h.company, 
                                    j_msat.planzahl, 
                                    j_msat.verkaufsgebiet, 
                                    j_msat.verkaeufernummer,
                                    j_msat.kundengruppe,
                                    anzahl_werktage,
                                    j_msat.planzahl/anzahl_werktage as geplanter_umsatz,
                                    j_dlnk.jahr,
                                    g_h.hk_rgnr_gesellschaft
                            from rvlt_general.rgnr_gesellschaft_hub g_h
                            join rvlt_manual_data.rmud_rgnr_gesellschaft_jahresplanzahl_dlnk j_dlnk on j_dlnk.hk_rgnr_gesellschaft=g_h.hk_rgnr_gesellschaft
                            join rvlt_manual_data.rmud_rgnr_gesellschaft_jahresplanzahl_p1_l20_msat j_msat on j_msat.lk_rmud_rgnr_gesellschaft_jahresplanzahl = j_dlnk.lk_rmud_rgnr_gesellschaft_jahresplanzahl
                                and j_msat.md_record_source <> 'SYSTEM'
                                and j_msat.md_valid_before = lib.dwh_far_future_date()
                                and not j_msat.md_is_deleted
                            left join anzahl_wertage_pro_jahr aw on aw.company = case when g_h.company = 'SUND_DIGITAL' then 'DEISS_DE' else g_h.company end
                                and aw.jahr = j_dlnk.jahr
                        ), geplanter_monatsumsatz_pro_tag as(
                                select g_h.company, 
                                    m_msat.planzahl, 
                                    m_msat.verkaufsgebiet, 
                                    m_msat.verkaeufernummer,
                                    m_msat.kundengruppe,
                                    anzahl_werktage,
                                    m_msat.planzahl/anzahl_werktage as geplanter_umsatz,
                                    m_dlnk.jahr,
                                    m_dlnk.monat,
                                    g_h.hk_rgnr_gesellschaft
                            from rvlt_general.rgnr_gesellschaft_hub g_h
                            join rvlt_manual_data.rmud_rgnr_gesellschaft_monatsplanzahl_dlnk m_dlnk on m_dlnk.hk_rgnr_gesellschaft=g_h.hk_rgnr_gesellschaft
                            join rvlt_manual_data.rmud_rgnr_gesellschaft_monatsplanzahl_p1_l20_msat m_msat on m_msat.lk_rmud_rgnr_gesellschaft_monatsplanzahl = m_dlnk.lk_rmud_rgnr_gesellschaft_monatsplanzahl
                                and m_msat.md_record_source <> 'SYSTEM'
                                and m_msat.md_valid_before = lib.dwh_far_future_date()
                                and not m_msat.md_is_deleted
                            left join anzahl_wertage_pro_monat aw on aw.company = case when g_h.company = 'SUND_DIGITAL' then 'DEISS_DE' else g_h.company end
                                and aw.jahr = m_dlnk.jahr
                                and aw.monat = m_dlnk.monat
                        )
                        select gjt.company, 
                            tagesdatum,
                            iff(k_sat.ist_werktag, gjt.geplanter_umsatz, 0) as geplanter_umsatz,
                            kundengruppe,
                            verkaeufernummer,
                            verkaufsgebiet,
                            gjt.hk_rgnr_gesellschaft
                        from geplanter_jahresumsatz_pro_tag gjt
                        join rvlt_general.rgnr_gesellschaft_hub g_h on case when gjt.company = 'SUND_DIGITAL' then 'DEISS_DE' else gjt.company end = g_h.company
                        join rvlt_general.rgnr_gesellschaft_kalender_dlnk k_dlnk on k_dlnk.hk_rgnr_gesellschaft= g_h.hk_rgnr_gesellschaft
                            and year(k_dlnk.tagesdatum) = gjt.jahr
                        join bvlt_general.bgnr_gesellschaft_kalender_p1_b10_sat k_sat on k_sat.lk_rgnr_gesellschaft_kalender = k_dlnk.lk_rgnr_gesellschaft_kalender
                            and k_sat.md_valid_before = lib.dwh_far_future_date()
                            and not k_sat.md_is_deleted
                            and k_sat.md_record_source <> 'SYSTEM'
                        
                        union all
                        
                        select gmt.company, 
                            tagesdatum,
                            iff(k_sat.ist_werktag, gmt.geplanter_umsatz, 0) as geplanter_umsatz,
                            kundengruppe,
                            verkaeufernummer,
                            verkaufsgebiet,
                            gmt.hk_rgnr_gesellschaft
                        from geplanter_monatsumsatz_pro_tag gmt
                        join rvlt_general.rgnr_gesellschaft_hub g_h on case when gmt.company = 'SUND_DIGITAL' then 'DEISS_DE' else gmt.company end  = g_h.company 
                        join rvlt_general.rgnr_gesellschaft_kalender_dlnk k_dlnk on k_dlnk.hk_rgnr_gesellschaft= g_h.hk_rgnr_gesellschaft
                            and year(k_dlnk.tagesdatum) = gmt.jahr
                            and month(k_dlnk.tagesdatum) = gmt.monat
                        join bvlt_general.bgnr_gesellschaft_kalender_p1_b10_sat k_sat on k_sat.lk_rgnr_gesellschaft_kalender = k_dlnk.lk_rgnr_gesellschaft_kalender
                            and k_sat.md_valid_before = lib.dwh_far_future_date()
                            and not k_sat.md_is_deleted
                            and k_sat.md_record_source <> 'SYSTEM'; """

        source_cursor.execute(source_sql)
        # transform  and stage the source rows into stage
        source_rows = source_cursor.fetchall()

        stage_data_rows = []

        group_hash_diff = dict()
        for source_row in source_rows:
            stage_data_row_temp = dict(stage_data_row)

            stage_data_row_temp['company'] = source_row[0]
            stage_data_row_temp['tagesdatum'] = source_row[1]
            stage_data_row_temp['geplanter_umsatz'] = source_row[2]
            stage_data_row_temp['kundengruppe'] = source_row[3]
            stage_data_row_temp['verkaeufernummer'] = source_row[4]
            stage_data_row_temp['verkaufsgebiet'] = source_row[5]
            stage_data_row_temp['hk_rgnr_gesellschaft'] = source_row[6]

            # construct value for group hash diff
            key = '_'.join(
                [str(stage_data_row_temp['tagesdatum']), stage_data_row_temp['company']])
            group_hash_diff.setdefault(key, []).append(stage_data_row_temp['geplanter_umsatz'])
            group_hash_diff.setdefault(key, []).append(stage_data_row_temp['kundengruppe'])
            group_hash_diff.setdefault(key, []).append(stage_data_row_temp['verkaeufernummer'])
            group_hash_diff.setdefault(key, []).append(stage_data_row_temp['verkaufsgebiet'])

            stage_data_rows.append(stage_data_row_temp)
            my_job_instance.count_input(1)

        # sort value per key and create a string from values for a group hash calculation
        for k, v in group_hash_diff.items():
            values_list = sorted([str(item) for item in v])
            group_hash_diff[k] = '||'.join(values_list)

        for key, group_hash_diff_item in group_hash_diff.items():
            for stage_row in stage_data_rows:
                ### hashes
                if '_'.join([str(stage_row['tagesdatum']), stage_row['company']]) == key:
                    stage_row['gh_bgnr_gesellschaft_tagesplanzahl_v1_b10_msat'] = dvf_assemble_datavault_hash([group_hash_diff_item])
                stage_row['lk_rgnr_gesellschaft_kalender'] = dvf_assemble_datavault_hash([stage_row['tagesdatum'], stage_row['company']])

        execute_snf_dict_bulk_insert(dwh_cursor, insert_statement, stage_data_rows)
        dwh_connection.commit()

    # ### FRAMEWORK PHASE: Log any exception to instance
    except Exception:
        my_job_instance.end_instance_with_error(1, 'something went wrong')
        raise

    # # ### FRAMEWORK PHASE: End the instance normally
    my_job_instance.end_instance()


@cimtjobinstance_job
def load_data_to_vault(parent_job_instance, dwh_connection, file_name=None, **kwargs):


    # ### FRAMEWORK PHASE: setup your job_instance
    my_job_instance = CimtJobInstance(kwargs['instance_job_name'], parent_job_instance)
    my_job_instance.set_work_item(file_name)
    my_job_instance.start_instance()
    try:

        # ### FRAMEWORK PHASE: do processing here
        # BEGIN LOAD DATA TO VLT PART
        # general constants
        stage_schema = 'stage_bvlt'
        stage_table = 'bgnr_planzahlen_taeglich_p1_stage'


        dwh_cursor = dwh_connection.cursor()


        hash_collision_check_statement_list = dvf_get_check_hash_collision_dlnk_elt_sql(vault_table='rgnr_gesellschaft_kalender_dlnk',
        vault_schema='rvlt_general',
        stage_lk_column='LK_RGNR_GESELLSCHAFT_KALENDER',
        stage_hk_column_list=['HK_RGNR_GESELLSCHAFT'],
        stage_dc_column_list=['TAGESDATUM'],
        db_connection=dwh_connection,
        stage_schema=stage_schema,
        stage_table=stage_table)

        dvf_execute_elt_statement_list(dwh_cursor, hash_collision_check_statement_list)

        statement_list = dvf_get_datavault_dlnk_elt_sql(vault_table='rgnr_gesellschaft_kalender_dlnk',
        vault_schema='rvlt_general',
        stage_lk_column='LK_RGNR_GESELLSCHAFT_KALENDER',
        stage_hk_column_list=['HK_RGNR_GESELLSCHAFT'],
        stage_dc_column_list=['TAGESDATUM'],
        db_connection=dwh_connection,
        stage_schema=stage_schema,
        stage_table=stage_table,
        meta_job_instance_id = my_job_instance.get_job_instance_id(),
        meta_inserted_at = my_job_instance.get_job_started_at() )

        dvf_execute_elt_statement_list(dwh_cursor, statement_list)

        # ----------


        statement_list = dvf_get_datavault_msat_elt_sql(vault_table='bgnr_gesellschaft_tagesplanzahl_v1_b10_msat',
        vault_schema='bvlt_general',
        stage_hk_column='LK_RGNR_GESELLSCHAFT_KALENDER',
        stage_gh_column='GH_BGNR_GESELLSCHAFT_TAGESPLANZAHL_V1_B10_MSAT',
        stage_content_column_list=['GEPLANTER_UMSATZ', 'KUNDENGRUPPE', 'VERKAEUFERNUMMER', 'VERKAUFSGEBIET'],
        with_deletion_detection=False,
        db_connection=dwh_connection,
        stage_schema=stage_schema,
        stage_table=stage_table,
        meta_job_instance_id = my_job_instance.get_job_instance_id(),
        meta_inserted_at = my_job_instance.get_job_started_at() )

        dvf_execute_elt_statement_list(dwh_cursor, statement_list)

        # ----------


        # END LOAD DATA TO VLT PART

        dwh_connection.commit()
    # ### FRAMEWORK PHASE: Log any exception to instance
    except Exception:
        my_job_instance.end_instance_with_error(1, 'something went wrong')
        raise
    # # ### FRAMEWORK PHASE: End the instance normally
    my_job_instance.end_instance()



@cimtjobinstance_job
def load_vault_1(parent_job_instance=None, **kwargs):

    # ### FRAMEWORK PHASE: setup your job_instance
    my_job_instance = CimtJobInstance(kwargs['instance_job_name'], parent_job_instance)
    #my_job_instance.set_work_item(file_name)
    my_job_instance.start_instance()

    try:
        # ### FRAMEWORK PHASE: do processing here
        dwh_connection = connection_snf_for_dwh_connection_type(Dvf_dwh_connection_type.raw_vault)
        fetch_and_stage_data(my_job_instance, dwh_connection)
        load_data_to_vault(my_job_instance, dwh_connection)
        dwh_connection.close()

    # ### FRAMEWORK PHASE: Log any exception to instance
    except Exception:
        my_job_instance.end_instance_with_error(1, 'some processing failed')
        raise

    # # ### FRAMEWORK PHASE: End the instance normally
    my_job_instance.end_instance()


if __name__ == '__main__':
    # local execution
    load_vault_1()