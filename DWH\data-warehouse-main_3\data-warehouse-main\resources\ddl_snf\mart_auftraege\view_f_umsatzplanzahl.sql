
/* select * from  mart_auftraege.f_umsatzplanzahl order by  gesellschaft, tagesdatum;
 * select tagesdatum, count(gesellschaft) from  mart_auftraege.f_umsatzplanzahl  group by 1 order by   tagesdatum;
   
 */

CREATE OR replace VIEW mart_auftraege.f_umsatzplanzahl AS 

-- Final dataset with last lookups, in row calculations and target column naming
select 
		gesellschaft_hub.company 								as Gesellschaft
		,kalender_dlnk.tagesdatum								as tagesdatum
		,tplan_msat.verkaufsgebiet								as verkaufsgebiet			
		,tplan_msat.verkaeufernummer							as verkaeufernummer
		,tplan_msat.kundengruppe								as kundengruppe
		,tplan_msat.geplanter_umsatz							as geplanter_umsatz_eur
from rvlt_general.rgnr_gesellschaft_hub gesellschaft_hub
join rvlt_general.rgnr_gesellschaft_kalender_dlnk kalender_dlnk 
	 on kalender_dlnk.hk_rgnr_gesellschaft = gesellschaft_hub.hk_rgnr_gesellschaft 
join BVLT_GENERAL.BGNR_GESELLSCHAFT_TAGESPLANZAHL_V1_B10_MSAT tplan_msat
     on tplan_msat.lk_rgnr_gesellschaft_kalender = kalender_dlnk.lk_rgnr_gesellschaft_kalender 
     and tplan_msat.md_valid_before = lib.dwh_far_future_date()
     and not tplan_msat.md_is_deleted ;
    
Grant select on VIEW mart_auftraege.f_umsatzplanzahl to role DV_D_ACCESS_MART_READ;    
	 
/*
  select  company ,tagesdatum
  	from rvlt_general.rgnr_gesellschaft_hub gesellschaft_hub
	join rvlt_general.rgnr_gesellschaft_kalender_dlnk kalender_dlnk 
		 on kalender_dlnk.hk_rgnr_gesellschaft = gesellschaft_hub.hk_rgnr_gesellschaft 
  	join bvlt_general.
  
 */
		
