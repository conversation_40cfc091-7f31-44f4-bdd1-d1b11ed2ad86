# DVPD配置开发指南
## Data Vault Pipeline Definition 完整开发手册

### 版本信息
- **文档版本**: 1.0
- **DVPD版本**: 0.6.2
- **适用项目**: SUND数据仓库
- **创建日期**: 2024年

---

## 目录

1. [DVPD框架概述](#dvpd框架概述)
2. [配置文件结构](#配置文件结构)
3. [实际案例分析](#实际案例分析)
4. [开发最佳实践](#开发最佳实践)
5. [常见问题解决](#常见问题解决)
6. [性能优化](#性能优化)

---

## DVPD框架概述

### 核心理念
DVPD (Data Vault Pipeline Definition) 是一个声明式的数据管道定义框架，通过JSON配置文件自动生成Data Vault 2.0兼容的DDL脚本和ETL代码。

### 主要优势
- **声明式配置**: 通过JSON描述数据结构，自动生成代码
- **标准化**: 严格遵循Data Vault 2.0建模规范
- **自动化**: 减少手工编码，降低错误率
- **一致性**: 确保所有数据管道使用统一的模式
- **可维护性**: 配置即文档，易于理解和维护

### 框架组件
```
DVPD框架
├── 配置解析器 (Config Parser)
├── DDL生成器 (DDL Generator)
├── ETL代码生成器 (ETL Code Generator)
├── 数据质量检查器 (Quality Checker)
└── 部署管理器 (Deployment Manager)
```

---

## 配置文件结构

### 基础配置模板
```json
{
    "dvpd_version": "0.6.2",
    "stage_properties": [
        {
            "stage_schema": "stage_rvlt",
            "stage_table_name": "{pipeline_name}_stage"
        }
    ],
    "pipeline_name": "{source}_{entity}_{version}",
    "record_source_name_expression": "{source_system}.{entity}",
    "data_extraction": {
        "fetch_module_name": "none - ddl and cnode snippet generation only"
    },
    "fields": [],
    "data_vault_model": []
}
```

### 字段配置详解

#### 基础字段配置
```json
{
    "field_name": "company",
    "field_type": "VARCHAR(50)",
    "targets": [
        {
            "table_name": "RSAP_BUSINESS_PARTNER_OCRD_HUB"
        }
    ]
}
```

#### 复杂字段配置
```json
{
    "field_name": "json_text",
    "field_type": "VARCHAR",
    "targets": [
        {
            "table_name": "RSAP_CREDIT_MEMO_LINE_RIN1_RIN2_J1_L10_SAT",
            "exclude_json_paths_from_change_detection": [
                "UpdateDate",
                "LastModified",
                "ProcessingTimestamp"
            ]
        }
    ]
}
```

### Data Vault模型配置

#### Hub表配置
```json
{
    "table_name": "RSAP_BUSINESS_PARTNER_OCRD_HUB",
    "table_stereotype": "hub",
    "hub_key_column_name": "HK_RSAP_BUSINESS_PARTNER_OCRD"
}
```

#### Link表配置
```json
{
    "table_name": "RSAP_CREDIT_MEMO_LINE_RIN1_RIN2_DLNK",
    "table_stereotype": "lnk",
    "link_key_column_name": "LK_RSAP_CREDIT_MEMO_LINE_RIN1_RIN2",
    "link_parent_tables": [
        "RSAP_CREDIT_MEMO_LINE_RIN1_HUB"
    ]
}
```

#### Satellite表配置
```json
{
    "table_name": "RSAP_CREDIT_MEMO_LINE_RIN1_RIN2_J1_L10_SAT",
    "table_stereotype": "sat",
    "satellite_parent_table": "RSAP_CREDIT_MEMO_LINE_RIN1_RIN2_DLNK",
    "diff_hash_column_name": "RH_RSAP_CREDIT_MEMO_LINE_RIN1_RIN2_J1_L10_SAT"
}
```

---

## 实际案例分析

### 案例1: SAP RIN2贷项凭证行详情

#### 业务背景
RIN2表包含SAP系统中贷项凭证的行项目详细信息，需要与RIN1表建立父子关系。

#### 完整配置文件
```json
{
    "dvpd_version": "0.6.2",
    "stage_properties": [
        {
            "stage_schema": "stage_rvlt",
            "stage_table_name": "rsap_rin2_j1_stage"
        }
    ],
    "pipeline_name": "rsap_rin2_j1",
    "record_source_name_expression": "sap.rin2",
    "data_extraction": {
        "fetch_module_name": "none - ddl and cnode snippet generation only"
    },
    "fields": [
        {
            "field_name": "company",
            "field_type": "VARCHAR(50)",
            "targets": [
                {"table_name": "RSAP_CREDIT_MEMO_LINE_RIN1_HUB"}
            ]
        },
        {
            "field_name": "docentry",
            "field_type": "INTEGER",
            "targets": [
                {"table_name": "RSAP_CREDIT_MEMO_LINE_RIN1_HUB"}
            ]
        },
        {
            "field_name": "linenum",
            "field_type": "INTEGER",
            "targets": [
                {"table_name": "RSAP_CREDIT_MEMO_LINE_RIN1_HUB"}
            ]
        },
        {
            "field_name": "groupnum",
            "field_type": "INTEGER",
            "targets": [
                {"table_name": "RSAP_CREDIT_MEMO_LINE_RIN1_RIN2_DLNK"}
            ]
        },
        {
            "field_name": "json_text",
            "field_type": "VARCHAR",
            "targets": [
                {
                    "table_name": "RSAP_CREDIT_MEMO_LINE_RIN1_RIN2_J1_L10_SAT",
                    "exclude_json_paths_from_change_detection": ["UpdateDate"]
                }
            ]
        }
    ],
    "data_vault_model": [
        {
            "schema_name": "rvlt_sap",
            "tables": [
                {
                    "table_name": "RSAP_CREDIT_MEMO_LINE_RIN1_HUB",
                    "table_stereotype": "hub",
                    "hub_key_column_name": "HK_RSAP_CREDIT_MEMO_LINE_RIN1"
                },
                {
                    "table_name": "RSAP_CREDIT_MEMO_LINE_RIN1_RIN2_DLNK",
                    "table_stereotype": "lnk",
                    "link_key_column_name": "LK_RSAP_CREDIT_MEMO_LINE_RIN1_RIN2",
                    "link_parent_tables": [
                        "RSAP_CREDIT_MEMO_LINE_RIN1_HUB"
                    ]
                },
                {
                    "table_name": "RSAP_CREDIT_MEMO_LINE_RIN1_RIN2_J1_L10_SAT",
                    "table_stereotype": "sat",
                    "satellite_parent_table": "RSAP_CREDIT_MEMO_LINE_RIN1_RIN2_DLNK",
                    "diff_hash_column_name": "RH_RSAP_CREDIT_MEMO_LINE_RIN1_RIN2_J1_L10_SAT"
                }
            ]
        }
    ]
}
```

#### 生成的DDL结构
```sql
-- Hub表
CREATE TABLE rvlt_sap.rsap_credit_memo_line_rin1_hub (
    MD_INSERTED_AT                TIMESTAMP      NOT NULL,
    MD_RUN_ID                     INT            NOT NULL,
    MD_RECORD_SOURCE              VARCHAR(255)   NOT NULL,
    HK_RSAP_CREDIT_MEMO_LINE_RIN1 CHAR(28)       NOT NULL,
    COMPANY                       VARCHAR(50)    NULL,
    DOCENTRY                      INTEGER        NULL,
    LINENUM                       INTEGER        NULL,
    PRIMARY KEY (HK_RSAP_CREDIT_MEMO_LINE_RIN1)
);

-- Dependent Link表
CREATE TABLE rvlt_sap.rsap_credit_memo_line_rin1_rin2_dlnk (
    MD_INSERTED_AT                     TIMESTAMP      NOT NULL,
    MD_RUN_ID                          INT            NOT NULL,
    MD_RECORD_SOURCE                   VARCHAR(255)   NOT NULL,
    HK_RSAP_CREDIT_MEMO_LINE_RIN1      CHAR(28)       NOT NULL,
    LK_RSAP_CREDIT_MEMO_LINE_RIN1_RIN2 CHAR(28)       NOT NULL,
    GROUPNUM                           INTEGER        NULL,
    PRIMARY KEY (LK_RSAP_CREDIT_MEMO_LINE_RIN1_RIN2)
);

-- Satellite表
CREATE TABLE rvlt_sap.rsap_credit_memo_line_rin1_rin2_j1_l10_sat (
    MD_INSERTED_AT                                TIMESTAMP      NOT NULL,
    MD_RUN_ID                                     INT            NOT NULL,
    MD_RECORD_SOURCE                              VARCHAR(255)   NOT NULL,
    MD_IS_DELETED                                 BOOLEAN        NOT NULL,
    MD_VALID_BEFORE                               TIMESTAMP      NOT NULL,
    LK_RSAP_CREDIT_MEMO_LINE_RIN1_RIN2            CHAR(28)       NOT NULL,
    RH_RSAP_CREDIT_MEMO_LINE_RIN1_RIN2_J1_L10_SAT CHAR(28)       NOT NULL,
    JSON_TEXT                                     VARCHAR        NULL,
    PRIMARY KEY (LK_RSAP_CREDIT_MEMO_LINE_RIN1_RIN2, MD_INSERTED_AT)
);
```

### 案例2: BillBee订单数据

#### 业务背景
BillBee订单数据包含多层嵌套结构，需要分别处理订单头、订单项目和产品信息。

#### 订单配置
```json
{
    "dvpd_version": "0.6.2",
    "pipeline_name": "rbbe_order_p1",
    "record_source_name_expression": "billbee.order",
    "stage_properties": [
        {
            "stage_schema": "stage_rvlt",
            "stage_table_name": "rbbe_order_p1_stage"
        }
    ],
    "fields": [
        {
            "field_name": "billbeeorderid",
            "field_type": "NUMBER(36,0)",
            "targets": [
                {"table_name": "RBBE_ORDER_HUB"}
            ]
        },
        {
            "field_name": "billbeeparentorderid",
            "field_type": "NUMBER(36,0)",
            "targets": [
                {"table_name": "RBBE_ORDER_HUB"}
            ]
        },
        {
            "field_name": "customer_id",
            "field_type": "NUMBER(36,0)",
            "targets": [
                {"table_name": "RBBE_CUSTOMER_HUB"}
            ]
        },
        {
            "field_name": "seller_billbeeshopid",
            "field_type": "NUMBER(36,0)",
            "targets": [
                {"table_name": "RBBE_SHOP_HUB"}
            ]
        }
    ],
    "data_vault_model": [
        {
            "schema_name": "rvlt_billbee",
            "tables": [
                {
                    "table_name": "RBBE_ORDER_HUB",
                    "table_stereotype": "hub",
                    "hub_key_column_name": "HK_RBBE_ORDER"
                },
                {
                    "table_name": "RBBE_CUSTOMER_HUB",
                    "table_stereotype": "hub",
                    "hub_key_column_name": "HK_RBBE_CUSTOMER"
                },
                {
                    "table_name": "RBBE_SHOP_HUB",
                    "table_stereotype": "hub",
                    "hub_key_column_name": "HK_RBBE_SHOP"
                }
            ]
        }
    ]
}
```

### 案例3: PIM产品数据分组

#### 业务背景
PIM产品数据包含大量属性，需要按功能分组到不同的Satellite表中。

#### 分组配置策略
```json
{
    "dvpd_version": "0.6.2",
    "pipeline_name": "rpim_product_p1",
    "record_source_name_expression": "pim.product",
    "fields": [
        {
            "field_name": "id",
            "field_type": "NUMBER(36,0)",
            "targets": [
                {"table_name": "RPIM_PRODUCT_HUB"}
            ]
        },
        {
            "field_name": "json_text_j1",
            "field_type": "VARCHAR",
            "targets": [
                {
                    "table_name": "RPIM_PRODUCT_PRODUCT_J1_L10_SAT",
                    "exclude_json_paths_from_change_detection": [
                        "Id", "Images", "Sources",
                        "SoldAmount", "SoldSumGross", "SoldSumNet",
                        "SoldSumNetLast30Days", "SoldSumGrossLast30Days",
                        "SoldAmountLast30Days"
                    ]
                }
            ]
        },
        {
            "field_name": "json_text_j2",
            "field_type": "VARCHAR",
            "targets": [
                {
                    "table_name": "RPIM_PRODUCT_PRODUCT_J2_L10_SAT",
                    "include_only_json_paths": ["Images", "Sources"]
                }
            ]
        },
        {
            "field_name": "json_text_j3",
            "field_type": "VARCHAR",
            "targets": [
                {
                    "table_name": "RPIM_PRODUCT_PRODUCT_J3_L10_SAT",
                    "include_only_json_paths": [
                        "SoldAmount", "SoldSumGross", "SoldSumNet",
                        "SoldSumNetLast30Days", "SoldSumGrossLast30Days",
                        "SoldAmountLast30Days"
                    ]
                }
            ]
        }
    ]
}
```

---

## 开发最佳实践

### 命名规范最佳实践

#### Pipeline命名
```python
# 推荐的Pipeline命名模式
naming_patterns = {
    "sap_sources": "rsap_{table_name}_{version}",
    "billbee_sources": "rbbe_{entity}_{version}",
    "pim_sources": "rpim_{entity}_{version}",
    "manual_data": "rmud_{entity}_{version}",
    "business_vault": "b{source}_{entity}_{version}"
}

# 示例
examples = [
    "rsap_rin2_j1",           # SAP RIN2表，第1版JSON处理
    "rbbe_order_p1",          # BillBee订单，第1版处理
    "rpim_product_p1",        # PIM产品，第1版处理
    "rmud_planzahlen_p1",     # 手动计划数据，第1版处理
    "bsap_invoice_korrigiert_v1"  # SAP发票修正，业务规则版本1
]
```

#### 字段映射策略
```python
def design_field_mapping(source_data, business_requirements):
    """设计字段映射策略"""

    mapping_strategy = {
        # 1. 业务键字段 -> Hub表
        "business_keys": {
            "principle": "保持源系统原始字段名",
            "target": "hub_tables",
            "examples": ["company", "docentry", "linenum", "billbeeorderid"]
        },

        # 2. 关系上下文字段 -> Link表
        "relationship_context": {
            "principle": "表示实体间关系的字段",
            "target": "link_tables",
            "examples": ["groupnum", "parent_order_id", "line_sequence"]
        },

        # 3. 描述性属性 -> Satellite表
        "descriptive_attributes": {
            "principle": "按变更频率和业务逻辑分组",
            "target": "satellite_tables",
            "grouping_strategies": [
                "by_change_frequency",  # 按变更频率
                "by_business_domain",   # 按业务域
                "by_data_sensitivity"   # 按数据敏感性
            ]
        }
    }

    return mapping_strategy
```

### JSON数据处理策略

#### 分组原则
```python
def design_json_grouping(json_structure):
    """设计JSON数据分组策略"""

    grouping_rules = {
        "j1_basic_info": {
            "description": "基础业务信息",
            "include_patterns": ["name", "code", "type", "status"],
            "exclude_patterns": ["image", "media", "statistics", "audit"]
        },

        "j2_media_content": {
            "description": "媒体和内容信息",
            "include_patterns": ["image", "media", "document", "attachment"],
            "exclude_patterns": ["statistics", "audit"]
        },

        "j3_statistics": {
            "description": "统计和计算信息",
            "include_patterns": ["count", "sum", "average", "statistics"],
            "exclude_patterns": ["audit", "system"]
        },

        "j4_audit_info": {
            "description": "审计和系统信息",
            "include_patterns": ["created", "modified", "updated", "audit"],
            "change_detection": False  # 通常排除变更检测
        }
    }

    return grouping_rules

# 实际应用示例
billbee_product_grouping = {
    "j1_product_basic": {
        "exclude_paths": [
            "Id", "Images", "Sources",
            "SoldAmount", "SoldSumGross", "SoldSumNet",
            "SoldSumNetLast30Days", "SoldSumGrossLast30Days",
            "SoldAmountLast30Days"
        ]
    },
    "j2_media_sources": {
        "include_only_paths": ["Images", "Sources"]
    },
    "j3_sales_statistics": {
        "include_only_paths": [
            "SoldAmount", "SoldSumGross", "SoldSumNet",
            "SoldSumNetLast30Days", "SoldSumGrossLast30Days",
            "SoldAmountLast30Days"
        ]
    }
}
```

#### 变更检测配置
```python
def configure_change_detection(entity_type, business_rules):
    """配置变更检测规则"""

    change_detection_rules = {
        "always_exclude": [
            "UpdateDate", "LastModified", "ProcessingTimestamp",
            "LastSeen", "SystemGeneratedId", "RowVersion"
        ],

        "conditional_exclude": {
            "audit_fields": ["CreatedBy", "ModifiedBy", "ProcessedAt"],
            "system_fields": ["InternalId", "SequenceNumber"],
            "calculated_fields": ["Hash", "Checksum", "ComputedValue"]
        },

        "business_specific": {
            "sap_invoice": ["UpdateDate", "LastProcessed"],
            "billbee_order": ["LastModified", "ProcessingStatus"],
            "pim_product": ["LastSync", "ImportTimestamp"]
        }
    }

    return change_detection_rules
```

### 性能优化配置

#### 分区策略
```python
def design_partitioning_strategy(table_type, data_volume, access_patterns):
    """设计表分区策略"""

    partitioning_strategies = {
        "hub_tables": {
            "small_volume": "no_partitioning",
            "medium_volume": "hash_partitioning_on_hk",
            "large_volume": "range_partitioning_on_load_date"
        },

        "satellite_tables": {
            "default": "range_partitioning_on_md_inserted_at",
            "high_frequency": "composite_partitioning",
            "archive_pattern": "monthly_partitions_with_compression"
        },

        "link_tables": {
            "default": "hash_partitioning_on_lk",
            "cross_schema": "range_partitioning_on_load_date"
        }
    }

    return partitioning_strategies

# 分区配置示例
partition_config = {
    "rsap_credit_memo_line_rin1_rin2_j1_l10_sat": {
        "partition_type": "RANGE",
        "partition_column": "MD_INSERTED_AT",
        "partition_interval": "MONTHLY",
        "retention_months": 84,  # 7年保留
        "compression": "ENABLED"
    }
}
```

#### 索引策略
```python
def design_index_strategy(table_structure, query_patterns):
    """设计索引策略"""

    index_strategies = {
        "hub_tables": [
            {
                "type": "PRIMARY_KEY",
                "columns": ["hash_key"],
                "description": "主键索引"
            },
            {
                "type": "UNIQUE",
                "columns": ["business_key_columns"],
                "description": "业务键唯一索引"
            },
            {
                "type": "BTREE",
                "columns": ["md_inserted_at"],
                "description": "加载时间索引"
            }
        ],

        "satellite_tables": [
            {
                "type": "PRIMARY_KEY",
                "columns": ["parent_key", "md_inserted_at"],
                "description": "复合主键"
            },
            {
                "type": "BTREE",
                "columns": ["parent_key", "md_valid_before"],
                "description": "当前记录查询索引"
            },
            {
                "type": "BTREE",
                "columns": ["record_hash"],
                "description": "变更检测索引"
            }
        ]
    }

    return index_strategies
```

### 数据质量配置

#### 质量检查规则
```python
def configure_quality_checks(pipeline_config):
    """配置数据质量检查规则"""

    quality_checks = {
        "hash_collision_detection": {
            "enabled": True,
            "tables": ["all_hubs", "all_links"],
            "action": "fail_pipeline"
        },

        "business_key_uniqueness": {
            "enabled": True,
            "tables": ["all_hubs"],
            "action": "log_warning"
        },

        "referential_integrity": {
            "enabled": True,
            "check_orphaned_satellites": True,
            "check_orphaned_links": True,
            "action": "log_error"
        },

        "data_completeness": {
            "enabled": True,
            "required_fields": ["business_keys", "record_source"],
            "action": "fail_record"
        },

        "temporal_consistency": {
            "enabled": True,
            "check_future_dates": True,
            "check_sequence_gaps": True,
            "action": "log_warning"
        }
    }

    return quality_checks
```

#### 错误处理策略
```python
def configure_error_handling(pipeline_type, criticality):
    """配置错误处理策略"""

    error_handling = {
        "critical_pipelines": {
            "hash_collision": "fail_immediately",
            "data_corruption": "fail_immediately",
            "schema_mismatch": "fail_immediately",
            "connection_error": "retry_with_backoff"
        },

        "standard_pipelines": {
            "hash_collision": "log_and_continue",
            "data_quality_issue": "quarantine_record",
            "transformation_error": "skip_record",
            "connection_error": "retry_limited"
        },

        "experimental_pipelines": {
            "any_error": "log_and_continue",
            "max_error_rate": "50%",
            "notification": "daily_summary"
        }
    }

    return error_handling
```

---

## 常见问题解决

### 配置文件常见错误

#### 1. 命名规范错误
```python
# 错误示例
bad_examples = {
    "pipeline_name": "SAP_RIN2_Pipeline",  # 大写字母
    "table_name": "sap_rin2_hub",          # 小写字母
    "field_name": "Company-Code"           # 连字符
}

# 正确示例
good_examples = {
    "pipeline_name": "rsap_rin2_j1",       # 小写+下划线
    "table_name": "RSAP_RIN2_HUB",         # 大写+下划线
    "field_name": "company_code"           # 小写+下划线
}
```

#### 2. 字段类型不匹配
```python
# 常见类型映射错误
type_mapping_issues = {
    "integer_as_varchar": {
        "wrong": "VARCHAR(50)",
        "correct": "INTEGER",
        "fields": ["docentry", "linenum", "quantity"]
    },

    "timestamp_as_varchar": {
        "wrong": "VARCHAR(100)",
        "correct": "TIMESTAMP",
        "fields": ["created_at", "updated_at", "processed_at"]
    },

    "decimal_precision": {
        "wrong": "DECIMAL(10,2)",
        "correct": "DECIMAL(16,4)",
        "fields": ["amount", "rate", "price"]
    }
}
```

#### 3. 关系配置错误
```python
# Link表配置常见错误
link_configuration_errors = {
    "missing_parent_tables": {
        "error": "link_parent_tables字段为空",
        "solution": "必须指定至少一个父Hub表"
    },

    "circular_dependency": {
        "error": "Hub表之间存在循环依赖",
        "solution": "重新设计业务键和关系结构"
    },

    "wrong_stereotype": {
        "error": "使用lnk而应该使用dlnk",
        "solution": "父子关系应使用dependent link"
    }
}
```

### 性能问题诊断

#### 1. 慢查询分析
```sql
-- 诊断Satellite表查询性能
WITH satellite_performance AS (
    SELECT
        table_name,
        COUNT(*) as total_records,
        COUNT(DISTINCT parent_key) as unique_parents,
        AVG(DATEDIFF(day, md_inserted_at, md_valid_before)) as avg_validity_days,
        COUNT(*) / COUNT(DISTINCT parent_key) as avg_versions_per_parent
    FROM satellite_tables
    GROUP BY table_name
)
SELECT * FROM satellite_performance
WHERE avg_versions_per_parent > 100  -- 版本过多的表
   OR total_records > 10000000;      -- 记录过多的表
```

#### 2. 索引优化建议
```python
def analyze_index_performance(table_name, query_patterns):
    """分析索引性能并提供优化建议"""

    optimization_suggestions = {
        "missing_indexes": [
            {
                "table": "satellite_tables",
                "columns": ["parent_key", "md_valid_before"],
                "reason": "当前记录查询优化"
            },
            {
                "table": "hub_tables",
                "columns": ["md_inserted_at"],
                "reason": "时间范围查询优化"
            }
        ],

        "redundant_indexes": [
            {
                "table": "all_tables",
                "pattern": "单列索引被复合索引覆盖",
                "action": "删除冗余单列索引"
            }
        ],

        "partition_pruning": [
            {
                "table": "large_satellites",
                "strategy": "按md_inserted_at分区",
                "benefit": "查询性能提升50-80%"
            }
        ]
    }

    return optimization_suggestions
```

### 部署问题解决

#### 1. DDL部署失败
```python
def troubleshoot_ddl_deployment(error_message, deployment_context):
    """DDL部署问题诊断"""

    common_issues = {
        "table_already_exists": {
            "cause": "表已存在且结构不匹配",
            "solution": "使用ALTER TABLE或DROP/CREATE策略"
        },

        "permission_denied": {
            "cause": "数据库用户权限不足",
            "solution": "检查CREATE TABLE和CREATE SCHEMA权限"
        },

        "invalid_column_type": {
            "cause": "数据库不支持指定的列类型",
            "solution": "检查数据库兼容性并调整类型映射"
        },

        "constraint_violation": {
            "cause": "外键约束或唯一约束冲突",
            "solution": "检查数据一致性和约束定义"
        }
    }

    return common_issues
```

#### 2. 数据加载失败
```python
def troubleshoot_data_loading(pipeline_name, error_details):
    """数据加载问题诊断"""

    loading_issues = {
        "hash_collision": {
            "detection": "HASH COLLISION DETECTED错误",
            "cause": "不同业务键生成相同哈希值",
            "solution": [
                "检查业务键定义完整性",
                "验证源数据质量",
                "考虑添加额外区分字段"
            ]
        },

        "data_type_mismatch": {
            "detection": "类型转换错误",
            "cause": "源数据类型与目标表不匹配",
            "solution": [
                "检查DVPD配置中的字段类型",
                "添加数据清洗逻辑",
                "更新类型映射规则"
            ]
        },

        "memory_overflow": {
            "detection": "内存不足错误",
            "cause": "批处理数据量过大",
            "solution": [
                "减少批处理大小",
                "增加容器内存配置",
                "实现流式处理"
            ]
        }
    }

    return loading_issues
```

---

## 性能优化

### 批处理优化

#### 批量大小调优
```python
def optimize_batch_size(table_type, record_size, available_memory):
    """优化批处理大小"""

    batch_size_recommendations = {
        "hub_tables": {
            "small_records": 10000,    # < 1KB per record
            "medium_records": 5000,    # 1-10KB per record
            "large_records": 1000      # > 10KB per record
        },

        "satellite_tables": {
            "json_light": 5000,        # 简单JSON
            "json_heavy": 1000,        # 复杂JSON
            "binary_data": 100         # 包含二进制数据
        },

        "link_tables": {
            "standard": 20000,         # 标准Link
            "cross_schema": 10000      # 跨Schema Link
        }
    }

    # 动态调整算法
    optimal_batch_size = min(
        batch_size_recommendations[table_type]["medium_records"],
        available_memory // (record_size * 2),  # 保留50%内存缓冲
        50000  # 最大批次限制
    )

    return optimal_batch_size
```

#### 并行处理策略
```python
def design_parallel_processing(pipeline_dependencies, resource_constraints):
    """设计并行处理策略"""

    parallel_strategies = {
        "independent_pipelines": {
            "strategy": "完全并行",
            "max_concurrent": resource_constraints["max_containers"],
            "examples": ["rsap_rin2_j1", "rbbe_order_p1", "rpim_product_p1"]
        },

        "dependent_pipelines": {
            "strategy": "分层并行",
            "layers": [
                ["raw_vault_layer"],      # 第1层：Raw Vault
                ["business_vault_layer"], # 第2层：Business Vault
                ["data_mart_layer"]       # 第3层：Data Mart
            ]
        },

        "resource_intensive": {
            "strategy": "串行执行",
            "reason": "内存或CPU密集型",
            "examples": ["large_json_processing", "complex_transformations"]
        }
    }

    return parallel_strategies
```

### 查询优化

#### 视图设计优化
```sql
-- 优化的当前记录视图
CREATE VIEW rsap_credit_memo_line_rin1_rin2_current_sat AS
SELECT
    s.lk_rsap_credit_memo_line_rin1_rin2,
    s.json_text,
    s.md_inserted_at,
    s.md_record_source
FROM rsap_credit_memo_line_rin1_rin2_j1_l10_sat s
INNER JOIN (
    -- 子查询优化：只查找最新记录的时间戳
    SELECT
        lk_rsap_credit_memo_line_rin1_rin2,
        MAX(md_inserted_at) as latest_inserted_at
    FROM rsap_credit_memo_line_rin1_rin2_j1_l10_sat
    WHERE md_valid_before = '2299-12-30 00:00:00'
      AND md_is_deleted = FALSE
    GROUP BY lk_rsap_credit_memo_line_rin1_rin2
) latest ON s.lk_rsap_credit_memo_line_rin1_rin2 = latest.lk_rsap_credit_memo_line_rin1_rin2
         AND s.md_inserted_at = latest.latest_inserted_at;
```

#### 物化视图策略
```python
def design_materialized_views(access_patterns, refresh_requirements):
    """设计物化视图策略"""

    materialized_view_candidates = {
        "high_frequency_queries": {
            "view_name": "current_business_partners_mv",
            "refresh_strategy": "incremental_hourly",
            "benefit": "90%查询性能提升"
        },

        "complex_joins": {
            "view_name": "order_item_product_details_mv",
            "refresh_strategy": "full_daily",
            "benefit": "减少跨Schema连接开销"
        },

        "aggregated_reports": {
            "view_name": "daily_sales_summary_mv",
            "refresh_strategy": "incremental_daily",
            "benefit": "报表查询秒级响应"
        }
    }

    return materialized_view_candidates
```

---

*文档版本: 1.0*
*最后更新: 2024年*
*适用DVPD版本: 0.6.2+*
