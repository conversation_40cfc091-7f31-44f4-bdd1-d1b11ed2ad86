Data vault pipeline developer cheat sheet 
rendered from  rsap_inv2_j1.dvpi

pipeline name:  rsap_inv2_j1

------------------------------------------------------
record source:  sap.inv2

Source fields:
       COMPANY    Varchar(50)
       DOCENTRY   INTEGER
       LINENUM    INTEGER
       GROUPNUM   INTEGER
       JSON_TEXT  VARCHAR


------------------------------------------------------
Table List:
stage_table.rvlt_sap.rsap_inv2_j1_stage
table.rvlt_sap.rsap_invoice_line_inv1_hub
table.rvlt_sap.rsap_invoice_line_inv1_inv2_dlnk
table.rvlt_sap.rsap_invoice_line_inv1_inv2_j1_l10_sat

------------------------------------------------------
stage table:  stage_rvlt.rsap_inv2_j1_stage
Field to Stage mapping:
	--business keys,
		COMPANY    >  COMPANY,
		DOCENTRY   >  DOCENTRY,
		GROUPNUM   >  GROUPNUM,
		LINENUM    >  LINENUM,

	--content,
		JSON_TEXT  >  JSON_TEXT

------------------------------------------------------
Hash value composition

HK_RSAP_INVOICE_LINE_INV1 (key)
		COMPANY 
		DOCENTRY 
		LINENUM 

LK_RSAP_INVOICE_LINE_INV1_INV2 (key)
		GROUPNUM 
		COMPANY 
		DOCENTRY 
		LINENUM 

RH_RSAP_INVOICE_LINE_INV1_INV2_J1_L10_SAT (diff_hash)
		JSON_TEXT 


------------------------------------------------------
Table load method
(STAGE >  VAULT)

rsap_invoice_line_inv1_hub (/) can be loaded by convention
		  key: HK_RSAP_INVOICE_LINE_INV1  >  HK_RSAP_INVOICE_LINE_INV1
		  business_key: COMPANY  >  COMPANY 
		  business_key: DOCENTRY  >  DOCENTRY 
		  business_key: LINENUM  >  LINENUM 

rsap_invoice_line_inv1_inv2_dlnk (/) can be loaded by convention
		  parent_key_1: HK_RSAP_INVOICE_LINE_INV1  >  HK_RSAP_INVOICE_LINE_INV1
		  key: LK_RSAP_INVOICE_LINE_INV1_INV2  >  LK_RSAP_INVOICE_LINE_INV1_INV2
		  dependent_child_key: GROUPNUM  >  GROUPNUM 

rsap_invoice_line_inv1_inv2_j1_l10_sat (/) can be loaded by convention
		  parent_key: LK_RSAP_INVOICE_LINE_INV1_INV2  >  LK_RSAP_INVOICE_LINE_INV1_INV2
		  diff_hash: RH_RSAP_INVOICE_LINE_INV1_INV2_J1_L10_SAT  >  RH_RSAP_INVOICE_LINE_INV1_INV2_J1_L10_SAT
		  content: JSON_TEXT  >  JSON_TEXT 

