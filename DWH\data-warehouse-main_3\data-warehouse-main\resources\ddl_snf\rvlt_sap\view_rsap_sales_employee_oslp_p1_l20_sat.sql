CREATE OR REPLACE VIEW rvlt_sap.rsap_sales_employee_oslp_p1_l20_sat (
    MD_INSERTED_AT,
	MD_RUN_ID,
	MD_RECORD_SOURCE,
    MD_IS_DELETED,
    MD_VALID_BEFORE,
    HK_<PERSON><PERSON>_sales_employee_OS<PERSON>,
    RH_RSAP_sales_employee_OSLP_J1_L10_SAT,
    SLPNAME,
    MEMO,
    U_DIM1
)
AS
SELECT
    MD_INSERTED_AT,
	MD_RUN_ID,
	MD_RECORD_SOURCE,
    MD_IS_DELETED,
    MD_VALID_BEFORE,
    HK_RSAP_sales_employee_OSLP,
    RH_RSAP_sales_employee_OSLP_J1_L10_SAT,
    JSON_EXTRACT_PATH_TEXT(JSON_TEXT, 'SlpName') as SLPNAME,
    JSON_EXTRACT_PATH_TEXT(JSON_TEXT, 'Memo') as ME<PERSON>,
    JSON_EXTRACT_PATH_TEXT(JSON_TEXT, 'U_Dim1') as U_DIM1,
FROM rvlt_sap.rsap_sales_employee_oslp_j1_l10_sat
;