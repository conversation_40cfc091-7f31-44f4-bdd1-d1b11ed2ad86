-- generated script for rvlt_billbee.rbbe_order_order_p2_l10_sat

-- DROP TABLE rvlt_billbee.rbbe_order_order_p2_l10_sat;

CREATE TABLE rvlt_billbee.rbbe_order_order_p2_l10_sat (
MD_INSERTED_AT TIMESTAMP NOT NULL,
MD_RUN_ID INT NOT NULL,
MD_RECORD_SOURCE VARCHAR(255) NOT NULL,
MD_IS_DELETED BOOLEAN NOT NULL,
MD_VALID_BEFORE TIMESTAMP NOT NULL,
HK_RBBE_ORDER CHAR(28) NOT NULL,
RH_RBBE_ORDER_ORDER_P2_L10_SAT CHAR(28) NOT NULL,
STAT<PERSON> NUMBER(36,0) NULL,
CREATEDAT TIMESTAMP NULL,
SHIPPEDAT TIMESTAMP NULL,
CONFIRMEDAT TIMESTAMP NULL,
INVOICEDATE TIMESTAMP NULL,
PAYEDAT TIMESTAMP NULL,
UPDATEDAT TIMESTAMP NULL,
LASTMODIFIEDAT TIMESTAMP NULL
);

--COMMENT STATEMENTS

-- end of script --