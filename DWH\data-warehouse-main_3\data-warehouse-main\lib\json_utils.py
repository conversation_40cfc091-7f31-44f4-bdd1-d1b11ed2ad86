import json

def get_json_text_content_column(json_data, keys_to_remove):
    json_content = json_data.copy()
    for key in keys_to_remove:
        json_content.pop(key, None)
    return json_content


def sort_json_content_column(json_data):
        return sort_json(json_data)


def sort_json(data):
    """
    Recursively sort dictionaries in a JSON-like structure (dict, list).
    Each dictionary in lists will be sorted, but other types in lists will not be affected.
    """
    if isinstance(data, dict):
        # Sort dictionary by keys and recursively sort values
        return {key: sort_json(value) for key, value in sorted(data.items())}
    elif isinstance(data, list):
        # Recursively sort each element in the list
        return [sort_json(item) if isinstance(item, dict) else item for item in data]
    else:
        # Return primitive types as-is (base case)
        return data
