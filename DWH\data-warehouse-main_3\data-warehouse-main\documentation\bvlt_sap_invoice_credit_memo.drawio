<mxfile host="Electron" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/25.0.2 Chrome/128.0.6613.186 Electron/32.2.5 Safari/537.36" version="25.0.2">
  <diagram name="Seite-1" id="Q71B-oQZgWCyV83AQp44">
    <mxGraphModel dx="1953" dy="505" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1169" pageHeight="827" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="ZSUZZlGQCimUVaxMRYQN-21" value="Schema" parent="0" />
        <mxCell id="ZSUZZlGQCimUVaxMRYQN-22" value="&lt;span style=&quot;font-size: 11px; text-align: center; background-color: rgb(255, 255, 255);&quot;&gt;rvlt_sap (rsap)&lt;/span&gt;" style="rounded=0;whiteSpace=wrap;html=1;align=left;verticalAlign=top;fillColor=none;" parent="ZSUZZlGQCimUVaxMRYQN-21" vertex="1">
          <mxGeometry x="-160" y="110" width="970" height="310" as="geometry" />
        </mxCell>
        <mxCell id="ZSUZZlGQCimUVaxMRYQN-26" value="&lt;span style=&quot;font-size: 11px; text-align: center;&quot;&gt;bvlt_sap (bsap)&lt;/span&gt;" style="rounded=0;whiteSpace=wrap;html=1;align=left;verticalAlign=top;fillColor=#E6E6E6;opacity=50;labelBackgroundColor=none;" parent="ZSUZZlGQCimUVaxMRYQN-21" vertex="1">
          <mxGeometry x="-160" y="10" width="970" height="100" as="geometry" />
        </mxCell>
        <mxCell id="ZSUZZlGQCimUVaxMRYQN-27" value="&lt;span style=&quot;font-size: 11px; text-align: center;&quot;&gt;bvlt_sap (bsap)&lt;/span&gt;" style="rounded=0;whiteSpace=wrap;html=1;align=left;verticalAlign=top;fillColor=#E6E6E6;opacity=50;labelBackgroundColor=none;" parent="ZSUZZlGQCimUVaxMRYQN-21" vertex="1">
          <mxGeometry x="-160" y="420" width="970" height="100" as="geometry" />
        </mxCell>
        <mxCell id="ZSUZZlGQCimUVaxMRYQN-33" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;endArrow=classicThin;endFill=1;jumpStyle=none;fontColor=#C2C2C2;strokeWidth=2;strokeColor=#969696;curved=1;labelBackgroundColor=default;fontFamily=Helvetica;fontSize=11;shape=connector;dashed=1;dashPattern=1 1;align=center;verticalAlign=middle;entryX=1;entryY=0.25;entryDx=0;entryDy=0;" parent="ZSUZZlGQCimUVaxMRYQN-21" source="ZSUZZlGQCimUVaxMRYQN-7" target="ZSUZZlGQCimUVaxMRYQN-34" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="450" y="500" as="sourcePoint" />
            <mxPoint x="540" y="580" as="targetPoint" />
            <Array as="points">
              <mxPoint x="655" y="414" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="ZSUZZlGQCimUVaxMRYQN-34" value="" style="shape=parallelogram;perimeter=parallelogramPerimeter;whiteSpace=wrap;html=1;fixedSize=1;fontColor=#C2C2C2;strokeColor=#000000;strokeWidth=1;fillColor=#E0E0E0;fontFamily=Helvetica;fontSize=11;labelBackgroundColor=default;" parent="ZSUZZlGQCimUVaxMRYQN-21" vertex="1">
          <mxGeometry x="370" y="400" width="80" height="50" as="geometry" />
        </mxCell>
        <mxCell id="ZSUZZlGQCimUVaxMRYQN-35" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;endArrow=classicThin;endFill=1;jumpStyle=none;fontColor=#C2C2C2;strokeWidth=2;strokeColor=#969696;curved=1;labelBackgroundColor=default;fontFamily=Helvetica;fontSize=11;shape=connector;dashed=1;dashPattern=1 1;align=center;verticalAlign=middle;entryX=0;entryY=0;entryDx=0;entryDy=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="ZSUZZlGQCimUVaxMRYQN-21" source="ZSUZZlGQCimUVaxMRYQN-10" target="ZSUZZlGQCimUVaxMRYQN-34" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="665" y="410" as="sourcePoint" />
            <mxPoint x="454" y="424" as="targetPoint" />
            <Array as="points">
              <mxPoint x="255" y="410" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="ZSUZZlGQCimUVaxMRYQN-36" style="edgeStyle=orthogonalEdgeStyle;shape=connector;curved=1;rounded=1;jumpStyle=none;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.998;entryY=0.638;entryDx=0;entryDy=0;entryPerimeter=0;dashed=1;dashPattern=1 1;strokeColor=#969696;strokeWidth=2;align=center;verticalAlign=middle;fontFamily=Helvetica;fontSize=11;fontColor=#C2C2C2;labelBackgroundColor=default;endArrow=classicThin;endFill=1;" parent="ZSUZZlGQCimUVaxMRYQN-21" source="ZSUZZlGQCimUVaxMRYQN-34" target="ZSUZZlGQCimUVaxMRYQN-31" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="ZSUZZlGQCimUVaxMRYQN-40" style="edgeStyle=orthogonalEdgeStyle;shape=connector;curved=1;rounded=1;jumpStyle=none;orthogonalLoop=1;jettySize=auto;html=1;dashed=1;dashPattern=1 1;strokeColor=#969696;strokeWidth=2;align=center;verticalAlign=middle;fontFamily=Helvetica;fontSize=11;fontColor=#C2C2C2;labelBackgroundColor=default;endArrow=classicThin;endFill=1;" parent="ZSUZZlGQCimUVaxMRYQN-21" source="ZSUZZlGQCimUVaxMRYQN-37" target="ZSUZZlGQCimUVaxMRYQN-28" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="430" y="55" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="ZSUZZlGQCimUVaxMRYQN-37" value="" style="shape=parallelogram;perimeter=parallelogramPerimeter;whiteSpace=wrap;html=1;fixedSize=1;fontColor=#C2C2C2;strokeColor=#000000;strokeWidth=1;fillColor=#E0E0E0;fontFamily=Helvetica;fontSize=11;labelBackgroundColor=default;" parent="ZSUZZlGQCimUVaxMRYQN-21" vertex="1">
          <mxGeometry x="390" y="90" width="80" height="50" as="geometry" />
        </mxCell>
        <mxCell id="ZSUZZlGQCimUVaxMRYQN-38" style="edgeStyle=orthogonalEdgeStyle;shape=connector;curved=1;rounded=1;jumpStyle=none;orthogonalLoop=1;jettySize=auto;html=1;dashed=1;dashPattern=1 1;strokeColor=#969696;strokeWidth=2;align=center;verticalAlign=middle;fontFamily=Helvetica;fontSize=11;fontColor=#C2C2C2;labelBackgroundColor=default;endArrow=classicThin;endFill=1;" parent="ZSUZZlGQCimUVaxMRYQN-21" source="ZSUZZlGQCimUVaxMRYQN-5" target="ZSUZZlGQCimUVaxMRYQN-37" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="660" y="120" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="ZSUZZlGQCimUVaxMRYQN-39" style="edgeStyle=orthogonalEdgeStyle;shape=connector;curved=1;rounded=1;jumpStyle=none;orthogonalLoop=1;jettySize=auto;html=1;dashed=1;dashPattern=1 1;strokeColor=#969696;strokeWidth=2;align=center;verticalAlign=middle;fontFamily=Helvetica;fontSize=11;fontColor=#C2C2C2;labelBackgroundColor=default;endArrow=classicThin;endFill=1;" parent="ZSUZZlGQCimUVaxMRYQN-21" source="ZSUZZlGQCimUVaxMRYQN-13" target="ZSUZZlGQCimUVaxMRYQN-37" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="240" y="120" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="FFsPzQJzrmcH5DIHsSi0-4" style="edgeStyle=orthogonalEdgeStyle;shape=connector;rounded=1;jumpStyle=none;orthogonalLoop=1;jettySize=auto;html=1;opacity=40;strokeColor=default;strokeWidth=2;align=center;verticalAlign=middle;fontFamily=Helvetica;fontSize=11;fontColor=default;labelBackgroundColor=default;endArrow=none;endFill=0;" parent="ZSUZZlGQCimUVaxMRYQN-21" source="FFsPzQJzrmcH5DIHsSi0-1" target="ZSUZZlGQCimUVaxMRYQN-12" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="FFsPzQJzrmcH5DIHsSi0-1" value="invoice_line_inv1_&lt;div&gt;inv2&lt;/div&gt;&lt;div&gt;(GroupNum)&lt;/div&gt;" style="verticalLabelPosition=middle;verticalAlign=middle;html=1;shape=hexagon;perimeter=hexagonPerimeter2;arcSize=6;size=0.16666666666666666;fillColor=#cce5ff;strokeColor=#36393d;strokeWidth=2;labelPosition=center;align=center;fontFamily=Helvetica;fontSize=11;fontColor=default;labelBackgroundColor=none;" parent="ZSUZZlGQCimUVaxMRYQN-21" vertex="1">
          <mxGeometry x="-120" y="200" width="140" height="40" as="geometry" />
        </mxCell>
        <mxCell id="FFsPzQJzrmcH5DIHsSi0-3" style="edgeStyle=orthogonalEdgeStyle;shape=connector;rounded=1;jumpStyle=none;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=default;align=center;verticalAlign=middle;fontFamily=Helvetica;fontSize=11;fontColor=default;labelBackgroundColor=default;endArrow=none;endFill=0;" parent="ZSUZZlGQCimUVaxMRYQN-21" source="FFsPzQJzrmcH5DIHsSi0-2" target="FFsPzQJzrmcH5DIHsSi0-1" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="FFsPzQJzrmcH5DIHsSi0-5" style="edgeStyle=orthogonalEdgeStyle;shape=connector;curved=1;rounded=1;jumpStyle=none;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0;entryDx=0;entryDy=0;dashed=1;dashPattern=1 1;strokeColor=#969696;strokeWidth=2;align=center;verticalAlign=middle;fontFamily=Helvetica;fontSize=11;fontColor=#C2C2C2;labelBackgroundColor=default;endArrow=classicThin;endFill=1;" parent="ZSUZZlGQCimUVaxMRYQN-21" source="FFsPzQJzrmcH5DIHsSi0-2" target="ZSUZZlGQCimUVaxMRYQN-37" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="20" y="100" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="FFsPzQJzrmcH5DIHsSi0-2" value="invoice_line_inv1_inv2_p1_l20" style="rounded=1;whiteSpace=wrap;html=1;strokeColor=#36393d;fillColor=#FFF3A8;strokeWidth=2;align=center;verticalAlign=middle;fontFamily=Helvetica;fontSize=12;fontColor=default;labelBackgroundColor=none;dashed=1;" parent="ZSUZZlGQCimUVaxMRYQN-21" vertex="1">
          <mxGeometry x="-145" y="150" width="190" height="30" as="geometry" />
        </mxCell>
        <mxCell id="J5lc5Pj09lLEP7WlP1O8-4" style="edgeStyle=orthogonalEdgeStyle;shape=connector;rounded=1;jumpStyle=none;orthogonalLoop=1;jettySize=auto;html=1;opacity=40;strokeColor=default;strokeWidth=2;align=center;verticalAlign=middle;fontFamily=Helvetica;fontSize=11;fontColor=default;labelBackgroundColor=default;endArrow=none;endFill=0;" edge="1" parent="ZSUZZlGQCimUVaxMRYQN-21" source="J5lc5Pj09lLEP7WlP1O8-1" target="ZSUZZlGQCimUVaxMRYQN-9">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="J5lc5Pj09lLEP7WlP1O8-1" value="credit_memo_line_&lt;div&gt;rin1_rin2&lt;/div&gt;&lt;div&gt;(GroupNum)&lt;/div&gt;" style="verticalLabelPosition=middle;verticalAlign=middle;html=1;shape=hexagon;perimeter=hexagonPerimeter2;arcSize=6;size=0.16666666666666666;fillColor=#cce5ff;strokeColor=#36393d;strokeWidth=2;labelPosition=center;align=center;fontFamily=Helvetica;fontSize=11;fontColor=default;labelBackgroundColor=none;" vertex="1" parent="ZSUZZlGQCimUVaxMRYQN-21">
          <mxGeometry x="-120" y="300" width="140" height="40" as="geometry" />
        </mxCell>
        <mxCell id="J5lc5Pj09lLEP7WlP1O8-2" style="edgeStyle=orthogonalEdgeStyle;shape=connector;rounded=1;jumpStyle=none;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=default;align=center;verticalAlign=middle;fontFamily=Helvetica;fontSize=11;fontColor=default;labelBackgroundColor=default;endArrow=none;endFill=0;" edge="1" parent="ZSUZZlGQCimUVaxMRYQN-21" source="J5lc5Pj09lLEP7WlP1O8-3" target="J5lc5Pj09lLEP7WlP1O8-1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="J5lc5Pj09lLEP7WlP1O8-5" style="edgeStyle=orthogonalEdgeStyle;shape=connector;curved=1;rounded=1;jumpStyle=none;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.75;entryDx=0;entryDy=0;dashed=1;dashPattern=1 1;strokeColor=#969696;strokeWidth=2;align=center;verticalAlign=middle;fontFamily=Helvetica;fontSize=11;fontColor=#C2C2C2;labelBackgroundColor=default;endArrow=classicThin;endFill=1;" edge="1" parent="ZSUZZlGQCimUVaxMRYQN-21" source="J5lc5Pj09lLEP7WlP1O8-3" target="ZSUZZlGQCimUVaxMRYQN-34">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="-50" y="436" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="J5lc5Pj09lLEP7WlP1O8-3" value="&lt;span style=&quot;font-size: 11px; text-wrap: nowrap;&quot;&gt;credit_memo_line_&lt;br&gt;&lt;/span&gt;&lt;span style=&quot;font-size: 11px; text-wrap: nowrap; background-color: initial;&quot;&gt;rin1_rin2&lt;/span&gt;_p1_l20" style="rounded=1;whiteSpace=wrap;html=1;strokeColor=#36393d;fillColor=#FFF3A8;strokeWidth=2;align=center;verticalAlign=middle;fontFamily=Helvetica;fontSize=12;fontColor=default;labelBackgroundColor=none;dashed=1;" vertex="1" parent="ZSUZZlGQCimUVaxMRYQN-21">
          <mxGeometry x="-145" y="360" width="190" height="30" as="geometry" />
        </mxCell>
        <mxCell id="1" value="Modell" parent="0" />
        <mxCell id="ZSUZZlGQCimUVaxMRYQN-2" value="invoice_oinv&lt;br&gt;(company,&lt;div&gt;DocEntry)&lt;/div&gt;" style="strokeWidth=2;html=1;shape=mxgraph.flowchart.start_1;whiteSpace=wrap;strokeColor=none;fontColor=#ffffff;fillColor=#002C82;fontFamily=Helvetica;fontSize=11;" parent="1" vertex="1">
          <mxGeometry x="480" y="190" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="ZSUZZlGQCimUVaxMRYQN-3" value="credit_memo_orin&lt;br&gt;(company,&lt;div&gt;DocEntry)&lt;/div&gt;" style="strokeWidth=2;html=1;shape=mxgraph.flowchart.start_1;whiteSpace=wrap;strokeColor=none;fontColor=#ffffff;fillColor=#002C82;fontFamily=Helvetica;fontSize=11;" parent="1" vertex="1">
          <mxGeometry x="480" y="290" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="ZSUZZlGQCimUVaxMRYQN-4" style="edgeStyle=orthogonalEdgeStyle;shape=connector;rounded=1;jumpStyle=none;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=default;align=center;verticalAlign=middle;fontFamily=Helvetica;fontSize=11;fontColor=default;labelBackgroundColor=default;endArrow=none;endFill=0;" parent="1" source="ZSUZZlGQCimUVaxMRYQN-5" target="ZSUZZlGQCimUVaxMRYQN-2" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="420" y="160" as="sourcePoint" />
            <Array as="points">
              <mxPoint x="540" y="145" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="ZSUZZlGQCimUVaxMRYQN-5" value="invoice_oninv_p1_l20" style="rounded=1;whiteSpace=wrap;html=1;strokeColor=#36393d;fillColor=#FFF3A8;strokeWidth=2;dashed=1;" parent="1" vertex="1">
          <mxGeometry x="580" y="130" width="160" height="30" as="geometry" />
        </mxCell>
        <mxCell id="ZSUZZlGQCimUVaxMRYQN-6" style="edgeStyle=orthogonalEdgeStyle;shape=connector;rounded=1;jumpStyle=none;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=default;align=center;verticalAlign=middle;fontFamily=Helvetica;fontSize=11;fontColor=default;labelBackgroundColor=default;endArrow=none;endFill=0;" parent="1" source="ZSUZZlGQCimUVaxMRYQN-7" target="ZSUZZlGQCimUVaxMRYQN-3" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="540" y="385" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="ZSUZZlGQCimUVaxMRYQN-7" value="credit_memo_orin_p1_l20" style="rounded=1;whiteSpace=wrap;html=1;strokeColor=#36393d;fillColor=#FFF3A8;strokeWidth=2;dashed=1;" parent="1" vertex="1">
          <mxGeometry x="570" y="370" width="170" height="30" as="geometry" />
        </mxCell>
        <mxCell id="ZSUZZlGQCimUVaxMRYQN-8" style="edgeStyle=orthogonalEdgeStyle;shape=connector;rounded=1;jumpStyle=none;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=default;align=center;verticalAlign=middle;fontFamily=Helvetica;fontSize=11;fontColor=default;labelBackgroundColor=default;endArrow=none;endFill=0;" parent="1" source="ZSUZZlGQCimUVaxMRYQN-9" target="ZSUZZlGQCimUVaxMRYQN-10" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="140" y="385" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="ZSUZZlGQCimUVaxMRYQN-9" value="credit_memo_line_rin1&#xa;(company,DocEntry, LineNum)" style="strokeWidth=2;shape=mxgraph.flowchart.start_1;whiteSpace=wrap;strokeColor=none;fontColor=#ffffff;fillColor=#002C82;fontFamily=Helvetica;fontSize=11;" parent="1" vertex="1">
          <mxGeometry x="70" y="291.75" width="140" height="54.5" as="geometry" />
        </mxCell>
        <mxCell id="ZSUZZlGQCimUVaxMRYQN-10" value="credit_memo_line_rin1_p1_l20" style="rounded=1;whiteSpace=wrap;html=1;strokeColor=#36393d;fillColor=#FFF3A8;strokeWidth=2;align=center;verticalAlign=middle;fontFamily=Helvetica;fontSize=12;fontColor=default;labelBackgroundColor=none;dashed=1;" parent="1" vertex="1">
          <mxGeometry x="160" y="370" width="190" height="30" as="geometry" />
        </mxCell>
        <mxCell id="ZSUZZlGQCimUVaxMRYQN-17" style="edgeStyle=orthogonalEdgeStyle;shape=connector;rounded=1;jumpStyle=none;orthogonalLoop=1;jettySize=auto;html=1;opacity=40;strokeColor=default;strokeWidth=2;align=center;verticalAlign=middle;fontFamily=Helvetica;fontSize=11;fontColor=default;labelBackgroundColor=default;endArrow=none;endFill=0;" parent="1" source="ZSUZZlGQCimUVaxMRYQN-11" target="ZSUZZlGQCimUVaxMRYQN-9" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="ZSUZZlGQCimUVaxMRYQN-18" style="edgeStyle=orthogonalEdgeStyle;shape=connector;rounded=1;jumpStyle=none;orthogonalLoop=1;jettySize=auto;html=1;opacity=40;strokeColor=default;strokeWidth=2;align=center;verticalAlign=middle;fontFamily=Helvetica;fontSize=11;fontColor=default;labelBackgroundColor=default;endArrow=none;endFill=0;" parent="1" source="ZSUZZlGQCimUVaxMRYQN-11" target="ZSUZZlGQCimUVaxMRYQN-3" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="ZSUZZlGQCimUVaxMRYQN-11" value="credit_memo_line_&lt;div&gt;credit_memo&lt;/div&gt;" style="verticalLabelPosition=middle;verticalAlign=middle;html=1;shape=hexagon;perimeter=hexagonPerimeter2;arcSize=6;size=0.16666666666666666;fillColor=#cce5ff;strokeColor=#36393d;strokeWidth=2;labelPosition=center;align=center;fontFamily=Helvetica;fontSize=11;fontColor=default;labelBackgroundColor=none;dashed=1;" parent="1" vertex="1">
          <mxGeometry x="260" y="300" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="ZSUZZlGQCimUVaxMRYQN-23" style="edgeStyle=orthogonalEdgeStyle;shape=connector;rounded=1;jumpStyle=none;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=default;align=center;verticalAlign=middle;fontFamily=Helvetica;fontSize=11;fontColor=default;labelBackgroundColor=default;endArrow=none;endFill=0;" parent="1" source="ZSUZZlGQCimUVaxMRYQN-12" target="ZSUZZlGQCimUVaxMRYQN-13" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="140" y="155" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="ZSUZZlGQCimUVaxMRYQN-30" style="edgeStyle=orthogonalEdgeStyle;shape=connector;rounded=1;jumpStyle=none;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=default;align=center;verticalAlign=middle;fontFamily=Helvetica;fontSize=11;fontColor=default;labelBackgroundColor=default;endArrow=none;endFill=0;" parent="1" source="ZSUZZlGQCimUVaxMRYQN-12" target="ZSUZZlGQCimUVaxMRYQN-28" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="140" y="55" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="ZSUZZlGQCimUVaxMRYQN-12" value="invoice_line_inv1&#xa;(company,DocEntry, LineNum)" style="strokeWidth=2;shape=mxgraph.flowchart.start_1;whiteSpace=wrap;strokeColor=none;fontColor=#ffffff;fillColor=#002C82;fontFamily=Helvetica;fontSize=11;" parent="1" vertex="1">
          <mxGeometry x="70" y="190" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="ZSUZZlGQCimUVaxMRYQN-13" value="invoice_line_inv1_p1_l20" style="rounded=1;whiteSpace=wrap;html=1;strokeColor=#36393d;fillColor=#FFF3A8;strokeWidth=2;align=center;verticalAlign=middle;fontFamily=Helvetica;fontSize=12;fontColor=default;labelBackgroundColor=none;dashed=1;" parent="1" vertex="1">
          <mxGeometry x="170" y="140" width="140" height="30" as="geometry" />
        </mxCell>
        <mxCell id="ZSUZZlGQCimUVaxMRYQN-16" style="edgeStyle=orthogonalEdgeStyle;shape=connector;rounded=1;jumpStyle=none;orthogonalLoop=1;jettySize=auto;html=1;opacity=40;strokeColor=default;strokeWidth=2;align=center;verticalAlign=middle;fontFamily=Helvetica;fontSize=11;fontColor=default;labelBackgroundColor=default;endArrow=none;endFill=0;" parent="1" source="ZSUZZlGQCimUVaxMRYQN-14" target="ZSUZZlGQCimUVaxMRYQN-12" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="ZSUZZlGQCimUVaxMRYQN-14" value="invoice_line_&lt;div&gt;invoice&lt;/div&gt;" style="verticalLabelPosition=middle;verticalAlign=middle;html=1;shape=hexagon;perimeter=hexagonPerimeter2;arcSize=6;size=0.16666666666666666;fillColor=#cce5ff;strokeColor=#36393d;strokeWidth=2;labelPosition=center;align=center;fontFamily=Helvetica;fontSize=11;fontColor=default;labelBackgroundColor=none;dashed=1;" parent="1" vertex="1">
          <mxGeometry x="260" y="200" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="ZSUZZlGQCimUVaxMRYQN-15" style="edgeStyle=orthogonalEdgeStyle;shape=connector;rounded=1;jumpStyle=none;orthogonalLoop=1;jettySize=auto;html=1;opacity=40;strokeColor=default;strokeWidth=2;align=center;verticalAlign=middle;fontFamily=Helvetica;fontSize=11;fontColor=default;labelBackgroundColor=default;endArrow=none;endFill=0;" parent="1" source="ZSUZZlGQCimUVaxMRYQN-14" target="ZSUZZlGQCimUVaxMRYQN-2" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="420" y="403" as="sourcePoint" />
            <mxPoint x="480" y="403" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="ZSUZZlGQCimUVaxMRYQN-28" value="invoice_line_inv1_&lt;div&gt;korrigiert_v1_b10_current&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;strokeColor=#36393d;fillColor=#FFF3A8;strokeWidth=2;align=center;verticalAlign=middle;fontFamily=Helvetica;fontSize=12;fontColor=default;labelBackgroundColor=none;dashed=1;" parent="1" vertex="1">
          <mxGeometry x="170" y="40" width="200" height="30" as="geometry" />
        </mxCell>
        <mxCell id="ZSUZZlGQCimUVaxMRYQN-32" style="edgeStyle=orthogonalEdgeStyle;shape=connector;rounded=1;jumpStyle=none;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=default;align=center;verticalAlign=middle;fontFamily=Helvetica;fontSize=11;fontColor=default;labelBackgroundColor=default;endArrow=none;endFill=0;" parent="1" source="ZSUZZlGQCimUVaxMRYQN-31" target="ZSUZZlGQCimUVaxMRYQN-9" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="140" y="475" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="ZSUZZlGQCimUVaxMRYQN-31" value="credit_memo_line_rin1_&lt;div&gt;korrigiert_v1_b10_current&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;strokeColor=#36393d;fillColor=#FFF3A8;strokeWidth=2;align=center;verticalAlign=middle;fontFamily=Helvetica;fontSize=12;fontColor=default;labelBackgroundColor=none;dashed=1;" parent="1" vertex="1">
          <mxGeometry x="160" y="460" width="220" height="30" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
