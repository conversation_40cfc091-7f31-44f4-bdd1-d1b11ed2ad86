{"dvdp_compiler": "dvpdc reference compiler,  release 0.6.2", "dvpi_version": "0.6.2", "compile_timestamp": "2024-11-12 18:20:23", "dvpd_version": "0.6.2", "pipeline_name": "rmud_planzahlen_monatlich_p1", "dvpd_filename": "rmud_planzahlen_monatlich_p1.dvpd.json", "tables": [{"table_name": "rgnr_gesellschaft_hub", "table_stereotype": "hub", "schema_name": "rvlt_general", "storage_component": "", "columns": [{"column_name": "MD_INSERTED_AT", "is_nullable": false, "column_class": "meta_load_date", "column_type": "TIMESTAMP"}, {"column_name": "MD_RUN_ID", "is_nullable": false, "column_class": "meta_load_process_id", "column_type": "INT"}, {"column_name": "MD_RECORD_SOURCE", "is_nullable": false, "column_class": "meta_record_source", "column_type": "VARCHAR(255)"}, {"column_name": "HK_RGNR_GESELLSCHAFT", "is_nullable": false, "column_class": "key", "column_type": "CHAR(28)"}, {"column_name": "COMPANY", "is_nullable": true, "column_class": "business_key", "column_type": "VARCHAR(50)", "prio_for_column_position": 50000}]}, {"table_name": "rmud_rgnr_gesellschaft_monatsplanzahl_dlnk", "table_stereotype": "lnk", "schema_name": "rvlt_manual_data", "storage_component": "", "columns": [{"column_name": "MD_INSERTED_AT", "is_nullable": false, "column_class": "meta_load_date", "column_type": "TIMESTAMP"}, {"column_name": "MD_RUN_ID", "is_nullable": false, "column_class": "meta_load_process_id", "column_type": "INT"}, {"column_name": "MD_RECORD_SOURCE", "is_nullable": false, "column_class": "meta_record_source", "column_type": "VARCHAR(255)"}, {"column_name": "HK_RGNR_GESELLSCHAFT", "is_nullable": false, "column_class": "parent_key", "column_type": "CHAR(28)", "parent_key_column_name": "HK_RGNR_GESELLSCHAFT", "parent_table_name": "rgnr_gesellschaft_hub"}, {"column_name": "LK_RMUD_RGNR_GESELLSCHAFT_MONATSPLANZAHL", "is_nullable": false, "column_class": "key", "column_type": "CHAR(28)"}, {"column_name": "JAHR", "is_nullable": true, "column_class": "dependent_child_key", "column_type": "NUMBER(20,0)", "prio_for_column_position": 50000}, {"column_name": "MONAT", "is_nullable": true, "column_class": "dependent_child_key", "column_type": "NUMBER(20,0)", "prio_for_column_position": 50000}]}, {"table_name": "rmud_rgnr_gesellschaft_monatsplanzahl_p1_l10_msat", "table_stereotype": "sat", "schema_name": "rvlt_manual_data", "storage_component": "", "has_deletion_flag": true, "is_effectivity_sat": false, "is_enddated": true, "is_multiactive": true, "compare_criteria": "key+current", "uses_diff_hash": true, "columns": [{"column_name": "MD_INSERTED_AT", "is_nullable": false, "column_class": "meta_load_date", "column_type": "TIMESTAMP"}, {"column_name": "MD_RUN_ID", "is_nullable": false, "column_class": "meta_load_process_id", "column_type": "INT"}, {"column_name": "MD_RECORD_SOURCE", "is_nullable": false, "column_class": "meta_record_source", "column_type": "VARCHAR(255)"}, {"column_name": "MD_IS_DELETED", "is_nullable": false, "column_class": "meta_deletion_flag", "column_type": "BOOLEAN"}, {"column_name": "MD_VALID_BEFORE", "is_nullable": false, "column_class": "meta_load_enddate", "column_type": "TIMESTAMP"}, {"column_name": "LK_RMUD_RGNR_GESELLSCHAFT_MONATSPLANZAHL", "is_nullable": false, "column_class": "parent_key", "column_type": "CHAR(28)", "parent_key_column_name": "LK_RMUD_RGNR_GESELLSCHAFT_MONATSPLANZAHL", "parent_table_name": "rmud_rgnr_gesellschaft_monatsplanzahl_dlnk"}, {"column_name": "GH_RMUD_RGNR_GESELLSCHAFT_MONATSPLANZAHL_P1_L10_MSAT", "is_nullable": false, "column_class": "diff_hash", "column_type": "CHAR(28)"}, {"column_name": "PLANZAHL", "is_nullable": true, "column_class": "content", "column_type": "NUMBER(20,2)", "exclude_from_change_detection": false, "prio_for_column_position": 50000}, {"column_name": "UNTERGRUPPE_JSON", "is_nullable": true, "column_class": "content", "column_type": "VARCHAR", "exclude_from_change_detection": false, "prio_for_column_position": 50000}]}], "data_extraction": {"fetch_module_name": "none - ddl generation only"}, "parse_sets": [{"stage_properties": [{"stage_schema": "stage_rvlt", "stage_table_name": "rmud_planzahlen_monatlich_p1_stage", "storage_component": ""}], "record_source_name_expression": "manuell.planzahlen.monatlich", "fields": [{"field_type": "NUMBER(20,0)", "field_position": 1, "needs_encryption": false, "field_name": "JAHR"}, {"field_type": "NUMBER(20,0)", "field_position": 2, "needs_encryption": false, "field_name": "MONAT"}, {"field_type": "<PERSON><PERSON><PERSON><PERSON>(50)", "field_position": 3, "needs_encryption": false, "field_name": "FIRMA"}, {"field_type": "NUMBER(20,2)", "field_position": 4, "needs_encryption": false, "field_name": "PLANZAHL"}, {"field_type": "VARCHAR", "field_position": 5, "needs_encryption": false, "field_name": "UNTERGRUPPE_JSON"}], "hashes": [{"stage_column_name": "HK_RGNR_GESELLSCHAFT", "hash_origin_table": "rgnr_gesellschaft_hub", "column_class": "key", "hash_fields": [{"field_name": "FIRMA", "prio_in_key_hash": 0, "field_target_table": "rgnr_gesellschaft_hub", "field_target_column": "COMPANY"}], "column_type": "CHAR(28)", "hash_encoding": "BASE64", "hash_function": "sha-1", "hash_concatenation_seperator": "|", "hash_timestamp_format_sqlstyle": "YYYY-MM-DD HH24:MI:SS.US", "hash_null_value_string": "", "model_profile_name": "_default", "hash_name": "KEY_OF_RGNR_GESELLSCHAFT_HUB"}, {"stage_column_name": "LK_RMUD_RGNR_GESELLSCHAFT_MONATSPLANZAHL", "hash_origin_table": "rmud_rgnr_gesellschaft_monatsplanzahl_dlnk", "column_class": "key", "hash_fields": [{"field_name": "FIRMA", "prio_in_key_hash": 0, "field_target_table": "rgnr_gesellschaft_hub", "field_target_column": "COMPANY", "parent_declaration_position": 1}, {"field_name": "JAHR", "prio_in_key_hash": 0, "field_target_table": "rmud_rgnr_gesellschaft_monatsplanzahl_dlnk", "field_target_column": "JAHR"}, {"field_name": "MONAT", "prio_in_key_hash": 0, "field_target_table": "rmud_rgnr_gesellschaft_monatsplanzahl_dlnk", "field_target_column": "MONAT"}], "column_type": "CHAR(28)", "hash_encoding": "BASE64", "hash_function": "sha-1", "hash_concatenation_seperator": "|", "hash_timestamp_format_sqlstyle": "YYYY-MM-DD HH24:MI:SS.US", "hash_null_value_string": "", "model_profile_name": "_default", "hash_name": "KEY_OF_RMUD_RGNR_GESELLSCHAFT_MONATSPLANZAHL_DLNK"}, {"stage_column_name": "GH_RMUD_RGNR_GESELLSCHAFT_MONATSPLANZAHL_P1_L10_MSAT", "hash_origin_table": "rmud_rgnr_gesellschaft_monatsplanzahl_p1_l10_msat", "multi_row_content": true, "column_class": "diff_hash", "hash_fields": [{"field_name": "PLANZAHL", "prio_in_diff_hash": 0, "prio_for_row_order": 50000, "field_target_table": "rmud_rgnr_gesellschaft_monatsplanzahl_p1_l10_msat", "field_target_column": "PLANZAHL"}, {"field_name": "UNTERGRUPPE_JSON", "prio_in_diff_hash": 0, "prio_for_row_order": 50000, "field_target_table": "rmud_rgnr_gesellschaft_monatsplanzahl_p1_l10_msat", "field_target_column": "UNTERGRUPPE_JSON"}], "column_type": "CHAR(28)", "hash_encoding": "BASE64", "hash_function": "sha-1", "hash_concatenation_seperator": "|", "hash_timestamp_format_sqlstyle": "YYYY-MM-DD HH24:MI:SS.US", "hash_null_value_string": "", "model_profile_name": "_default", "related_key_hash": "KEY_OF_RMUD_RGNR_GESELLSCHAFT_MONATSPLANZAHL_DLNK", "hash_name": "DIFF_OF_RMUD_RGNR_GESELLSCHAFT_MONATSPLANZAHL_P1_L10_MSAT"}], "load_operations": [{"table_name": "rgnr_gesellschaft_hub", "relation_name": "/", "operation_origin": "field mapping relation", "hash_mappings": [{"hash_class": "key", "column_name": "HK_RGNR_GESELLSCHAFT", "is_nullable": false, "hash_name": "KEY_OF_RGNR_GESELLSCHAFT_HUB", "stage_column_name": "HK_RGNR_GESELLSCHAFT"}], "data_mapping": [{"column_name": "COMPANY", "field_name": "FIRMA", "column_class": "business_key", "is_nullable": true, "stage_column_name": "FIRMA"}]}, {"table_name": "rmud_rgnr_gesellschaft_monatsplanzahl_dlnk", "relation_name": "/", "operation_origin": "field mapping relation", "hash_mappings": [{"hash_class": "parent_key_1", "column_name": "HK_RGNR_GESELLSCHAFT", "is_nullable": false, "hash_name": "KEY_OF_RGNR_GESELLSCHAFT_HUB", "stage_column_name": "HK_RGNR_GESELLSCHAFT"}, {"hash_class": "key", "column_name": "LK_RMUD_RGNR_GESELLSCHAFT_MONATSPLANZAHL", "is_nullable": false, "hash_name": "KEY_OF_RMUD_RGNR_GESELLSCHAFT_MONATSPLANZAHL_DLNK", "stage_column_name": "LK_RMUD_RGNR_GESELLSCHAFT_MONATSPLANZAHL"}], "data_mapping": [{"column_name": "JAHR", "field_name": "JAHR", "column_class": "dependent_child_key", "is_nullable": true, "stage_column_name": "JAHR"}, {"column_name": "MONAT", "field_name": "MONAT", "column_class": "dependent_child_key", "is_nullable": true, "stage_column_name": "MONAT"}]}, {"table_name": "rmud_rgnr_gesellschaft_monatsplanzahl_p1_l10_msat", "relation_name": "/", "operation_origin": "field mapping relation", "hash_mappings": [{"hash_class": "parent_key", "column_name": "LK_RMUD_RGNR_GESELLSCHAFT_MONATSPLANZAHL", "is_nullable": false, "hash_name": "KEY_OF_RMUD_RGNR_GESELLSCHAFT_MONATSPLANZAHL_DLNK", "stage_column_name": "LK_RMUD_RGNR_GESELLSCHAFT_MONATSPLANZAHL"}, {"hash_class": "diff_hash", "column_name": "GH_RMUD_RGNR_GESELLSCHAFT_MONATSPLANZAHL_P1_L10_MSAT", "is_nullable": false, "hash_name": "DIFF_OF_RMUD_RGNR_GESELLSCHAFT_MONATSPLANZAHL_P1_L10_MSAT", "stage_column_name": "GH_RMUD_RGNR_GESELLSCHAFT_MONATSPLANZAHL_P1_L10_MSAT"}], "data_mapping": [{"column_name": "PLANZAHL", "field_name": "PLANZAHL", "column_class": "content", "is_nullable": true, "stage_column_name": "PLANZAHL"}, {"column_name": "UNTERGRUPPE_JSON", "field_name": "UNTERGRUPPE_JSON", "column_class": "content", "is_nullable": true, "stage_column_name": "UNTERGRUPPE_JSON"}]}], "stage_columns": [{"stage_column_name": "MD_INSERTED_AT", "is_nullable": false, "stage_column_class": "meta_load_date", "column_type": "TIMESTAMP"}, {"stage_column_name": "MD_RUN_ID", "is_nullable": false, "stage_column_class": "meta_load_process_id", "column_type": "INT"}, {"stage_column_name": "MD_RECORD_SOURCE", "is_nullable": false, "stage_column_class": "meta_record_source", "column_type": "VARCHAR(255)"}, {"stage_column_name": "MD_IS_DELETED", "is_nullable": false, "stage_column_class": "meta_deletion_flag", "column_type": "BOOLEAN"}, {"stage_column_name": "HK_RGNR_GESELLSCHAFT", "stage_column_class": "hash", "is_nullable": false, "hash_name": "KEY_OF_RGNR_GESELLSCHAFT_HUB", "column_type": "CHAR(28)"}, {"stage_column_name": "LK_RMUD_RGNR_GESELLSCHAFT_MONATSPLANZAHL", "stage_column_class": "hash", "is_nullable": false, "hash_name": "KEY_OF_RMUD_RGNR_GESELLSCHAFT_MONATSPLANZAHL_DLNK", "column_type": "CHAR(28)"}, {"stage_column_name": "GH_RMUD_RGNR_GESELLSCHAFT_MONATSPLANZAHL_P1_L10_MSAT", "stage_column_class": "hash", "is_nullable": false, "hash_name": "DIFF_OF_RMUD_RGNR_GESELLSCHAFT_MONATSPLANZAHL_P1_L10_MSAT", "column_type": "CHAR(28)"}, {"stage_column_name": "JAHR", "stage_column_class": "data", "field_name": "JAHR", "is_nullable": true, "column_type": "NUMBER(20,0)", "column_classes": ["dependent_child_key"]}, {"stage_column_name": "MONAT", "stage_column_class": "data", "field_name": "MONAT", "is_nullable": true, "column_type": "NUMBER(20,0)", "column_classes": ["dependent_child_key"]}, {"stage_column_name": "FIRMA", "stage_column_class": "data", "field_name": "FIRMA", "is_nullable": true, "column_type": "<PERSON><PERSON><PERSON><PERSON>(50)", "column_classes": ["business_key"]}, {"stage_column_name": "PLANZAHL", "stage_column_class": "data", "field_name": "PLANZAHL", "is_nullable": true, "column_type": "NUMBER(20,2)", "column_classes": ["content"]}, {"stage_column_name": "UNTERGRUPPE_JSON", "stage_column_class": "data", "field_name": "UNTERGRUPPE_JSON", "is_nullable": true, "column_type": "VARCHAR", "column_classes": ["content"]}]}]}