{"dvpd_version": "0.6.2", "stage_properties": [{"stage_schema": "stage_bvlt"}], "pipeline_name": "bgnr_planza<PERSON>en_taeglich_p1", "record_source_name_expression": "bgnr_planza<PERSON>en_taeglich_p1", "data_extraction": {"fetch_module_name": "none - ddl generation only"}, "fields": [{"field_name": "company", "field_type": "VARCHAR(50)", "targets": [{"table_name": "rgnr_gesellschaft_hub"}]}, {"field_name": "tagesdatum", "field_type": "DATE", "targets": [{"table_name": "rgnr_gesellschaft_kalender_dlnk"}]}, {"field_name": "geplanter_umsatz", "field_type": "NUMBER(20,2)", "targets": [{"table_name": "bgnr_gesellschaft_tagesplanzahl_v1_b10_msat"}]}, {"field_name": "verkaufsgebiet", "field_type": "VARCHAR(100)", "targets": [{"table_name": "bgnr_gesellschaft_tagesplanzahl_v1_b10_msat"}]}, {"field_name": "verkaeufernummer", "field_type": "VARCHAR(100)", "targets": [{"table_name": "bgnr_gesellschaft_tagesplanzahl_v1_b10_msat"}]}, {"field_name": "kundengruppe", "field_type": "VARCHAR(100)", "targets": [{"table_name": "bgnr_gesellschaft_tagesplanzahl_v1_b10_msat"}]}], "data_vault_model": [{"schema_name": "rvlt_general", "tables": [{"table_name": "rgnr_gesellschaft_hub", "table_stereotype": "hub", "is_only_structural_element": "true", "hub_key_column_name": "HK_rgnr_gesellschaft"}, {"table_name": "rgnr_gesellschaft_kalender_dlnk", "table_stereotype": "lnk", "link_key_column_name": "LK_rgnr_gesellschaft_kalender", "link_parent_tables": ["rgnr_gesellschaft_hub"]}]}, {"schema_name": "bvlt_general", "tables": [{"table_name": "bgnr_gesellschaft_tagesplanzahl_v1_b10_msat", "table_stereotype": "sat", "is_multiactive": "true", "satellite_parent_table": "rgnr_gesellschaft_kalender_dlnk", "diff_hash_column_name": "gh_bgnr_gesellschaft_tagesplanzahl_v1_b10_msat"}]}]}