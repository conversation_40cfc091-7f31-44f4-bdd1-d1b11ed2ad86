import os
import json

from azure.identity import DefaultAzureCredential
from azure.keyvault.secrets import SecretClient

def connection_keyvault(secret_name):
    from lib.configuration import configuration_read
    # Construct the Key Vault URL
    params = configuration_read('dwh_azrkeyvault','key_vault', ['url'])
    kv_url = params['url']
    # Authenticate using DefaultAzureCredential
    credential = DefaultAzureCredential(managed_identity_client_id=os.getenv('UMI_CLIENT_ID')) #UMI_CLIENT_ID need to be explicit for cloud execution
    client = SecretClient(vault_url=kv_url, credential=credential)
    try:
        # Retrieve the secret
        retrieved_secret = client.get_secret(secret_name)
        return retrieved_secret.value
    except Exception:
        raise


if __name__ == '__main__':
    secret = connection_keyvault('DWH-AZRSQL')
    secret_dict = json.loads(secret)
    print(secret_dict)