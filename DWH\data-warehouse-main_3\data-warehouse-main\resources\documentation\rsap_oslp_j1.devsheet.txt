Data vault pipeline developer cheat sheet 
rendered from  rsap_oslp_j1.dvpi

pipeline name:  rsap_oslp_j1

------------------------------------------------------
record source:  sap.oslp

Source fields:
       COMPANY    Varchar(50)
       SLPCODE    VARCHAR(20)
       JSON_TEXT  VARCHAR


------------------------------------------------------
Table List:
stage_table.rvlt_sap.rsap_oslp_j1_stage
table.rvlt_sap.rsap_sales_employee_oslp_hub
table.rvlt_sap.rsap_sales_employee_oslp_j1_l10_sat

------------------------------------------------------
stage table:  stage_rvlt.rsap_oslp_j1_stage
Field to Stage mapping:
	--business keys,
		COMPANY    >  COMPANY,
		SLPCODE    >  SLPCODE,

	--content,
		JSON_TEXT  >  JSON_TEXT

------------------------------------------------------
Hash value composition

HK_RSAP_SALES_EMPLOYEE_OSLP (key)
		COMPANY 
		SLPCODE 

RH_RSAP_SALES_EMPLOYEE_OSLP_J1_L10_SAT (diff_hash)
		JSON_TEXT 


------------------------------------------------------
Table load method
(STAGE >  VAULT)

rsap_sales_employee_oslp_hub (/) can be loaded by convention
		  key: HK_RSAP_SALES_EMPLOYEE_OSLP  >  HK_RSAP_SALES_EMPLOYEE_OSLP
		  business_key: COMPANY  >  COMPANY 
		  business_key: SLPCODE  >  SLPCODE 

rsap_sales_employee_oslp_j1_l10_sat (/) can be loaded by convention
		  parent_key: HK_RSAP_SALES_EMPLOYEE_OSLP  >  HK_RSAP_SALES_EMPLOYEE_OSLP
		  diff_hash: RH_RSAP_SALES_EMPLOYEE_OSLP_J1_L10_SAT  >  RH_RSAP_SALES_EMPLOYEE_OSLP_J1_L10_SAT
		  content: JSON_TEXT  >  JSON_TEXT 

