Data vault pipeline developer cheat sheet 
rendered from  rsap_oinv_j1.dvpi

pipeline name:  rsap_oinv_j1

------------------------------------------------------
record source:  sap.oinv

Source fields:
       COMPANY    Varchar(50)
       DOCENTRY   INTEGER
       CARDCODE   VARCHAR(20)
       SLPCODE    VARCHAR(20)
       JSON_TEXT  VARCHAR


------------------------------------------------------
Table List:
stage_table.rvlt_sap.rsap_oinv_j1_stage
table.rvlt_sap.rsap_invoice_oinv_hub
table.rvlt_sap.rsap_invoice_oinv_j1_l10_sat
table.rvlt_sap.rsap_business_partner_ocrd_hub
table.rvlt_sap.rsap_sales_employee_oslp_hub
table.rvlt_sap.rsap_invoice_oinv_business_partner_ocrd_sales_employee_oslp_lnk
table.rvlt_sap.rsap_invoice_oinv_business_partner_ocrd_sales_employee_oslp_esat

------------------------------------------------------
stage table:  stage_rvlt.rsap_oinv_j1_stage
Field to Stage mapping:
	--business keys,
		CARDCODE   >  CARDCODE,
		COMPANY    >  COMPANY,
		DOCENTRY   >  DOCENTRY,
		SLPCODE    >  SLPCODE,

	--content,
		JSON_TEXT  >  JSON_TEXT

------------------------------------------------------
Hash value composition

HK_RSAP_INVOICE_OINV (key)
		COMPANY 
		DOCENTRY 

HK_RSAP_BUSINESS_PARTNER_OCRD (key)
		CARDCODE 
		COMPANY 

HK_RSAP_SALES_EMPLOYEE_OSLP (key)
		COMPANY 
		SLPCODE 

LK_RSAP_INVOICE_OINV_BUSINESS_PARTNER_OCRD_SALES_EMPLOYEE_OSLP (key)
		COMPANY 
		DOCENTRY 
		CARDCODE 
		COMPANY 
		COMPANY 
		SLPCODE 

RH_RSAP_INVOICE_OINV_J1_L10_SAT (diff_hash)
		JSON_TEXT 


------------------------------------------------------
Table load method
(STAGE >  VAULT)

rsap_invoice_oinv_hub (/) can be loaded by convention
		  key: HK_RSAP_INVOICE_OINV  >  HK_RSAP_INVOICE_OINV
		  business_key: COMPANY  >  COMPANY 
		  business_key: DOCENTRY  >  DOCENTRY 

rsap_invoice_oinv_j1_l10_sat (/) can be loaded by convention
		  parent_key: HK_RSAP_INVOICE_OINV  >  HK_RSAP_INVOICE_OINV
		  diff_hash: RH_RSAP_INVOICE_OINV_J1_L10_SAT  >  RH_RSAP_INVOICE_OINV_J1_L10_SAT
		  content: JSON_TEXT  >  JSON_TEXT 

rsap_business_partner_ocrd_hub (/) can be loaded by convention
		  key: HK_RSAP_BUSINESS_PARTNER_OCRD  >  HK_RSAP_BUSINESS_PARTNER_OCRD
		  business_key: COMPANY  >  COMPANY 
		  business_key: CARDCODE  >  CARDCODE 

rsap_sales_employee_oslp_hub (/) can be loaded by convention
		  key: HK_RSAP_SALES_EMPLOYEE_OSLP  >  HK_RSAP_SALES_EMPLOYEE_OSLP
		  business_key: COMPANY  >  COMPANY 
		  business_key: SLPCODE  >  SLPCODE 

rsap_invoice_oinv_business_partner_ocrd_sales_employee_oslp_lnk (/) can be loaded by convention
		  parent_key_1: HK_RSAP_INVOICE_OINV  >  HK_RSAP_INVOICE_OINV
		  parent_key_2: HK_RSAP_BUSINESS_PARTNER_OCRD  >  HK_RSAP_BUSINESS_PARTNER_OCRD
		  parent_key_3: HK_RSAP_SALES_EMPLOYEE_OSLP  >  HK_RSAP_SALES_EMPLOYEE_OSLP
		  key: LK_RSAP_INVOICE_OINV_BUSINESS_PARTNER_OCRD_SALES_EMPLOYEE_OSLP  >  LK_RSAP_INVOICE_OINV_BUSINESS_PARTNER_OCRD_SALES_EMPLOYEE_OSLP

rsap_invoice_oinv_business_partner_ocrd_sales_employee_oslp_esat (/) can be loaded by convention
		! Driving keys: HK_RSAP_INVOICE_OINV 
		  parent_key: LK_RSAP_INVOICE_OINV_BUSINESS_PARTNER_OCRD_SALES_EMPLOYEE_OSLP  >  LK_RSAP_INVOICE_OINV_BUSINESS_PARTNER_OCRD_SALES_EMPLOYEE_OSLP

