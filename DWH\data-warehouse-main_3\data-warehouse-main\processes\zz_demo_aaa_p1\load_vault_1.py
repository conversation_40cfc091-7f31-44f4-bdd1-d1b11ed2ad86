"""

Template for a stage+ rvtl step

"""

from lib.cimtjobinstance import CimtJobInstance, cimtjobinstance_job
from lib.connection_snf import  connection_snf_for_dwh_connection_type
from lib.connection_pg import  connection_pg_for_dwh_connection_type

from lib.dvf_basics import dvf_assemble_datavault_hash, Dvf_dwh_connection_type
from lib.dvf_sqlByConvention_snf import dvf_get_datavault_hub_elt_sql, \
    dvf_get_datavault_sat_elt_sql, dvf_execute_elt_statement_list, dvf_get_datavault_lnk_elt_sql, \
    dvf_get_datavault_esat_elt_sql, \
    dvf_get_check_hash_collision_hub_elt_sql, \
    dvf_get_check_hash_collision_lnk_elt_sql, \
    dvf_get_check_singularity_sat_elt_sql
from lib.dbutils_snf import get_snf_dict_insert_sql, execute_snf_dict_bulk_insert

@cimtjobinstance_job
def fetch_and_stage_data(parent_job_instance,dwh_connection,**kwargs):

    my_job_instance = CimtJobInstance(kwargs['instance_job_name'], parent_job_instance)
    my_job_instance.start_instance()

    try:
        # ### FRAMEWORK PHASE: do processing here

        # prepare stage insert operation
        dwh_cursor = dwh_connection.cursor()
        dwh_cursor.execute("TRUNCATE TABLE stage_rvlt.szz_source_demo_aaa_p1")
        insert_statement = get_snf_dict_insert_sql(dwh_connection, "stage_rvlt", "szz_source_demo_aaa_p1")
        stage_data_row = {'md_inserted_at': my_job_instance.get_job_started_at(),
                    'md_record_source': 'szzsource_demo_aaa_p1',
                    'md_job_instance_id': my_job_instance.get_job_instance_id(),
                    'md_is_deleted': False
                    }

        # connect to source, and order data
        source_connection = connection_pg_for_dwh_connection_type(Dvf_dwh_connection_type.owner)
        source_cursor = source_connection.cursor()
        source_sql="""SELECT aaa_bk1, aaa_bk2f, bbb_bk1f, aaa_p1_c1, aaa_p1_c2, aaa_p1_c3, aaa_p1_c4f
                        FROM zz_source_demo.aaa;"""
        source_cursor.execute(source_sql)

        # transform  and stage the source rows into stage
        source_rows = source_cursor.fetchall()

        stage_data_rows = []

        #transform tuples into dictionaries:
        for source_row in source_rows:
            stage_data_row_temp = dict(stage_data_row)
            stage_data_row_temp['aaa_bk1']=source_row[0]
            stage_data_row_temp['aaa_bk2f']=source_row[1]
            stage_data_row_temp['bbb_bk1f']=source_row[2]
            stage_data_row_temp['aaa_p1_c1']=source_row[3]
            stage_data_row_temp['aaa_p1_c2']=source_row[4]
            stage_data_row_temp['aaa_p1_c3']=source_row[5]
            stage_data_row_temp['aaa_p1_c4f']=source_row[6]

            stage_data_row_temp['hk_aaa'] = dvf_assemble_datavault_hash([stage_data_row_temp['aaa_bk1'],  stage_data_row_temp['aaa_bk2f']])
            stage_data_row_temp['hk_bbb'] = dvf_assemble_datavault_hash([stage_data_row_temp['bbb_bk1f']])
            stage_data_row_temp['lk_aaa_bbb'] = dvf_assemble_datavault_hash([stage_data_row_temp['aaa_bk1'],  stage_data_row_temp['aaa_bk2f'],stage_data_row_temp['bbb_bk1f']])
            stage_data_row_temp['rh_aaa_aaa_p1_sat'] = dvf_assemble_datavault_hash([stage_data_row_temp['aaa_p1_c1']
                                                                                  , stage_data_row_temp['aaa_p1_c2']
                                                                                  ,stage_data_row_temp['aaa_p1_c3']
                                                                                  ,stage_data_row_temp['aaa_p1_c4f']])
            stage_data_rows.append(stage_data_row_temp)
            my_job_instance.count_input(1)



        execute_snf_dict_bulk_insert(dwh_cursor, insert_statement, stage_data_rows)
        dwh_connection.commit()

    # ### FRAMEWORK PHASE: Log any exception to instance
    except Exception:
        my_job_instance.end_instance_with_error(1, 'something went wrong')
        raise

    # # ### FRAMEWORK PHASE: End the instance normally
    my_job_instance.end_instance()

@cimtjobinstance_job
def load_data_to_vault(parent_job_instance, dwh_connection,**kwargs):

    # ### FRAMEWORK PHASE: setup your job_instance
    my_job_instance = CimtJobInstance(kwargs['instance_job_name'], parent_job_instance)
    my_job_instance.start_instance()

    try:
        # ### FRAMEWORK PHASE: do processing here

        # general constants
        vault_schema = 'zz_rvlt_demo'
        stage_schema = 'stage_rvlt'
        stage_table = 'szz_source_demo_aaa_p1'


        dwh_cursor = dwh_connection.cursor()

        # ----------

        hash_collision_check_statement_list = dvf_get_check_hash_collision_hub_elt_sql(
                                                        vault_table='aaa_hub',
                                                        stage_hk_column='HK_AAA',
                                                        stage_bk_column_list=['AAA_BK1', 'AAA_BK2F'],
                                                        db_connection=dwh_connection,
                                                        vault_schema=vault_schema,
                                                        stage_schema=stage_schema,
                                                        stage_table=stage_table
                                                    )

        dvf_execute_elt_statement_list(dwh_cursor, hash_collision_check_statement_list)


        statement_list = dvf_get_datavault_hub_elt_sql(
                                                       vault_table = 'aaa_hub',
                                                       stage_hk_column='HK_AAA',
                                                       stage_bk_column_list=['AAA_BK1', 'AAA_BK2F'],
                                                        db_connection = dwh_connection,
                                                        vault_schema = vault_schema,
                                                        stage_schema = stage_schema, stage_table = stage_table,
                                                        meta_job_instance_id = my_job_instance.get_job_instance_id(),
                                                        meta_inserted_at = my_job_instance.get_job_started_at()
                                                       )

        dvf_execute_elt_statement_list(dwh_cursor, statement_list)

        # ----------
        hash_collision_check_statement_list = dvf_get_check_hash_collision_hub_elt_sql(
                                                        vault_table='bbb_hub',
                                                        stage_hk_column='HK_BBB',
                                                        stage_bk_column_list=['BBB_BK1F'],
                                                        db_connection=dwh_connection,
                                                        vault_schema=vault_schema,
                                                        stage_schema=stage_schema,
                                                        stage_table=stage_table
                                                    )

        dvf_execute_elt_statement_list(dwh_cursor, hash_collision_check_statement_list)

        statement_list = dvf_get_datavault_hub_elt_sql(   vault_table = 'bbb_hub',
                                                       stage_hk_column='HK_BBB',
                                                       stage_bk_column_list=['BBB_BK1F'],
                                                        db_connection = dwh_connection,
                                                        vault_schema = vault_schema,
                                                        stage_schema = stage_schema, stage_table = stage_table,
                                                        meta_job_instance_id = my_job_instance.get_job_instance_id(),
                                                        meta_inserted_at = my_job_instance.get_job_started_at()
                                                       )
        dvf_execute_elt_statement_list(dwh_cursor, statement_list)

        dwh_connection.commit()

        # ----------
        hash_collision_check_statement_list = dvf_get_check_hash_collision_lnk_elt_sql(
                                                        vault_table='aaa_bbb_lnk',
                                                        stage_lk_column='LK_AAA_BBB',
                                                        stage_hk_column_list=['HK_AAA', 'HK_BBB'],
                                                        db_connection=dwh_connection,
                                                        vault_schema=vault_schema,
                                                        stage_schema=stage_schema,
                                                        stage_table=stage_table
                                                    )

        dvf_execute_elt_statement_list(dwh_cursor, hash_collision_check_statement_list)

        statement_list = dvf_get_datavault_lnk_elt_sql(
                                                       vault_table='aaa_bbb_lnk',
                                                       stage_lk_column='LK_AAA_BBB',
                                                       stage_hk_column_list=['HK_AAA', 'HK_BBB'],
                                                        db_connection=dwh_connection,
                                                        vault_schema=vault_schema,
                                                        stage_schema=stage_schema, stage_table=stage_table,
                                                        meta_job_instance_id=my_job_instance.get_job_instance_id(),
                                                        meta_inserted_at=my_job_instance.get_job_started_at()
                                                    )
        dvf_execute_elt_statement_list(dwh_cursor, statement_list)

        # ----------

        statement_list = dvf_get_datavault_esat_elt_sql(
                                                        vault_esat_table='aaa_bbb_esat',
                                                        vault_lnk_table='aaa_bbb_lnk',
                                                        stage_lk_column='LK_AAA_BBB',
                                                        vault_driving_key_column_list=['HK_AAA'],
                                                        stage_driving_key_column_list=['HK_AAA'],
                                                        with_deletion_detection=True,
                                                        db_connection = dwh_connection,
                                                        vault_schema = vault_schema,
                                                        stage_schema = stage_schema, stage_table = stage_table,
                                                        meta_job_instance_id = my_job_instance.get_job_instance_id(),
                                                        meta_inserted_at = my_job_instance.get_job_started_at()
                                                        )
        dvf_execute_elt_statement_list(dwh_cursor, statement_list)

        dwh_connection.commit()

        # ----------

        singularity_check_statement_list = dvf_get_check_singularity_sat_elt_sql(vault_table='aaa_aaa_p1_sat',
                                                        stage_hk_column='HK_AAA',
                                                        stage_rh_column='RH_AAA_AAA_P1_SAT',
                                                        stage_schema=stage_schema,
                                                        stage_table=stage_table)

        dvf_execute_elt_statement_list(dwh_cursor, singularity_check_statement_list)

        statement_list = dvf_get_datavault_sat_elt_sql( vault_table='aaa_aaa_p1_sat',
                                                       stage_hk_column='HK_AAA',
                                                       stage_rh_column='RH_AAA_AAA_P1_SAT',
                                                       with_deletion_detection=True,
                                                        db_connection=dwh_connection,
                                                        vault_schema=vault_schema,
                                                        stage_schema=stage_schema, stage_table=stage_table,
                                                        meta_job_instance_id=my_job_instance.get_job_instance_id(),
                                                        meta_inserted_at=my_job_instance.get_job_started_at()
                                                        )
        dvf_execute_elt_statement_list(dwh_cursor, statement_list)

        dwh_connection.commit()

    # ### FRAMEWORK PHASE: Log any exception to instance
    except Exception:
        my_job_instance.end_instance_with_error(1, 'something went wrong')
        raise

    # # ### FRAMEWORK PHASE: End the instance normally
    my_job_instance.end_instance()


@cimtjobinstance_job
def load_vault_1(parent_job_instance=None, **kwargs):
    """Step A of the template, processing a given file"""

    # ### FRAMEWORK PHASE: setup your job_instance
    my_job_instance = CimtJobInstance(kwargs['instance_job_name'], parent_job_instance)
    my_job_instance.start_instance()

    try:
        # ### FRAMEWORK PHASE: do processing here
        dwh_connection = connection_snf_for_dwh_connection_type(Dvf_dwh_connection_type.raw_vault)
        fetch_and_stage_data(my_job_instance, dwh_connection)
        load_data_to_vault(my_job_instance, dwh_connection)
        # differences: list[str] = get_stage_rvlt_differences(db_connection)
        # if len(differences) > 0:
        #     raise TestFailedError("There are issues regarding the datasets for: " + ", ".join(differences))
        dwh_connection.close()

    # ### FRAMEWORK PHASE: Log any exception to instance
    except Exception:
        my_job_instance.end_instance_with_error(1, 'some processing failed')
        raise

    # # ### FRAMEWORK PHASE: End the instance normally
    my_job_instance.end_instance()


if __name__ == '__main__':
    # local execution
    load_vault_1()
