# DVPD 快速参考指南
## Data Vault Pipeline Definition 开发速查手册

### 版本信息
- **文档版本**: 1.0
- **DVPD版本**: 0.6.2
- **适用范围**: 快速开发和问题排查

---

## 目录

1. [DVPD配置模板](#dvpd配置模板)
2. [常用代码片段](#常用代码片段)
3. [命名规范](#命名规范)
4. [SQL模板](#sql模板)
5. [调试技巧](#调试技巧)
6. [常见错误](#常见错误)

---

## DVPD配置模板

### 基础配置结构
```json
{
    "dvpd_version": "0.6.2",
    "stage_properties": [
        {
            "stage_schema": "stage_rvlt",
            "stage_table_name": "{pipeline_name}_stage"
        }
    ],
    "pipeline_name": "{pipeline_name}",
    "record_source_name_expression": "{source_system}.{entity}",
    "data_extraction": {
        "fetch_module_name": "none - ddl and cnode snippet generation only"
    },
    "fields": [],
    "data_vault_model": []
}
```

### Hub配置模板
```json
{
    "field_name": "{business_key}",
    "field_type": "VARCHAR(50)",
    "targets": [{"table_name": "{HUB_TABLE_NAME}"}]
}
```

### Link配置模板
```json
{
    "field_name": "{dependent_context}",
    "field_type": "INTEGER",
    "targets": [{"table_name": "{LINK_TABLE_NAME}"}]
}
```

### Satellite配置模板
```json
{
    "field_name": "json_text",
    "field_type": "VARCHAR",
    "targets": [{
        "table_name": "{SATELLITE_TABLE_NAME}",
        "exclude_json_paths_from_change_detection": ["UpdateDate", "LastModified"]
    }]
}
```

### Data Vault模型配置
```json
"data_vault_model": [
    {
        "schema_name": "rvlt_{domain}",
        "tables": [
            {
                "table_name": "{HUB_TABLE_NAME}",
                "table_stereotype": "hub",
                "hub_key_column_name": "HK_{HUB_NAME}"
            },
            {
                "table_name": "{LINK_TABLE_NAME}",
                "table_stereotype": "lnk",
                "link_key_column_name": "LK_{LINK_NAME}",
                "link_parent_tables": ["{HUB_TABLE_NAME}"]
            },
            {
                "table_name": "{SATELLITE_TABLE_NAME}",
                "table_stereotype": "sat",
                "satellite_parent_table": "{PARENT_TABLE_NAME}",
                "diff_hash_column_name": "RH_{SATELLITE_NAME}"
            }
        ]
    }
]
```

---

## 常用代码片段

### Python导入语句
```python
# 基础导入
import json
from lib.cimtjobinstance import CimtJobInstance, cimtjobinstance_job
from lib.connection_snf import connection_snf_for_dwh_connection_type
from lib.connection_azrblob import connection_azrblob
from lib.dvf_basics import dvf_assemble_datavault_hash, Dvf_dwh_connection_type
from lib.blobstorage_utils import fetch_json_source_file_from_blob_container
from lib.json_utils import get_json_text_content_column, sort_json_content_column
from lib.dbutils_snf import get_snf_dict_insert_sql, execute_snf_dict_bulk_insert

# DVF SQL生成函数
from lib.dvf_sqlByConvention_snf import (
    dvf_get_datavault_hub_elt_sql,
    dvf_get_datavault_sat_elt_sql,
    dvf_get_datavault_lnk_elt_sql,
    dvf_get_datavault_dlnk_elt_sql,
    dvf_execute_elt_statement_list,
    dvf_get_check_hash_collision_hub_elt_sql,
    dvf_get_check_hash_collision_dlnk_elt_sql,
    dvf_get_check_singularity_sat_elt_sql
)
```

### 作业实例装饰器
```python
@cimtjobinstance_job
def your_function(parent_job_instance=None, **kwargs):
    # 初始化作业实例
    my_job_instance = CimtJobInstance(kwargs['instance_job_name'], parent_job_instance)
    my_job_instance.start_instance()
    
    try:
        # 业务逻辑
        pass
    except Exception:
        my_job_instance.end_instance_with_error(1, 'processing failed')
        raise
    
    my_job_instance.end_instance()
```

### 哈希计算
```python
# Hub哈希键
hk_value = dvf_assemble_datavault_hash([
    business_key_1,
    business_key_2,
    business_key_3
])

# Link哈希键
lk_value = dvf_assemble_datavault_hash([
    dependent_context,
    business_key_1,
    business_key_2
])

# 记录哈希
rh_value = dvf_assemble_datavault_hash([json_content])
```

### 暂存数据准备
```python
# 基础元数据
stage_data_row = {
    'md_inserted_at': my_job_instance.get_job_started_at().isoformat(),
    'md_record_source': 'source_system.entity',
    'md_run_id': my_job_instance.get_job_instance_id(),
    'md_is_deleted': False
}

# JSON处理
json_text = sort_json_content_column(
    get_json_text_content_column(source_row, keys_to_remove=bk_keys)
)
stage_data_row['json_text'] = json.dumps(json_text)
```

---

## 命名规范

### 表命名规范
```
Hub表:        {DOMAIN}_{ENTITY}_{SOURCE}_HUB
Link表:       {DOMAIN}_{ENTITY1}_{ENTITY2}_LNK
Dependent Link: {DOMAIN}_{PARENT}_{CHILD}_DLNK
Satellite表:  {DOMAIN}_{PARENT}_{SOURCE}_{LEVEL}_SAT
暂存表:       {pipeline_name}_stage
```

### 字段命名规范
```
哈希键:       HK_{ENTITY_NAME}
链接键:       LK_{LINK_NAME}
记录哈希:     RH_{SATELLITE_NAME}
元数据:       MD_{METADATA_TYPE}
业务键:       {ORIGINAL_FIELD_NAME}
```

### 示例
```
Hub表:        RSAP_CUSTOMER_OCRD_HUB
Link表:       RSAP_CUSTOMER_ORDER_LNK
Satellite表:  RSAP_CUSTOMER_OCRD_J1_L10_SAT
哈希键:       HK_RSAP_CUSTOMER_OCRD
链接键:       LK_RSAP_CUSTOMER_ORDER
记录哈希:     RH_RSAP_CUSTOMER_OCRD_J1_L10_SAT
```

---

## SQL模板

### Hub加载SQL
```python
statement_list = dvf_get_datavault_hub_elt_sql(
    vault_table='{hub_table_name}',
    vault_schema='{vault_schema}',
    stage_hk_column='{HK_COLUMN}',
    stage_bk_column_list=['{BK1}', '{BK2}'],
    db_connection=dwh_connection,
    stage_schema='{stage_schema}',
    stage_table='{stage_table}',
    meta_job_instance_id=my_job_instance.get_job_instance_id(),
    meta_inserted_at=my_job_instance.get_job_started_at()
)
```

### Link加载SQL
```python
statement_list = dvf_get_datavault_dlnk_elt_sql(
    vault_table='{link_table_name}',
    vault_schema='{vault_schema}',
    stage_lk_column='{LK_COLUMN}',
    stage_hk_column_list=['{HK_PARENT}'],
    stage_dc_column_list=['{DEPENDENT_CONTEXT}'],
    db_connection=dwh_connection,
    stage_schema='{stage_schema}',
    stage_table='{stage_table}',
    meta_job_instance_id=my_job_instance.get_job_instance_id(),
    meta_inserted_at=my_job_instance.get_job_started_at()
)
```

### Satellite加载SQL
```python
statement_list = dvf_get_datavault_sat_elt_sql(
    vault_table='{satellite_table_name}',
    vault_schema='{vault_schema}',
    stage_hk_column='{PARENT_KEY_COLUMN}',
    stage_rh_column='{RH_COLUMN}',
    stage_content_column_list=['{CONTENT_COLUMNS}'],
    with_deletion_detection=False,
    db_connection=dwh_connection,
    stage_schema='{stage_schema}',
    stage_table='{stage_table}',
    meta_job_instance_id=my_job_instance.get_job_instance_id(),
    meta_inserted_at=my_job_instance.get_job_started_at()
)
```

### 数据质量检查SQL
```python
# Hub哈希冲突检查
hash_collision_check = dvf_get_check_hash_collision_hub_elt_sql(
    vault_table='{hub_table}',
    vault_schema='{vault_schema}',
    stage_hk_column='{HK_COLUMN}',
    stage_bk_column_list=['{BK_COLUMNS}'],
    db_connection=dwh_connection,
    stage_schema='{stage_schema}',
    stage_table='{stage_table}'
)

# Satellite单一性检查
singularity_check = dvf_get_check_singularity_sat_elt_sql(
    vault_table='{satellite_table}',
    stage_hk_column='{PARENT_KEY}',
    stage_rh_column='{RH_COLUMN}',
    stage_schema='{stage_schema}',
    stage_table='{stage_table}'
)
```

---

## 调试技巧

### 日志配置
```python
import logging

# 配置详细日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)
logger.debug(f"处理记录数: {len(stage_data_rows)}")
```

### 数据采样
```python
# 检查前几条记录
def debug_sample_data(data_list, sample_size=3):
    for i, record in enumerate(data_list[:sample_size]):
        print(f"记录 {i+1}: {record}")

# 检查哈希计算
def debug_hash(business_keys, hash_result):
    input_str = "|".join(str(k) for k in business_keys)
    print(f"哈希输入: '{input_str}' -> 输出: '{hash_result}'")
```

### 性能监控
```python
import time

def timing_wrapper(func):
    def wrapper(*args, **kwargs):
        start = time.time()
        result = func(*args, **kwargs)
        end = time.time()
        print(f"{func.__name__} 执行时间: {end-start:.2f}秒")
        return result
    return wrapper
```

---

## 常见错误

### 1. 哈希冲突
```
错误: HASH COLLISION DETECTED
原因: 不同业务键组合产生相同哈希
解决: 检查业务键定义，确保唯一性
```

### 2. JSON解析错误
```
错误: JSONDecodeError
原因: 源数据格式问题
解决: 添加数据清理和异常处理
```

### 3. 连接超时
```
错误: Connection timeout
原因: 网络或数据库负载问题
解决: 增加超时设置，实现重试机制
```

### 4. 权限错误
```
错误: Access denied
原因: 数据库或存储权限不足
解决: 检查和配置相应权限
```

### 5. 内存不足
```
错误: MemoryError
原因: 处理数据量过大
解决: 分批处理，优化内存使用
```

---

## 快速检查清单

### 开发前检查
- [ ] DVPD配置文件语法正确
- [ ] 业务键定义完整
- [ ] 表名和字段名符合规范
- [ ] 数据类型定义合理

### 开发中检查
- [ ] 导入语句完整
- [ ] 作业实例正确初始化
- [ ] 异常处理机制完善
- [ ] 哈希计算逻辑正确

### 测试前检查
- [ ] 数据库连接配置正确
- [ ] 源数据格式验证
- [ ] 暂存表结构匹配
- [ ] 目标表权限充足

### 部署前检查
- [ ] 环境配置一致
- [ ] 依赖包版本匹配
- [ ] 监控告警配置
- [ ] 回滚方案准备

---

*快速参考指南 v1.0*  
*适用于DVPD 0.6.2及以上版本*
