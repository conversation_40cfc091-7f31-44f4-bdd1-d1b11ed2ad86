-- generated script for stage_rvlt.szz_source_demo_aaa_p1

-- DROP TABLE stage_rvlt.szz_source_demo_aaa_p1;

CREATE TABLE stage_rvlt.szz_source_demo_aaa_p1 (
--metadata,
MD_INSERTED_AT TIMESTAMP NOT NULL,
MD_JOB_INSTANCE_ID INT NOT NULL,
MD_RECORD_SOURCE VARCHAR(255) NOT NULL,
MD_IS_DELETED BOOLEAN NOT NULL,
--hash keys,
HK_AAA CHAR(28) NOT NULL,
HK_B<PERSON> CHAR(28) NOT NULL,
LK_AAA_BBB CHAR(28) NOT NULL,
RH_AAA_AAA_P1_SAT CHAR(28) NOT NULL,
--business keys,
AAA_BK1 integer  NULL,
AAA_BK2F integer  NULL,
BBB_BK1F integer  NULL,
--content,
AAA_P1_C1 VARCHAR(20)  NULL,
AAA_P1_C2 VARCHAR(100)  NULL,
AAA_P1_C3 integer  NULL,
AAA_P1_C4F integer  NULL
);
-- end of script --