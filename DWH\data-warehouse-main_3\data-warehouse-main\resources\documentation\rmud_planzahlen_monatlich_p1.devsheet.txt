Data vault pipeline developer cheat sheet 
rendered from  rmud_planzahlen_monatlich_p1.dvpi

pipeline name:  rmud_planzahlen_monatlich_p1

------------------------------------------------------
record source:  manuell.planzahlen.monatlich

Source fields:
       JAHR              NUMBER(20,0)
       MONAT             NUMBER(20,0)
       FIRMA             Varchar(50)
       PLANZAHL          NUMBER(20,2)
       UNTERGRUPPE_JSON  VARCHAR


------------------------------------------------------
Table List:
stage_table.rvlt_manual_data.rmud_planzahlen_monatlich_p1_stage
table.rvlt_general.rgnr_gesellschaft_hub
table.rvlt_manual_data.rmud_rgnr_gesellschaft_monatsplanzahl_dlnk
table.rvlt_manual_data.rmud_rgnr_gesellschaft_monatsplanzahl_p1_l10_msat

------------------------------------------------------
stage table:  stage_rvlt.rmud_planzahlen_monatlich_p1_stage
Field to Stage mapping:
	--business keys,
		FIRMA             >  FIRMA,
		JAHR              >  JAHR,
		MONAT             >  MONAT,

	--content,
		PLANZAHL          >  PLANZAHL,
		UNTERGRUPPE_JSON  >  UNTERGRUPPE_JSON

------------------------------------------------------
Hash value composition

HK_RGNR_GESELLSCHAFT (key)
		FIRMA 

LK_RMUD_RGNR_GESELLSCHAFT_MONATSPLANZAHL (key)
		JAHR 
		MONAT 
		FIRMA 

GH_RMUD_RGNR_GESELLSCHAFT_MONATSPLANZAHL_P1_L10_MSAT (diff_hash)
		PLANZAHL 
		UNTERGRUPPE_JSON 


------------------------------------------------------
Table load method
(STAGE >  VAULT)

rgnr_gesellschaft_hub (/) needs explicit loading:
		  key: HK_RGNR_GESELLSCHAFT  >  HK_RGNR_GESELLSCHAFT
		* business_key: FIRMA  >  COMPANY 

rmud_rgnr_gesellschaft_monatsplanzahl_dlnk (/) can be loaded by convention
		  parent_key_1: HK_RGNR_GESELLSCHAFT  >  HK_RGNR_GESELLSCHAFT
		  key: LK_RMUD_RGNR_GESELLSCHAFT_MONATSPLANZAHL  >  LK_RMUD_RGNR_GESELLSCHAFT_MONATSPLANZAHL
		  dependent_child_key: JAHR  >  JAHR 
		  dependent_child_key: MONAT  >  MONAT 

rmud_rgnr_gesellschaft_monatsplanzahl_p1_l10_msat (/) can be loaded by convention
		  parent_key: LK_RMUD_RGNR_GESELLSCHAFT_MONATSPLANZAHL  >  LK_RMUD_RGNR_GESELLSCHAFT_MONATSPLANZAHL
		  diff_hash: GH_RMUD_RGNR_GESELLSCHAFT_MONATSPLANZAHL_P1_L10_MSAT  >  GH_RMUD_RGNR_GESELLSCHAFT_MONATSPLANZAHL_P1_L10_MSAT
		  content: PLANZAHL  >  PLANZAHL 
		  content: UNTERGRUPPE_JSON  >  UNTERGRUPPE_JSON 

