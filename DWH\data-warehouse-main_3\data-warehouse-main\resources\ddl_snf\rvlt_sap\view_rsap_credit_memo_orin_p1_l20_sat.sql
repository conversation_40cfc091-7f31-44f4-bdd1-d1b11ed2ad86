CREATE OR REPLACE VIEW rvlt_sap.rsap_credit_memo_orin_p1_l20_sat
AS
SELECT
	MD_INSERTED_AT,
    MD_RUN_ID,
    MD_RECORD_SOURCE,
    MD_IS_DELETED,
    MD_VALID_BEFORE,
    HK_RSAP_CREDIT_MEMO_ORIN,
    RH_RSAP_CREDIT_MEMO_ORIN_J1_L10_SAT,
    JSON_EXTRACT_PATH_TEXT(json_text, 'CANCELED') as CANCELED,
    JSON_EXTRACT_PATH_TEXT(json_text, 'DocCur') as DOCCUR,
    JSON_EXTRACT_PATH_TEXT(json_text, 'DiscSum') as DISCSUM,
    TO_DATE(JSON_EXTRACT_PATH_TEXT(json_text, 'DocDate')) as DOCDATE,
    TO_NUMBER(JSON_EXTRACT_PATH_TEXT(JSON_TEXT, 'DocRate'),10,3) as <PERSON>OC<PERSON><PERSON>,
    JSO<PERSON>_EXTRACT_PATH_TEXT(json_text, 'DocType') as DOC<PERSON><PERSON><PERSON>,
    TO_NUMBER(JSON_EXTRACT_PATH_TEXT(JSON_TEXT, 'DocTotal'),10,2) as DOCTOTAL,
    TO_NUMBER(JSON_EXTRACT_PATH_TEXT(JSON_TEXT, 'DpmAmnt'),10,2) as DPMAMNT,
    TO_NUMBER(JSON_EXTRACT_PATH_TEXT(JSON_TEXT, 'TotalExpns'),10,2) as TOTALEXPNS,
    TO_DATE(JSON_EXTRACT_PATH_TEXT(json_text, 'UpdateDate')) as UPDATEDATE,
    TO_NUMBER(JSON_EXTRACT_PATH_TEXT(JSON_TEXT, 'VatSum'),10,2) as VATSUM
FROM rvlt_sap.rsap_credit_memo_orin_j1_l10_sat;

--select * from   rvlt_sap.rsap_credit_memo_orin_p1_l20_sat order by md_inserted_at desc */
