CREATE VIEW rvlt_sap.rsap_currency_ortt_exchange_rate_p1_l20_sat(
    MD_INSERTED_AT,
	MD_RUN_ID,
	MD_RECORD_SOURCE,
    MD_IS_DELETED,
    MD_VALID_BEFORE,
    LK_RSAP_ORTT_EXCHANGE_RATE_DLNK,
    RH_RSAP_CURRENCY_ORTT_EXCHANGE_RATE_J1_L10_SAT,
    RATE,
    DATASOURCE,
    UPDATEDATE,
    USERSIGN
)
AS
SELECT
    MD_INSERTED_AT,
	MD_RUN_ID,
	MD_RECORD_SOURCE,
    MD_IS_DELETED,
    MD_VALID_BEFORE,
    LK_RSAP_ORTT_EXCHANGE_RATE_DLNK,
    RH_RSAP_CURRENCY_ORTT_EXCHANGE_RATE_J1_L10_SAT,
    TO_NUMBER(JSON_EXTRACT_PATH_TEXT(JSON_TEXT, 'Rate'),10,3) as RA<PERSON>,
    JSO<PERSON>_EXTRACT_PATH_TEXT(JSON_TEXT, 'DataSource') as DAT<PERSON>OUR<PERSON>,
    TO_DATE(JSON_EXTRACT_PATH_TEXT(JSON_TEXT, 'UpdateDate')) as UPDATEDATE,
    JSON_EXTRACT_PATH_TEXT(JSON_TEXT, 'UserSign') as USERSIGN,
FROM rvlt_sap.rsap_currency_ortt_exchange_rate_j1_l10_sat;
