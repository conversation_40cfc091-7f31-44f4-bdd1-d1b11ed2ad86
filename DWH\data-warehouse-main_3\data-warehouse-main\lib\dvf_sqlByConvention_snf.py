"""
Module with functions to generate sql statements and snippets
"""
import json

from lib.connection_snf import connection_snf_for_dwh_connection_type
from lib.dvf_basics import HASHKEY_FOR_MISSING_DATA, HASHKEY_FOR_DELIVERED_NULL, FAR_FUTURE_DATE_SQL_STRING, \
    Dvf_generatorSettingsError, Dvf_dwh_connection_type
from lib.configuration import configuration_read




def dvf_get_datavault_column_lists_from_snf(db_connection, table_schema, table_name):
    """Gets provides columnnames  of a postgresql table as 4 lists, devided into content_columns,
     dv_key_columns, dv_compare_columns and meta_columns"""
    db_repo_cursor = db_connection.cursor()
    db_repo_cursor.execute("""SELECT lower(column_name) FROM INFORMATION_SCHEMA.COLUMNS 
                            WHERE TABLE_SCHEMA = upper(%s)
                            AND TABLE_NAME = upper(%s) order by 1""",
                           (table_schema, table_name.lower()))
    meta_column_list = []
    vault_key_column_list = []
    vault_compare_column_list = []
    content_column_list = []
    column_row = db_repo_cursor.fetchone()
    while column_row:
        column_name = column_row[0]
        if column_name[:3] in ['hk_', 'lk_']:
            vault_key_column_list.append(column_name)
        elif column_name[:3] in ['rh_', 'gh_']:
            vault_compare_column_list.append(column_name)
        elif column_name[:3] == 'md_':
            meta_column_list.append(column_name)
        else:
            content_column_list.append(column_name)
        column_row = db_repo_cursor.fetchone()

    if len(content_column_list) + len(vault_key_column_list) + len(meta_column_list) == 0:
        raise Dvf_generatorSettingsError(f"no columns collected for >{table_schema}.{table_name}< . Does this object exist?")

    result = {'meta_columns': meta_column_list,
              'dv_key_columns': vault_key_column_list,
              'dv_compare_columns': vault_compare_column_list,
              'content_columns': content_column_list}
    return result

def dvf_get_dvpi_like_column_lists_by_convention_from_snf(db_connection, schema_name, table_name):
    """Gets essential column data of a table"""
    db_repo_cursor = db_connection.cursor()
                                     #0
    db_repo_cursor.execute("""SELECT upper(column_name) , /* 0 */
                                     data_type , /* 1 */
                                     character_maximum_length, /* 2 */
                                     numeric_precision, /* 3 */
                                     numeric_scale, /* 4 */
                                     datetime_precision /* 5 */
                            FROM INFORMATION_SCHEMA.COLUMNS 
                            WHERE TABLE_SCHEMA = upper(%s)
                            AND TABLE_NAME = upper(%s) order by 1""",
                           (schema_name.lower(), table_name.lower()))

    type_translation={'character varying':{'column_type':'VARCHAR' ,'add_charlength':True,'add_numeric_precision':False},
                      'character': {'column_type': 'CHAR', 'add_charlength': True,'add_numeric_precision': False},
                      'numeric': {'column_type': 'NUMERIC', 'add_charlength': False,'add_numeric_precision': True},
                      }
    # thie following map depends on the model profile
    name_to_meta_class_translation={'MD_INSERTED_AT':'meta_load_date','MD_VALID_BEFORE':'meta_load_enddate'
        ,'MD_IS_DELETED':'meta_deletion_flag','MD_RUN_ID':'meta_load_process_id'
        ,'MD_RECORD_SOURCE':'meta_record_source',}
    table_columns = []
    column_row = db_repo_cursor.fetchone()
    table_stereotype = table_name[-3:].lower()

    while column_row:
        column_name=column_row[0]
        if column_row[1] in type_translation:
            translation=type_translation[ column_row[1] ]
            if translation['add_charlength']:
                column_type=f"{translation['column_type']}({ column_row[2]})"
            elif translation['add_numeric_precision']:
                column_type = f"{translation['column_type']}({column_row[3]},{column_row[4]})"
            else:
                column_type = f"{translation['column_type']}"
        else:
            column_type= column_row[1]

        if column_name in name_to_meta_class_translation:
            column_class=name_to_meta_class_translation[column_name]
        elif table_stereotype=='hub':
            if  column_name[:3]=='HK_':
                column_class='key'
            else:
                column_class='business_key'
        elif table_stereotype == 'sat':
            if column_name[:3] in('HK_','LK_'):
                column_class = 'parent_key'
            elif column_name[:3] in('RH_','GH_'):
                column_class = 'diff_hash'
            else:
                column_class='content'
        elif table_stereotype == 'lnk':
            if column_name[:3] == 'LK_':
                column_class = 'key'
            elif column_name[:3] == 'HK_':
                column_class = 'parent_key'
            else:
                column_class='dependent_child_key'


        table_column={'column_name': column_name,
                      'column_type': column_type,
                      'column_class':column_class,
                      'numeric_precision':column_row[3],
                      'numeric_scale': column_row[4],
                      'is_numeric':(column_row[3]!=None),
                      'is_datetime':(column_row[5]!=None)}
        table_columns.append(table_column)
        column_row = db_repo_cursor.fetchone()

    if len(table_columns)  == 0:
        raise Exception(f"Could not determine columns for {schema_name}.{table_name} . Does this object exist?")

    return table_columns


def dvf_get_datavault_hub_elt_sql(db_connection, vault_schema, vault_table, stage_schema, stage_table, stage_hk_column,
                                  stage_bk_column_list, meta_job_instance_id, meta_inserted_at):
    """generate all statements to load data from stage to a hub. Relies on naming data model conventions.
    columns must always be listed in alphabetical order"""

    statement_list = []

    vault_column_dict = dvf_get_datavault_column_lists_from_snf(db_connection, vault_schema, vault_table)
    vault_column_list = ['MD_INSERTED_AT', 'MD_RUN_ID', 'MD_RECORD_SOURCE']
    vault_column_list.extend(vault_column_dict['dv_key_columns'])
    vault_column_list.extend(vault_column_dict['content_columns'])

    vault_hk_column = vault_column_dict['dv_key_columns'][0]  # there can be only one key column in the hub

    insert_column_section = ','.join(vault_column_list)

    vault_bk_column_list = vault_column_dict['content_columns']

    stage_hk_column = stage_hk_column.lower()
    stage_bk_column_snip = "stage." + ",stage.".join(stage_bk_column_list)

    join_condition = ""
    group_by_snip = "GROUP BY 1,2,3,4"

    if len(stage_bk_column_list) != len(vault_bk_column_list):
        raise Dvf_generatorSettingsError("bk colums list of stage does not have same number of elements like hub table ["+
                                         "|".join(stage_bk_column_list)+ "] <> ["+ " |".join(vault_bk_column_list)+"]")

    for i in range(len(vault_bk_column_list)):
        if i > 0:
            join_condition += " AND "
        join_condition += f"equal_null(stage.{stage_bk_column_list[i]},hub.{vault_bk_column_list[i]})"
        group_by_snip += "," + str(i + 5)

    statement = f"""  /* Hub insert statement */
        INSERT INTO {vault_schema}.{vault_table} ({insert_column_section}) 
        SELECT DISTINCT '{meta_inserted_at}'::timestamp,{meta_job_instance_id},stage.MD_RECORD_SOURCE,
                stage.{stage_hk_column},{stage_bk_column_snip}
        FROM {stage_schema}.{stage_table} stage
        LEFT OUTER JOIN  {vault_schema}.{vault_table} hub ON {join_condition}
        WHERE hub.{vault_hk_column} is null
        AND stage.{stage_hk_column} != '{HASHKEY_FOR_MISSING_DATA}'
        AND stage.{stage_hk_column} != '{HASHKEY_FOR_DELIVERED_NULL}'
        {group_by_snip}
        """
    statement_list.append(statement)
    return statement_list


def dvf_get_datavault_lnk_elt_sql(db_connection, vault_schema, vault_table, stage_schema, stage_table, stage_lk_column,
                                  stage_hk_column_list, meta_job_instance_id, meta_inserted_at):
    """generate all statements to load data from stage to a link. Relies on naming data model conventions.
    colums must always be listet in alphebetical order"""

    return dvf_get_datavault_dlnk_elt_sql(db_connection, vault_schema, vault_table, stage_schema, stage_table,
                                          stage_lk_column,
                                          stage_hk_column_list, meta_job_instance_id, meta_inserted_at, [])


def dvf_get_datavault_dlnk_elt_sql(db_connection, vault_schema, vault_table, stage_schema, stage_table, stage_lk_column,
                                   stage_hk_column_list, meta_job_instance_id, meta_inserted_at, stage_dc_column_list):
    """generate all statements to load data from stage to a link. Relies on naming data model conventions.
    colums must always be listet in alphebetical order"""

    statement_list = []

    vault_column_dict = dvf_get_datavault_column_lists_from_snf(db_connection, vault_schema, vault_table)
    vault_column_list = ['MD_INSERTED_AT', 'MD_RUN_ID', 'MD_RECORD_SOURCE']
    vault_column_list.extend(vault_column_dict['dv_key_columns'])
    vault_column_list.extend(vault_column_dict['content_columns'])
    insert_column_section = ','.join(vault_column_list)

    vault_hk_column_list = []
    vault_lk_column = ""
    for key_column in vault_column_dict['dv_key_columns']:
        if key_column[:3] == "lk_":
            vault_lk_column = key_column
        else:
            vault_hk_column_list.append(key_column)

    if len(vault_hk_column_list) != len(stage_hk_column_list):
        raise Dvf_generatorSettingsError("Hub key column list of stage does not have same number of elements like link table",
                                         stage_hk_column_list, "<>", vault_hk_column_list)

    stage_hk_column_snip = "stage." + ",stage.".join(stage_hk_column_list)

    vault_dg_column_list = vault_column_dict['content_columns']  # content in linktable is a degenerated attribute
    if len(vault_dg_column_list) != len(stage_dc_column_list):
        raise Dvf_generatorSettingsError("degenerated column list of stage does not have same number of elements like link table",
                                         stage_dc_column_list, "<>", vault_dg_column_list)

    stage_dg_column_snip = ""
    if len(vault_dg_column_list) > 0:
        stage_dg_column_snip = ",stage." + ",stage.".join(stage_dc_column_list)

    join_condition = ""
    group_by_snip = "GROUP BY 1,2,3,4"

    for i in range(len(vault_hk_column_list)):
        if i > 0:
            join_condition += " AND "
        join_condition += f" stage.{stage_hk_column_list[i]} = lnk.{vault_hk_column_list[i]}"
        group_by_snip += "," + str(i + 5)

    for i in range(len(vault_dg_column_list)):
        join_condition += " AND "
        join_condition += f" equal_null(stage.{stage_dc_column_list[i]}, lnk.{vault_dg_column_list[i]})"
        group_by_snip += "," + str(i + 5 + len(vault_hk_column_list))

    statement = f"""  /* Link insert statement */
        INSERT INTO {vault_schema}.{vault_table} ({insert_column_section}) 
        SELECT DISTINCT '{meta_inserted_at}'::timestamp,{meta_job_instance_id},stage.MD_RECORD_SOURCE,
                {stage_hk_column_snip},stage.{stage_lk_column} {stage_dg_column_snip}
        FROM {stage_schema}.{stage_table} stage
        LEFT OUTER JOIN  {vault_schema}.{vault_table} lnk ON {join_condition}
        WHERE lnk.{vault_lk_column} is null
        AND stage.{stage_lk_column} != '{HASHKEY_FOR_MISSING_DATA}'
        AND stage.{stage_lk_column} != '{HASHKEY_FOR_DELIVERED_NULL}'
        {group_by_snip}
        """
    statement_list.append(statement)
    return statement_list


def dvf_get_datavault_esat_elt_sql(db_connection, vault_schema, vault_esat_table, vault_lnk_table,
                                   stage_schema, stage_table, stage_lk_column, meta_job_instance_id, meta_inserted_at,
                                   vault_driving_key_column_list=(), stage_driving_key_column_list=(),
                                   with_deletion_detection=False):
    statement_list = []

    vault_column_dict = dvf_get_datavault_column_lists_from_snf(db_connection, vault_schema, vault_esat_table)
    vault_column_list = ['MD_INSERTED_AT', 'MD_RUN_ID', 'MD_RECORD_SOURCE', 'MD_VALID_BEFORE',
                         'MD_IS_DELETED']
    vault_lk_column = ""
    for key_column in vault_column_dict['dv_key_columns']:
        if key_column[:3] == "lk_":
            vault_lk_column = key_column
            vault_column_list.append(vault_lk_column)

    if len(vault_lk_column) == 0:
        raise Dvf_generatorSettingsError("Could not find lk column in ", {vault_schema}, {vault_esat_table})

    insert_column_snip = ','.join(vault_column_list)

    new_data_statement = f""" /* Esat insert statement */
            INSERT INTO {vault_schema}.{vault_esat_table} ({insert_column_snip}) 
            SELECT DISTINCT '{meta_inserted_at}'::timestamp,{meta_job_instance_id},stage.MD_RECORD_SOURCE
                    ,lib.dwh_far_future_date(),false,stage.{vault_lk_column}
            FROM {stage_schema}.{stage_table} stage
            LEFT OUTER JOIN  {vault_schema}.{vault_esat_table} esat
                            ON esat.{vault_lk_column} = stage.{stage_lk_column}
                            AND  esat.MD_VALID_BEFORE = lib.dwh_far_future_date()
                            AND not esat.MD_IS_DELETED
            WHERE esat.{vault_lk_column} is null
            """

    statement_list.append(new_data_statement)

    if len(vault_driving_key_column_list) > 0:
        if len(vault_driving_key_column_list) != len(stage_driving_key_column_list):
            raise Dvf_generatorSettingsError("driving key lists have different element count", vault_driving_key_column_list,
                                             stage_driving_key_column_list)

        driving_key_join_snip = ""
        snip_and_clause = ""
        for i in range(len(vault_driving_key_column_list)):
            driving_key_join_snip += snip_and_clause
            driving_key_join_snip += \
                f" lnk.{vault_driving_key_column_list[i]} = stage.{stage_driving_key_column_list[i]}"
            snip_and_clause = " AND "

        driving_key_enddate_statement = f""" /* driving key enddate statement */
                INSERT INTO {vault_schema}.{vault_esat_table} ({insert_column_snip}) 
                SELECT DISTINCT '{meta_inserted_at}'::timestamp,{meta_job_instance_id},stage.MD_RECORD_SOURCE
                        ,lib.dwh_far_future_date(),true, esat.{vault_lk_column}
                FROM {stage_schema}.{stage_table} stage
                JOIN {vault_schema}.{vault_lnk_table} lnk
                  ON {driving_key_join_snip}
                JOIN {vault_schema}.{vault_esat_table} esat
                  ON esat.{vault_lk_column} = lnk.{vault_lk_column}  
                WHERE  esat.MD_VALID_BEFORE = lib.dwh_far_future_date()
                 AND   esat.{vault_lk_column} <> stage.{stage_lk_column}
                 AND NOT esat.MD_IS_DELETED
                """
        statement_list.append(driving_key_enddate_statement)

    if with_deletion_detection:
        deletion_statement = f""" /* Deletion detection on full stage data */
                INSERT INTO {vault_schema}.{vault_esat_table} ({insert_column_snip}) 
                SELECT DISTINCT '{meta_inserted_at}'::timestamp,{meta_job_instance_id},'deletion_detection'
                        ,lib.dwh_far_future_date(),true,esat.{vault_lk_column} 
                FROM {vault_schema}.{vault_esat_table} esat
                LEFT OUTER JOIN {vault_schema}.{vault_esat_table} deleted_esat
                    ON esat.{vault_lk_column} = deleted_esat.{vault_lk_column}
                        AND deleted_esat.MD_VALID_BEFORE = lib.dwh_far_future_date()
                        AND deleted_esat.MD_IS_DELETED
                LEFT OUTER JOIN  {stage_schema}.{stage_table} stage
                      ON stage.{stage_lk_column} = esat.{vault_lk_column} 
                WHERE esat.MD_VALID_BEFORE = lib.dwh_far_future_date()
                AND   not esat.MD_IS_DELETED 
                AND   deleted_esat.{vault_lk_column} IS NULL
                AND   esat.MD_RECORD_SOURCE != 'SYSTEM'
                AND   stage.{stage_lk_column} is NULL
                """
        statement_list.append(deletion_statement)

    update_timeline_statement = f"""  /* Enddating of outdated rows */
            UPDATE {vault_schema}.{vault_esat_table} prevdata
            SET MD_VALID_BEFORE = newdata.MD_INSERTED_AT
            FROM ( SELECT DISTINCT {vault_lk_column}, md_run_id,md_inserted_at
                   FROM {vault_schema}.{vault_esat_table}
                   WHERE md_run_id = {meta_job_instance_id} ) newdata
            WHERE newdata.{vault_lk_column} = prevdata.{vault_lk_column}
              AND prevdata.md_run_id <> {meta_job_instance_id}
              AND prevdata.md_valid_before=lib.dwh_far_future_date()
            """

    statement_list.append(update_timeline_statement)

    return statement_list


def dvf_get_datavault_sat_elt_sql(db_connection, vault_schema, vault_table, stage_schema, stage_table, stage_hk_column,
                                  stage_rh_column, stage_content_column_list, meta_job_instance_id, meta_inserted_at, with_deletion_detection=False,
                                  parent_schema=None, parent_table=None, parent_partition_column_list=None,
                                  stage_partition_column_list=None, distinct_rows=True):
    """generate all statements to load data from stage to a sat. Relies on naming data model conventions"""
    # currently we just use the msat logic. which ist not optimal but delivers the same result
    statement_list = []

    vault_column_dict = dvf_get_datavault_column_lists_from_snf(db_connection, vault_schema, vault_table)
    vault_column_list = ['MD_INSERTED_AT', 'MD_RUN_ID', 'MD_RECORD_SOURCE', 'MD_VALID_BEFORE',
                         'MD_IS_DELETED']
    vault_column_list.extend(vault_column_dict['dv_key_columns'])
    vault_column_list.extend(vault_column_dict['dv_compare_columns'])
    vault_column_list.extend(vault_column_dict['content_columns'])

    vault_hk_column = vault_column_dict['dv_key_columns'][0]  # there can be only one key column in a sat
    vault_rh_column = vault_column_dict['dv_compare_columns'][0]  # there can be only one row hash in a sat

    insert_column_snip = ','.join(vault_column_list)
    #content_column_snip = 'stage.' + ',stage.'.join(vault_column_dict['content_columns'])
    content_column_snip = 'stage.' + ',stage.'.join(stage_content_column_list)

    if distinct_rows:
        distinct_rule = "DISTINCT"
    else:
        distinct_rule = " "

    statement = f"""  /* Satellite insert for new and changed data */
            INSERT INTO {vault_schema}.{vault_table} ({insert_column_snip}) 
            SELECT {distinct_rule} '{meta_inserted_at}'::timestamp,{meta_job_instance_id},stage.MD_RECORD_SOURCE
                    ,lib.dwh_far_future_date(),false,
                    stage.{stage_hk_column},stage.{stage_rh_column}, {content_column_snip}
            FROM {stage_schema}.{stage_table} stage
            LEFT OUTER JOIN  {vault_schema}.{vault_table} sat
                            ON sat.{vault_hk_column} = stage.{stage_hk_column}
                            AND  sat.MD_VALID_BEFORE = lib.dwh_far_future_date()
                            AND not sat.MD_IS_DELETED
            WHERE sat.{vault_hk_column} is null
               OR sat.{vault_rh_column} != stage.{stage_rh_column} 
            """

    statement_list.append(statement)

    if with_deletion_detection:
        null_column_snip = ""
        for i in range(len(vault_column_dict['content_columns'])):
            null_column_snip += " , NULL"

        if parent_partition_column_list is not None:
            parent_part_column_snip = 'parent.' + ',parent.'.join(parent_partition_column_list)
            stage_part_column_snip = 'stage.' + ',stage.'.join(stage_partition_column_list)
            deletion_insert_statement = f""" /* Partition deletion detection on {stage_part_column_snip} */
                    INSERT INTO {vault_schema}.{vault_table} ({insert_column_snip})
                    with all_current_objects_of_partition as (
                        select {vault_hk_column} 
                        from {parent_schema}.{parent_table} parent
                        where ({parent_part_column_snip}) in (SELECT DISTINCT {stage_part_column_snip}
                        from {stage_schema}.{stage_table} stage)
                        )
                    SELECT '{meta_inserted_at}'::timestamp,{meta_job_instance_id},'deletion_detection',
                            lib.dwh_far_future_date(),true,
                            sat.{stage_hk_column},'{HASHKEY_FOR_MISSING_DATA}' {null_column_snip}
                    FROM  {vault_schema}.{vault_table} sat
                    JOIN all_current_objects_of_partition acoop ON acoop.{vault_hk_column}=sat.{vault_hk_column}
                    LEFT OUTER JOIN {stage_schema}.{stage_table} stage
                                ON sat.{vault_hk_column} = stage.{stage_hk_column}
                    WHERE stage.{stage_hk_column} is null
                    AND sat.MD_VALID_BEFORE = lib.dwh_far_future_date()
                    AND not sat.MD_IS_DELETED
                    AND sat.md_record_source != 'SYSTEM'
                 """
        else:
            deletion_insert_statement = f"""  /* Deletion detection on full stage data */
                    INSERT INTO {vault_schema}.{vault_table} ({insert_column_snip}) 
                    SELECT '{meta_inserted_at}'::timestamp,{meta_job_instance_id},'deletion_detection',
                            lib.dwh_far_future_date(),true,
                            sat.{stage_hk_column},'{HASHKEY_FOR_MISSING_DATA}' {null_column_snip}
                    FROM  {vault_schema}.{vault_table} sat
                    LEFT OUTER JOIN {stage_schema}.{stage_table} stage
                                ON sat.{vault_hk_column} = stage.{stage_hk_column}
                    WHERE stage.{stage_hk_column} is null
                    AND sat.MD_VALID_BEFORE = lib.dwh_far_future_date()
                    AND not sat.MD_IS_DELETED
                    AND sat.md_record_source != 'SYSTEM'
                        """

        statement_list.append(deletion_insert_statement)

    update_timeline_statement = f"""   /* Enddating of outdated rows */
            UPDATE {vault_schema}.{vault_table} prevdata
            SET MD_VALID_BEFORE = newdata.MD_INSERTED_AT
            FROM ( SELECT DISTINCT {vault_hk_column}, md_run_id,md_inserted_at
                   FROM {vault_schema}.{vault_table}
                   WHERE md_run_id = {meta_job_instance_id} ) newdata
            WHERE newdata.{vault_hk_column} = prevdata.{vault_hk_column}
              AND prevdata.md_run_id <> {meta_job_instance_id}
              AND prevdata.md_valid_before=lib.dwh_far_future_date()
            """

    statement_list.append(update_timeline_statement)

    return statement_list


def dvf_get_datavault_msat_elt_sql(db_connection, vault_schema, vault_table, stage_schema, stage_table, stage_hk_column,
                                   stage_gh_column, stage_content_column_list, meta_job_instance_id, meta_inserted_at, with_deletion_detection=False,
                                   parent_schema=None, parent_table=None, parent_partition_column_list=None,
                                   stage_partition_column_list=None):
    """generate all statements to load data from stage to a msat. Relies on naming data model conventions.
    colums must always be listet in alphebetical order"""

    statement_list = []

    vault_column_dict = dvf_get_datavault_column_lists_from_snf(db_connection, vault_schema, vault_table)
    vault_column_list = ['MD_INSERTED_AT', 'MD_RUN_ID', 'MD_RECORD_SOURCE', 'MD_VALID_BEFORE',
                         'MD_IS_DELETED']
    vault_column_list.extend(vault_column_dict['dv_key_columns'])
    vault_column_list.extend(vault_column_dict['dv_compare_columns'])
    vault_column_list.extend(vault_column_dict['content_columns'])

    vault_hk_column = vault_column_dict['dv_key_columns'][0]  # there can be only one key column in a sat
    vault_gh_column = vault_column_dict['dv_compare_columns'][0]  # there can be only one group hash in a sat

    insert_column_snip = ','.join(vault_column_list)
    #content_column_snip = ','.join(vault_column_dict['content_columns'])
    content_column_snip = ','.join(stage_content_column_list)

    statement = f""" /* Multiactive Satellite insert */
        INSERT INTO {vault_schema}.{vault_table} ({insert_column_snip}) 
        SELECT
            '{meta_inserted_at}'::timestamp,
            {meta_job_instance_id},
            stage.MD_RECORD_SOURCE,
            '{FAR_FUTURE_DATE_SQL_STRING}',
            false,
            stage.{stage_hk_column},stage.{stage_gh_column}, {content_column_snip}
        FROM {stage_schema}.{stage_table} stage
        LEFT OUTER JOIN (SELECT {vault_hk_column}, {vault_gh_column} 
                        FROM {vault_schema}.{vault_table} msat 
                        WHERE  msat.MD_VALID_BEFORE = lib.dwh_far_future_date()
                               AND not msat.MD_IS_DELETED
                        GROUP BY {vault_hk_column}, {vault_gh_column}
                        ) msat
                        ON msat.{vault_hk_column} = stage.{stage_hk_column}
        WHERE msat.{vault_hk_column} is null
           OR msat.{vault_gh_column} != stage.{stage_gh_column} 
        """

    statement_list.append(statement)

    if with_deletion_detection:
        null_column_snip = ""
        for i in range(len(vault_column_dict['content_columns'])):
            null_column_snip += " , NULL"

        if parent_partition_column_list is not None:
            parent_part_column_snip = 'parent.' + ',parent.'.join(parent_partition_column_list)
            stage_part_column_snip = 'stage.' + ',stage.'.join(stage_partition_column_list)
            deletion_insert_statement = f""" /* Partition deletion detection on {stage_part_column_snip} */
                           INSERT INTO {vault_schema}.{vault_table} ({insert_column_snip})
                           with all_objects_of_partition as (
                               select distinct {vault_hk_column} 
                               from {parent_schema}.{parent_table} parent
                               where ({parent_part_column_snip}) in (SELECT DISTINCT {stage_part_column_snip}
                               from {stage_schema}.{stage_table} stage)
                               )
                           SELECT '{meta_inserted_at}'::timestamp,{meta_job_instance_id},'deletion_detection',
                                   lib.dwh_far_future_date(),true,
                                   msat.{stage_hk_column},'{HASHKEY_FOR_MISSING_DATA}' {null_column_snip}
                           FROM {stage_schema}.{stage_table} stage
                           RIGHT OUTER JOIN (SELECT msat.{vault_hk_column}
                                FROM {vault_schema}.{vault_table} msat 
                                join all_objects_of_partition aoop on aoop.{vault_hk_column}=msat.{vault_hk_column}
                                WHERE  msat.MD_VALID_BEFORE = lib.dwh_far_future_date()
                                       AND not msat.MD_IS_DELETED
                                       AND md_record_source != 'SYSTEM'
                                GROUP BY msat.{vault_hk_column}, msat.{vault_gh_column}
                                ) msat
                                ON msat.{vault_hk_column} = stage.{stage_hk_column}
                           WHERE stage.{stage_hk_column} is null"""
        else:
            deletion_insert_statement = f""" /* Full data deletion detection */
                INSERT INTO {vault_schema}.{vault_table} ({insert_column_snip}) 
                SELECT
                    '{meta_inserted_at}'::timestamp,
                    {meta_job_instance_id},
                    'deletion_detection',
                    '{FAR_FUTURE_DATE_SQL_STRING}',
                    true,
                    msat.{stage_hk_column},
                    '{HASHKEY_FOR_MISSING_DATA}' {null_column_snip}
                FROM {stage_schema}.{stage_table} stage
                RIGHT OUTER JOIN (SELECT {vault_hk_column} 
                                FROM {vault_schema}.{vault_table} msat 
                                WHERE  msat.MD_VALID_BEFORE = lib.dwh_far_future_date()
                                       AND not msat.MD_IS_DELETED
                                       AND md_record_source != 'SYSTEM'
                                GROUP BY {vault_hk_column}, {vault_gh_column}
                                ) msat
                                ON msat.{vault_hk_column} = stage.{stage_hk_column}
                WHERE stage.{stage_hk_column} is null"""

        statement_list.append(deletion_insert_statement)

    update_timeline_statement = f""" /* Enddating of outdated rows */
        UPDATE {vault_schema}.{vault_table} prevdata
        SET MD_VALID_BEFORE = newdata.MD_INSERTED_AT
        FROM ( SELECT DISTINCT {vault_hk_column}, md_run_id,md_inserted_at
               FROM {vault_schema}.{vault_table}
               WHERE md_run_id = {meta_job_instance_id} ) newdata
        WHERE newdata.{vault_hk_column} = prevdata.{vault_hk_column}
          AND prevdata.md_run_id <> {meta_job_instance_id}
          AND prevdata.md_valid_before=lib.dwh_far_future_date()
        """

    statement_list.append(update_timeline_statement)

    return statement_list


def dvf_execute_elt_statement_list(db_cursor, statement_list, only_print=False, trace_sql=False):
    """Executes a list of statements"""
    params = configuration_read('dvf', 'sqlgenerator')
    if (params['trace_sql'] == 'true'):
        trace_sql=True
    for statement in statement_list:
        try:
            if trace_sql or only_print:
                print('>>>>'+40*'--')
                print(statement)
                print('<<<<'+40 * '--')
            if not only_print:
                db_cursor.execute(statement)
            print('Affected rows: '+ str(db_cursor.rowcount))
        except Exception as e:
            print(statement)
            raise




def dvf_get_check_hash_collision_hub_elt_sql(db_connection, vault_schema, vault_table, stage_schema, stage_table, stage_hk_column,
                                             stage_bk_column_list):
    """generate statements to check hash collision for hub. Relies on naming data model conventions.
    columns must always be listed in alphabetical order"""

    statement_list = []

    vault_column_dict = dvf_get_datavault_column_lists_from_snf(db_connection, vault_schema, vault_table)

    vault_bk_column_list = vault_column_dict['content_columns']
    vault_hk_column = vault_column_dict['dv_key_columns'][0]    # there can be only one key column in the hub

    stage_hk_column = stage_hk_column.lower()
    stage_bk_column_snip = "stage." + ",stage.".join(stage_bk_column_list)

    bk_unequal_condition = ' OR '.join([f"not equal_null(stage.{stage_col}, hub.{hub_col})" for stage_col, hub_col in zip(stage_bk_column_list, vault_bk_column_list)])


    if len(stage_bk_column_list) != len(vault_bk_column_list):
        raise Dvf_generatorSettingsError("bk colums list of stage does not have same number of elements like hub table ["+
                                         "|".join(stage_bk_column_list)+ "] <> ["+ " |".join(vault_bk_column_list)+"]")

    statement_check_1 = f""" WITH hk_bk_combinations  AS ( /* collision check 1 - Stage internal collision */
          SELECT {stage_hk_column}, count(distinct {stage_bk_column_snip}) bk_count
          FROM {stage_schema}.{stage_table} stage
          GROUP BY 1
        )
        SELECT to_number('------>>>>>  DATA VAULT LOAD EXCEPTION when loading "{vault_table}":'
          ||' Hash collision in stage column {stage_hk_column} on hash value >>>'||{stage_hk_column} 
          ||'<<< ---- this conversion error is on purpose to prevent loading')  dummy_case_column 
        FROM hk_bk_combinations 
        WHERE bk_count>1
        """

    statement_list.append(statement_check_1)

    statement_check_2 = f""" WITH hk_collisions as ( /* collision check 2 - collision between stage and hub*/
          SELECT DISTINCT stage.{stage_hk_column}
          FROM {stage_schema}.{stage_table} stage
          JOIN {vault_schema}.{vault_table} hub
          ON stage.{stage_hk_column}= hub.{vault_hk_column}
                AND ({bk_unequal_condition}) 
          WHERE stage.{stage_hk_column} <> '{HASHKEY_FOR_DELIVERED_NULL}'
        )
        SELECT to_number('------>>>>>  DATA VAULT LOAD EXCEPTION when loading "{vault_table}":'
         ||' hash collision between stage column "{stage_hk_column}" and hub column "{vault_hk_column}"'
         ||' on hash value(s) >>>'
         ||{stage_hk_column}
         ||'<<< ---- this conversion error is on purpose to prevent loading')  dummy_case_column
         FROM hk_collisions
        """
    statement_list.append(statement_check_2)

    return statement_list

def dvf_get_check_hash_collision_lnk_elt_sql(db_connection, vault_schema, vault_table, stage_schema, stage_table, stage_lk_column, stage_hk_column_list):
    return dvf_get_check_hash_collision_dlnk_elt_sql(db_connection, vault_schema, vault_table, stage_schema, stage_table, stage_lk_column,
                                                     stage_hk_column_list, [])

def dvf_get_check_hash_collision_dlnk_elt_sql(db_connection, vault_schema, vault_table, stage_schema, stage_table, stage_lk_column,
                                              stage_hk_column_list, stage_dc_column_list):
    """generate statements to check hash collision for link. Relies on naming data model conventions.
    columns must always be listed in alphabetical order"""

    statement_list = []

    vault_column_dict = dvf_get_datavault_column_lists_from_snf(db_connection, vault_schema, vault_table)

    vault_hk_column_list = []
    vault_lk_column = ""
    for key_column in vault_column_dict['dv_key_columns']:
        if key_column[:3] == "lk_":
            vault_lk_column = key_column
        else:
            vault_hk_column_list.append(key_column)

    if len(vault_hk_column_list) != len(stage_hk_column_list):
        raise Dvf_generatorSettingsError("Hub key column list of stage does not have same number of elements like link table",
                                         stage_hk_column_list, "<>", vault_hk_column_list)

    stage_hk_column_snip = "stage." + ",stage.".join(stage_hk_column_list)

    vault_dg_column_list = vault_column_dict['content_columns']  # content in linktable is a degenerated attribute

    if len(vault_dg_column_list) != len(stage_dc_column_list):
        raise Dvf_generatorSettingsError("degenerated column list of stage does not have same number of elements like link table",
                                         stage_dc_column_list, "<>", vault_dg_column_list)

    stage_dc_column_snip = ""
    if len(vault_dg_column_list) > 0:
        stage_dc_column_snip = ",stage." + ",stage.".join(stage_dc_column_list)
        stage_hk_dc_column_snip = f"{stage_hk_column_snip}{stage_dc_column_snip}"
    else:
        stage_hk_dc_column_snip = stage_hk_column_snip

    hk_dc_unequal_condition = ' OR '.join([f"not equal_null(stage.{stage_col}, lnk.{lnk_col})" for stage_col, lnk_col in zip(stage_hk_column_list + stage_dc_column_list, vault_hk_column_list + vault_dg_column_list)])

    statement_check_1 = f""" WITH lk_hk_dc_combinations  AS ( /* collision check 1 - Stage internal collision */
                      SELECT {stage_lk_column}, count(distinct {stage_hk_dc_column_snip}) hk_dc_count
                      FROM {stage_schema}.{stage_table} stage
                      GROUP BY 1
                    )
                    SELECT to_number('------>>>>>  DATA VAULT LOAD EXCEPTION when loading "{vault_table}":'
                      ||' Hash collision in stage column {stage_lk_column} on hash value >>>'||{stage_lk_column} 
                      ||'<<< ---- this conversion error is on purpose to prevent loading')  dummy_case_column 
                    FROM lk_hk_dc_combinations
                    WHERE hk_dc_count>1
        """

    statement_list.append(statement_check_1)

    statement_check_2 = f""" WITH lk_collisions as ( /* collision check 2 - collision between stage and link*/
                      SELECT DISTINCT stage.{stage_lk_column}
                      FROM {stage_schema}.{stage_table} stage
                      JOIN {vault_schema}.{vault_table} lnk
                      ON stage.{stage_lk_column}= lnk.{vault_lk_column}
                      and ({hk_dc_unequal_condition})
                      and stage.{stage_lk_column} <> '{HASHKEY_FOR_DELIVERED_NULL}'
                    )
                    SELECT to_number('------>>>>>  DATA VAULT LOAD EXCEPTION when loading "{vault_table}":'
                     ||' hash collision between stage column "{stage_lk_column}" and link column "{vault_lk_column}"'
                     ||' on hash value(s) >>>'||{stage_lk_column}
                     ||'<<< ---- this conversion error is on purpose to prevent loading')  dummy_case_column
                     FROM lk_collisions 
        """
    statement_list.append(statement_check_2)

    return statement_list

def dvf_get_check_singularity_sat_elt_sql(vault_table, stage_schema, stage_table, stage_hk_column,
                                  stage_rh_column):
    statement_list = []

    statement = f"""WITH hk_value_variations  AS ( /* satellite stage data singularity check */
                      SELECT {stage_hk_column}, count(distinct {stage_rh_column}) variation_count
                      FROM {stage_schema}.{stage_table} stage
                      GROUP BY 1
                    )
                    SELECT to_number('------>>>>>  DATA VAULT LOAD EXCEPTION when loading "{vault_table}":'
                      ||' Multiple values in stage for the same key of satellite >>>'||{stage_hk_column} 
                      ||'<<< ---- this conversion error is on purpose to prevent loading')  dummy_case_column 
                    FROM hk_value_variations  
                    WHERE variation_count > 1 
                    """

    statement_list.append(statement)
    return statement_list

# -------------------------------------------------------------------------------------------------------------
# internal test and demo
# -------------------------------------------------------------------------------------------------------------
from lib.cimtjobinstance import cimtjobinstance_job, CimtJobInstance


def deploy_test_model():
    from lib.dvf_ddl_deploymentmanager_snf import Dvf_ddl_deploymentmanager_snf

    my_connection = connection_snf_for_dwh_connection_type(Dvf_dwh_connection_type.owner)
    my_deployment_manager = Dvf_ddl_deploymentmanager_snf(my_connection)

    my_deployment_manager.deploy_stage_table("zz_rvlt_demo", "szz_source_demo_aaa_p1")
    my_deployment_manager.deploy_table("zz_rvlt_demo", "aaa_hub")
    my_deployment_manager.deploy_table("zz_rvlt_demo", "aaa_aaa_p1_sat")
    my_deployment_manager.deploy_table("zz_rvlt_demo", "aaa_bbb_lnk")
    my_deployment_manager.deploy_table("zz_rvlt_demo", "aaa_bbb_esat")

    my_deployment_manager.deploy_stage_table("zz_rvlt_demo","szz_source_demo_bbb_p1")
    my_deployment_manager.deploy_table("zz_rvlt_demo","bbb_hub")
    my_deployment_manager.deploy_table("zz_rvlt_demo","bbb_dcl_p1_msat")
    my_deployment_manager.deploy_table("zz_rvlt_demo","bbb_dcl_dlnk")


@cimtjobinstance_job
def test_and_demo_generators(**kwargs):
    deploy_test_model()
    my_job_instance = CimtJobInstance(kwargs['instance_job_name'])
    my_job_instance.start_instance()

    from lib.connection_snf import connection_snf_for_dwh_connection_type

    try:
        dwh_connection = connection_snf_for_dwh_connection_type(Dvf_dwh_connection_type.raw_vault)
        dwh_cursor = dwh_connection.cursor()
        vault_schema = 'zz_rvlt_demo'
        stage_schema = 'stage_rvlt'
        stage_table = 'szz_source_demo_aaa_p1'

        # ----------

        statement_list = dvf_get_datavault_hub_elt_sql(
            vault_table='aaa_hub',
            stage_hk_column='HK_AAA',
            stage_bk_column_list=['AAA_BK1', 'AAA_BK2F'],
            db_connection=dwh_connection,
            vault_schema=vault_schema,
            stage_schema=stage_schema, stage_table=stage_table,
            meta_job_instance_id=my_job_instance.get_job_instance_id(),
            meta_inserted_at=my_job_instance.get_job_started_at()
        )
        dvf_execute_elt_statement_list(dwh_cursor, statement_list,trace_sql=True)

        # ----------

        statement_list = dvf_get_datavault_hub_elt_sql(
           vault_table='bbb_hub',
           stage_hk_column='HK_BBB',
           stage_bk_column_list=['BBB_BK1F'],
           db_connection=dwh_connection,
           vault_schema=vault_schema,
           stage_schema=stage_schema, stage_table=stage_table,
           meta_job_instance_id=my_job_instance.get_job_instance_id(),
           meta_inserted_at=my_job_instance.get_job_started_at()
           )
        dvf_execute_elt_statement_list(dwh_cursor, statement_list,trace_sql=True)

        dwh_connection.commit()

        # ----------

        statement_list = dvf_get_datavault_lnk_elt_sql(
            vault_table='aaa_bbb_lnk',
            stage_lk_column='LK_AAA_BBB',
            stage_hk_column_list=['HK_AAA', 'HK_BBB'],
            db_connection=dwh_connection,
            vault_schema=vault_schema,
            stage_schema=stage_schema, stage_table=stage_table,
            meta_job_instance_id=my_job_instance.get_job_instance_id(),
            meta_inserted_at=my_job_instance.get_job_started_at()
        )
        dvf_execute_elt_statement_list(dwh_cursor, statement_list,trace_sql=True)

        # ----------

        statement_list = dvf_get_datavault_esat_elt_sql(
            vault_esat_table='aaa_bbb_esat',
            vault_lnk_table='aaa_bbb_lnk',
            stage_lk_column='LK_AAA_BBB',
            vault_driving_key_column_list=['HK_AAA'],
            stage_driving_key_column_list=['HK_AAA'],
            with_deletion_detection=True,
            db_connection=dwh_connection,
            vault_schema=vault_schema,
            stage_schema=stage_schema, stage_table=stage_table,
            meta_job_instance_id=my_job_instance.get_job_instance_id(),
            meta_inserted_at=my_job_instance.get_job_started_at()
        )
        dvf_execute_elt_statement_list(dwh_cursor, statement_list,trace_sql=True)

        dwh_connection.commit()

        # ----------

        statement_list = dvf_get_datavault_sat_elt_sql(
           vault_table='aaa_aaa_p1_sat',
           stage_hk_column='HK_AAA',
           stage_rh_column='RH_AAA_AAA_P1_SAT',
           with_deletion_detection=True,
           db_connection=dwh_connection,
           vault_schema=vault_schema,
           stage_schema=stage_schema, stage_table=stage_table,
           meta_job_instance_id=my_job_instance.get_job_instance_id(),
           meta_inserted_at=my_job_instance.get_job_started_at()
           )
        dvf_execute_elt_statement_list(dwh_cursor, statement_list,trace_sql=True)

        vault_schema = 'zz_rvlt_demo'
        stage_schema = 'stage_rvlt'
        stage_table = 'szz_source_demo_bbb_p1'

#
        statement_list = dvf_get_datavault_dlnk_elt_sql(
            vault_table='bbb_dcl_dlnk',
            stage_lk_column='LK_BBB_DCL',
            stage_hk_column_list=['HK_BBB'],
            stage_dc_column_list=['BBB_DCL_DC1F'],
            db_connection = dwh_connection,
            vault_schema = vault_schema,
            stage_schema = stage_schema, stage_table = stage_table,
            meta_job_instance_id = my_job_instance.get_job_instance_id(),
            meta_inserted_at = my_job_instance.get_job_started_at()
            )
        print(statement_list[0])

        statement_list = dvf_get_datavault_msat_elt_sql(
            vault_table='bbb_dcl_p1_msat',
            stage_hk_column='LK_BBB_DCL',
            stage_gh_column='GH_BBB_BBB_P1_MSAT',
            with_deletion_detection=True,
            parent_schema=vault_schema, parent_table='bbb_dcl_dlnk',
            parent_partition_column_list=["BBB_DCL_DC1F"],
            stage_partition_column_list=["BBB_DCL_DC1F"],
            db_connection=dwh_connection,
            vault_schema=vault_schema,
            stage_schema=stage_schema, stage_table=stage_table,
            meta_job_instance_id=my_job_instance.get_job_instance_id(),
            meta_inserted_at=my_job_instance.get_job_started_at()
            )
        for statement in statement_list:
            print(statement)
        dvf_execute_elt_statement_list(dwh_cursor, statement_list,trace_sql=True)


        # for statement in statement_list:
        #   print(statement+";")

        dwh_connection.commit()
    except Exception:
        my_job_instance.end_instance_with_error(1, "Somethin bad happend in the test")
        raise

    my_job_instance.end_instance()

@cimtjobinstance_job
def test_and_demo_column_reader(**kwargs):
    deploy_test_model()
    my_job_instance = CimtJobInstance(kwargs['instance_job_name'])
    my_job_instance.start_instance()

    try:
        dwh_connection = connection_snf_for_dwh_connection_type(Dvf_dwh_connection_type.raw_vault)
        table_name='aaa_aaa_p1_sat'
        column_list=dvf_get_dvpi_like_column_lists_by_convention_from_snf(dwh_connection, "zz_rvlt_demo", table_name)
        print(f"\n\n--- {table_name} ---column_list ------")
        print(json.dumps(column_list, indent=2,sort_keys=True))

        table_name='aaa_hub'
        column_list=dvf_get_dvpi_like_column_lists_by_convention_from_snf(dwh_connection, "zz_rvlt_demo", table_name)
        print(f"\n\n--- {table_name} ---column_list ------")
        print(json.dumps(column_list, indent=2,sort_keys=True))

    except Exception:
        my_job_instance.end_instance_with_error(1, "Something bad happend in the test")
        raise

    my_job_instance.end_instance()



def test_hash_collision():
    dwh_connection = connection_snf_for_dwh_connection_type(Dvf_dwh_connection_type.raw_vault)
    vault_schema = 'zz_rvlt_demo'
    stage_schema = 'stage_rvlt'
    stage_table = 'szz_source_demo_aaa_p1'

    check_hub = dvf_get_check_hash_collision_hub_elt_sql(
        vault_table='aaa_hub',
        stage_hk_column='HK_AAA',
        stage_bk_column_list=['AAA_BK1', 'AAA_BK2F'],
        db_connection=dwh_connection,
        vault_schema=vault_schema,
        stage_schema=stage_schema,
        stage_table=stage_table
    )

    for c in range(len(check_hub)):
        print(check_hub[c])

    check_lnk = dvf_get_check_hash_collision_lnk_elt_sql(
        vault_table='aaa_bbb_lnk',
        stage_lk_column='LK_AAA_BBB',
        stage_hk_column_list=['HK_AAA', 'HK_BBB'],
        db_connection=dwh_connection,
        vault_schema=vault_schema,
        stage_schema=stage_schema,
        stage_table=stage_table
    )

    for i in range(len(check_lnk)):
        print(check_lnk[i])

    check_sat = dvf_get_check_singularity_sat_elt_sql(
        vault_table='aaa_aaa_p1_sat',
        stage_hk_column='HK_AAA',
        stage_rh_column='RH_AAA_AAA_P1_SAT',
        stage_schema=stage_schema,
        stage_table=stage_table
    )

    for i in range(len(check_sat)):
        print(check_sat[i])

    return True




if __name__ == '__main__':
    test_and_demo_generators()
    #test_and_demo_column_reader()
    #test_hash_collision()
