import json

from lib.blobstorage_utils import fetch_excel_source_file_from_blob_container
from lib.cimtjobinstance import CimtJobInstance, cimtjobinstance_job
from lib.connection_snf import  connection_snf_for_dwh_connection_type
from lib.connection_azrblob import connection_azrblob

from lib.dvf_basics import dvf_assemble_datavault_hash, Dvf_dwh_connection_type
from lib.dvf_sqlByConvention_snf import dvf_get_datavault_hub_elt_sql, \
    dvf_get_datavault_msat_elt_sql, dvf_execute_elt_statement_list, dvf_get_check_hash_collision_hub_elt_sql, dvf_get_check_singularity_sat_elt_sql, \
    dvf_get_datavault_dlnk_elt_sql, dvf_get_check_hash_collision_dlnk_elt_sql

from lib.dbutils_snf import get_snf_dict_insert_sql, execute_snf_dict_bulk_insert

@cimtjobinstance_job
def stage_excel_file(parent_job_instance, dwh_connection, file_name, data, **kwargs):

    my_job_instance = CimtJobInstance(kwargs['instance_job_name'], parent_job_instance)
    my_job_instance.set_work_item(file_name)
    my_job_instance.start_instance()

    try:
        # ### FRAMEWORK PHASE: do processing here

        # prepare stage insert operation
        dwh_cursor = dwh_connection.cursor()
        dwh_cursor.execute("TRUNCATE TABLE stage_rvlt.rmud_planzahlen_monatlich_p1_stage")
        insert_statement = get_snf_dict_insert_sql(dwh_connection, "stage_rvlt", "rmud_planzahlen_monatlich_p1_stage")
        stage_data_row = {'md_inserted_at': my_job_instance.get_job_started_at().isoformat(),
                    'md_record_source': 'manuell.planzahlen.monatlich',
                    'md_run_id': my_job_instance.get_job_instance_id(),
                    'md_is_deleted': False
                   }

        # prepare stage data
        source_rows = data

        stage_data_rows = []

        group_hash_diff = dict()
        for source_row in source_rows:
            stage_data_row_temp = dict(stage_data_row)

            stage_data_row_temp['firma'] = source_row.get('Firma')
            stage_data_row_temp['jahr'] = source_row.get('Jahr')
            stage_data_row_temp['monat'] = source_row.get('Monat')
            stage_data_row_temp['planzahl'] = source_row.get('planwert')
            stage_data_row_temp['untergruppe_json'] = source_row.get('untergruppe_json')

            # construct value for group hash diff
            key = '_'.join([stage_data_row_temp['firma'], str(stage_data_row_temp['jahr']), str(stage_data_row_temp['monat'])])
            group_hash_diff.setdefault(key, []).append(stage_data_row_temp['planzahl'])
            group_hash_diff.setdefault(key, []).append(stage_data_row_temp['untergruppe_json'])

            stage_data_rows.append(stage_data_row_temp)
            my_job_instance.count_input(1)

        for k, v in group_hash_diff.items():
            values_list = sorted([str(item) for item in v])
            group_hash_diff[k] = '||'.join(values_list)

        for key, group_hash_diff_item in group_hash_diff.items():
            for stage_row in stage_data_rows:
                ### hashes
                if '_'.join([stage_row['firma'], str(stage_row['jahr']), str(stage_row['monat'])]) == key:
                    stage_row['gh_rmud_rgnr_gesellschaft_monatsplanzahl_p1_l10_msat'] = dvf_assemble_datavault_hash([group_hash_diff_item])
                stage_row['hk_rgnr_gesellschaft'] = dvf_assemble_datavault_hash([stage_row['firma']])
                stage_row['lk_rmud_rgnr_gesellschaft_monatsplanzahl'] = dvf_assemble_datavault_hash([stage_row['jahr'],stage_row['monat'], stage_row['firma']])


        execute_snf_dict_bulk_insert(dwh_cursor, insert_statement, stage_data_rows)
        dwh_connection.commit()

    # ### FRAMEWORK PHASE: Log any exception to instance
    except Exception:
        my_job_instance.end_instance_with_error(1, 'something went wrong')
        raise

    # # ### FRAMEWORK PHASE: End the instance normally
    my_job_instance.end_instance()


@cimtjobinstance_job
def load_data_to_vault(parent_job_instance, dwh_connection, file_name=None, **kwargs):


    # ### FRAMEWORK PHASE: setup your job_instance
    my_job_instance = CimtJobInstance(kwargs['instance_job_name'], parent_job_instance)
    my_job_instance.set_work_item(file_name)
    my_job_instance.start_instance()
    try:

        # ### FRAMEWORK PHASE: do processing here
        # BEGIN LOAD DATA TO VLT PART
        # general constants
        stage_schema = 'stage_rvlt'
        stage_table = 'rmud_planzahlen_monatlich_p1_stage'


        dwh_cursor = dwh_connection.cursor()


        hash_collision_check_statement_list = dvf_get_check_hash_collision_hub_elt_sql(vault_table='rgnr_gesellschaft_hub',
        vault_schema='rvlt_general',
        stage_hk_column='HK_RGNR_GESELLSCHAFT',
        stage_bk_column_list=['FIRMA'],
        db_connection=dwh_connection,
        stage_schema=stage_schema,
        stage_table=stage_table)

        dvf_execute_elt_statement_list(dwh_cursor, hash_collision_check_statement_list)

        statement_list = dvf_get_datavault_hub_elt_sql(vault_table='rgnr_gesellschaft_hub',
        vault_schema='rvlt_general',
        stage_hk_column='HK_RGNR_GESELLSCHAFT',
        stage_bk_column_list=['FIRMA'],
        db_connection=dwh_connection,
        stage_schema=stage_schema,
        stage_table=stage_table,
        meta_job_instance_id = my_job_instance.get_job_instance_id(),
        meta_inserted_at = my_job_instance.get_job_started_at() )

        dvf_execute_elt_statement_list(dwh_cursor, statement_list)

        # ----------


        hash_collision_check_statement_list = dvf_get_check_hash_collision_dlnk_elt_sql(vault_table='rmud_rgnr_gesellschaft_monatsplanzahl_dlnk',
        vault_schema='rvlt_manual_data',
        stage_lk_column='LK_RMUD_RGNR_GESELLSCHAFT_MONATSPLANZAHL',
        stage_hk_column_list=['HK_RGNR_GESELLSCHAFT'],
        stage_dc_column_list=['JAHR', 'MONAT'],
        db_connection=dwh_connection,
        stage_schema=stage_schema,
        stage_table=stage_table)

        dvf_execute_elt_statement_list(dwh_cursor, hash_collision_check_statement_list)

        statement_list = dvf_get_datavault_dlnk_elt_sql(vault_table='rmud_rgnr_gesellschaft_monatsplanzahl_dlnk',
        vault_schema='rvlt_manual_data',
        stage_lk_column='LK_RMUD_RGNR_GESELLSCHAFT_MONATSPLANZAHL',
        stage_hk_column_list=['HK_RGNR_GESELLSCHAFT'],
        stage_dc_column_list=['JAHR', 'MONAT'],
        db_connection=dwh_connection,
        stage_schema=stage_schema,
        stage_table=stage_table,
        meta_job_instance_id = my_job_instance.get_job_instance_id(),
        meta_inserted_at = my_job_instance.get_job_started_at() )

        dvf_execute_elt_statement_list(dwh_cursor, statement_list)

        # ----------


        statement_list = dvf_get_datavault_msat_elt_sql(vault_table='rmud_rgnr_gesellschaft_monatsplanzahl_p1_l10_msat',
        vault_schema='rvlt_manual_data',
        stage_hk_column='LK_RMUD_RGNR_GESELLSCHAFT_MONATSPLANZAHL',
        stage_gh_column='GH_RMUD_RGNR_GESELLSCHAFT_MONATSPLANZAHL_P1_L10_MSAT',
        stage_content_column_list=['PLANZAHL', 'UNTERGRUPPE_JSON'],
        with_deletion_detection=False,
        db_connection=dwh_connection,
        stage_schema=stage_schema,
        stage_table=stage_table,
        meta_job_instance_id = my_job_instance.get_job_instance_id(),
        meta_inserted_at = my_job_instance.get_job_started_at() )

        dvf_execute_elt_statement_list(dwh_cursor, statement_list)

        # ----------


        # END LOAD DATA TO VLT PART

        dwh_connection.commit()
    # ### FRAMEWORK PHASE: Log any exception to instance
    except Exception:
        my_job_instance.end_instance_with_error(1, 'something went wrong')
        raise
    # # ### FRAMEWORK PHASE: End the instance normally
    my_job_instance.end_instance()



@cimtjobinstance_job
def load_vault_1(data,file_name,parent_job_instance=None, **kwargs):

    # ### FRAMEWORK PHASE: setup your job_instance
    my_job_instance = CimtJobInstance(kwargs['instance_job_name'], parent_job_instance)
    my_job_instance.set_work_item(file_name)
    my_job_instance.start_instance()

    try:
        # ### FRAMEWORK PHASE: do processing here
        dwh_connection = connection_snf_for_dwh_connection_type(Dvf_dwh_connection_type.raw_vault)
        stage_excel_file(my_job_instance, dwh_connection, file_name, data)
        load_data_to_vault(my_job_instance, dwh_connection, file_name)
        dwh_connection.close()

    # ### FRAMEWORK PHASE: Log any exception to instance
    except Exception:
        my_job_instance.end_instance_with_error(1, 'some processing failed')
        raise

    # # ### FRAMEWORK PHASE: End the instance normally
    my_job_instance.end_instance()


if __name__ == '__main__':
    # local execution
    load_vault_1()