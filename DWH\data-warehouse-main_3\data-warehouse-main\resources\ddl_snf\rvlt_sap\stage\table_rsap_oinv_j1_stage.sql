-- generated script for stage_rvlt.rsap_oinv_j1_stage

-- DROP TABLE stage_rvlt.rsap_oinv_j1_stage;

CREATE TABLE stage_rvlt.rsap_oinv_j1_stage (
--metadata,
MD_INSERTED_AT TIMESTAMP NOT NULL,
MD_<PERSON>UN_ID INT NOT NULL,
MD_RECORD_SOURCE VARCHAR(255) NOT NULL,
MD_IS_DELETED BOOLEAN NOT NULL,
--hash keys,
HK_RSAP_BUSINESS_PARTNER_OCRD CHAR(28) NOT NULL,
HK_RSAP_INVOICE_OINV CHAR(28) NOT NULL,
HK_RSAP_SALES_EMPLOYEE_OSLP CHAR(28) NOT NULL,
LK_RSAP_INVOICE_OINV_BUSINESS_PARTNER_OCRD_SALES_EMPLOYEE_OSLP CHAR(28) NOT NULL,
RH_RSAP_INVOICE_OINV_J1_L10_SAT CHAR(28) NOT NULL,
--business keys,
CARDCODE VARCHAR(20) NULL,
COMPANY Varchar(50) NULL,
<PERSON><PERSON><PERSON>TRY INTEGER NULL,
SLPCODE VARCHAR(20) NULL,
--content,
JSON_TEXT VARCHAR NULL
);
-- end of script --