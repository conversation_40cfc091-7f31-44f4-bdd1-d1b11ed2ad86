{"dvpd_version": "0.6.2", "stage_properties": [{"stage_schema": "stage_rvlt", "stage_table_name": "rsap_hld1_j1_stage"}], "pipeline_name": "rsap_hld1_j1", "record_source_name_expression": "sap.hld1", "data_extraction": {"fetch_module_name": "none - ddl generation only"}, "fields": [{"field_name": "company", "field_type": "<PERSON><PERSON><PERSON><PERSON>(50)", "targets": [{"table_name": "rsap_feiertage_hld1_hub"}]}, {"field_name": "hldCode", "field_type": "VARCHAR(20)", "targets": [{"table_name": "rsap_feiertage_hld1_hub"}]}, {"field_name": "json_text", "field_type": "VARCHAR", "targets": [{"table_name": "rsap_feiertage_hld1_j1_l10_msat", "exclude_json_paths_from_change_detection": ["UpdateDate"]}]}], "data_vault_model": [{"schema_name": "rvlt_sap", "tables": [{"table_name": "rsap_feiertage_hld1_hub", "table_stereotype": "hub", "hub_key_column_name": "HK_rsap_feiertage_hld1"}, {"table_name": "rsap_feiertage_hld1_j1_l10_msat", "table_stereotype": "sat", "satellite_parent_table": "rsap_feiertage_hld1_hub", "is_multiactive": "true", "diff_hash_column_name": "GH_rsap_feiertage_hld1_j1_l10_msat"}]}], "deletion_detection_rules": [{"procedure": "stage_comparison", "tables_to_cleanup": ["rsap_feiertage_hld1_j1_l10_msat"]}]}