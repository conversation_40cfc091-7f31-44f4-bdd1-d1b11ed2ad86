{"dvpd_version": "0.6.2", "stage_properties": [{"stage_schema": "stage_rvlt", "stage_table_name": "rmud_planzahlen_monatlich_p1_stage"}], "pipeline_name": "rmud_planzahlen_monatlich_p1", "record_source_name_expression": "manuell.planzahlen.monatlich", "data_extraction": {"fetch_module_name": "none - ddl generation only"}, "fields": [{"field_name": "jahr", "field_type": "NUMBER(20,0)", "targets": [{"table_name": "rmud_rgnr_gesellschaft_monatsplanzahl_dlnk"}]}, {"field_name": "monat", "field_type": "NUMBER(20,0)", "targets": [{"table_name": "rmud_rgnr_gesellschaft_monatsplanzahl_dlnk"}]}, {"field_name": "firma", "field_type": "<PERSON><PERSON><PERSON><PERSON>(50)", "targets": [{"table_name": "rgnr_gesellschaft_hub", "column_name": "company"}]}, {"field_name": "<PERSON><PERSON><PERSON>", "field_type": "NUMBER(20,2)", "targets": [{"table_name": "rmud_rgnr_gesellschaft_monatsplanzahl_p1_l10_msat"}]}, {"field_name": "untergruppe_json", "field_type": "VARCHAR", "targets": [{"table_name": "rmud_rgnr_gesellschaft_monatsplanzahl_p1_l10_msat"}]}], "data_vault_model": [{"schema_name": "rvlt_general", "tables": [{"table_name": "rgnr_gesellschaft_hub", "table_stereotype": "hub", "hub_key_column_name": "HK_RGNR_GESELLSCHAFT"}]}, {"schema_name": "rvlt_manual_data", "tables": [{"table_name": "rmud_rgnr_gesellschaft_monatsplanzahl_dlnk", "table_stereotype": "lnk", "link_parent_tables": ["rgnr_gesellschaft_hub"]}, {"table_name": "rmud_rgnr_gesellschaft_monatsplanzahl_p1_l10_msat", "table_stereotype": "sat", "is_multiactive": "true", "satellite_parent_table": "rmud_rgnr_gesellschaft_monatsplanzahl_dlnk", "diff_hash_column_name": "GH_RMUD_RGNR_GESELLSCHAFT_MONATSPLANZAHL_P1_L10_MSAT"}]}]}