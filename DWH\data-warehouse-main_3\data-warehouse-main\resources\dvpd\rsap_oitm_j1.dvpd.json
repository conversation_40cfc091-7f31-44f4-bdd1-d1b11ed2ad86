{"dvpd_version": "0.6.2", "stage_properties": [{"stage_schema": "stage_rvlt", "stage_table_name": "rsap_oitm_j1_stage"}], "pipeline_name": "rsap_oitm_j1", "record_source_name_expression": "sap.oitm", "data_extraction": {"fetch_module_name": "none - ddl generation only"}, "fields": [{"field_name": "company", "field_type": "<PERSON><PERSON><PERSON><PERSON>(50)", "targets": [{"table_name": "rsap_item_oitm_hub"}]}, {"field_name": "itemcode", "field_type": "VARCHAR(50)", "targets": [{"table_name": "rsap_item_oitm_hub"}]}, {"field_name": "json_text", "field_type": "VARCHAR", "targets": [{"table_name": "rsap_item_oitm_j1_l10_sat", "exclude_json_paths_from_change_detection": ["UpdateDate"]}]}], "data_vault_model": [{"schema_name": "rvlt_sap", "tables": [{"table_name": "rsap_item_oitm_hub", "table_stereotype": "hub", "hub_key_column_name": "HK_rsap_item_oitm"}, {"table_name": "rsap_item_oitm_j1_l10_sat", "table_stereotype": "sat", "satellite_parent_table": "rsap_item_oitm_hub", "diff_hash_column_name": "RH_rsap_item_oitm_j1_l10_sat"}]}]}