{"dvpd_version": "0.6.2", "stage_properties": [{"stage_schema": "stage_bvlt"}], "pipeline_name": "bgnr_kalender_p1", "record_source_name_expression": "bgnr_kalender_p1", "data_extraction": {"fetch_module_name": "none - ddl generation only"}, "fields": [{"field_name": "company", "field_type": "<PERSON><PERSON><PERSON><PERSON>(50)", "targets": [{"table_name": "rgnr_gesellschaft_hub"}]}, {"field_name": "tagesdatum", "field_type": "DATE", "targets": [{"table_name": "rgnr_gesellschaft_kalender_dlnk"}]}, {"field_name": "ist_werktag", "field_type": "BOOLEAN", "targets": [{"table_name": "bgnr_gesellschaft_kalender_p1_b10_sat"}]}, {"field_name": "rmrks", "field_type": "<PERSON><PERSON><PERSON>(2000)", "targets": [{"table_name": "bgnr_gesellschaft_kalender_p1_b10_sat", "column_name": "bemer<PERSON>ng"}]}], "data_vault_model": [{"schema_name": "rvlt_general", "tables": [{"table_name": "rgnr_gesellschaft_hub", "table_stereotype": "hub", "hub_key_column_name": "HK_rgnr_gesellschaft"}, {"table_name": "rgnr_gesellschaft_kalender_dlnk", "table_stereotype": "lnk", "link_key_column_name": "LK_rgnr_gesellschaft_kalender", "link_parent_tables": ["rgnr_gesellschaft_hub"]}]}, {"schema_name": "bvlt_general", "tables": [{"table_name": "bgnr_gesellschaft_kalender_p1_b10_sat", "table_stereotype": "sat", "satellite_parent_table": "rgnr_gesellschaft_kalender_dlnk", "diff_hash_column_name": "rh_bgnr_gesellschaft_kalender_p1_b10_sat"}]}]}