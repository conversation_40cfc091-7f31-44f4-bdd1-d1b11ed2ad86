"""

__main__.py

"""
from lib.cimtjobinstance import CimtJobInstance, cimtjobinstance_job
from lib.dvf_basics import Dvf_dwh_connection_type
from lib.dvf_ddl_deploymentmanager_snf import Dvf_ddl_deploymentmanager_snf
from lib.connection_snf import  connection_snf_for_dwh_connection_type
from lib.blobstorage_utils import get_source_files_from_blob_storage, move_processed_file_to_processed_container
from lib.connection_azrblob import connection_azrblob
from datetime import datetime

from load_vault_1 import load_vault_1
from fetch_planzahlen_monatlich import  fetch_planzahlen_monatlich


def deploy_datamodel(my_job_instance):
    run_id=my_job_instance.get_job_instance_id()
    insert_date=my_job_instance.get_job_started_at()
    deployment_manager = Dvf_ddl_deploymentmanager_snf(connection_snf_for_dwh_connection_type(Dvf_dwh_connection_type.owner),run_id,insert_date)
    deployment_manager.deploy_stage_table("rvlt_manual_data", "rmud_planzahlen_monatlich_p1_stage")
    deployment_manager.deploy_table("rvlt_general", "rgnr_gesellschaft_hub")
    deployment_manager.deploy_table("rvlt_manual_data", "rmud_rgnr_gesellschaft_monatsplanzahl_dlnk")
    deployment_manager.deploy_table("rvlt_manual_data", "rmud_rgnr_gesellschaft_monatsplanzahl_p1_l10_msat")
    deployment_manager.deploy_view("rvlt_manual_data", "rmud_rgnr_gesellschaft_monatsplanzahl_p1_l20_msat")



@cimtjobinstance_job
def main(**kwargs):
    # ### FRAMEWORK PHASE: setup job_instance for main module
    my_job_instance = CimtJobInstance(kwargs['instance_job_name'])
    my_job_instance.start_instance()

    my_previous_job_instance = CimtJobInstance(kwargs['instance_job_name'], my_job_instance)
    default_previous_timerange = datetime.strptime('1990-01-01 00:00:00', "%Y-%m-%d %H:%M:%S")
    if my_previous_job_instance.load_previous_instance():  #if previous load exists and was successful
        previous_timerange = my_previous_job_instance.get_time_range_end() or default_previous_timerange #if timerange not set before
    else:
        previous_timerange = default_previous_timerange

    sap_source_object = "_Planzahlen"
    container_name = "manualplandata"
    blob_service_client = connection_azrblob('blob_storage')

    try:        # ### FRAMEWORK PHASE: do processing here
        deploy_datamodel(my_job_instance)
        source_rows, processed_files, highest_last_modified = fetch_planzahlen_monatlich(blob_service_client, container_name, sap_source_object)
        processed_files_str = ','.join(processed_files) # join processed files names to str to set work_item in job instance

        # check if last_modified ts of current file is bigger than the previous run, if yes - load the data
        if highest_last_modified.replace(tzinfo=None) > previous_timerange:    #highest_last_modified to tz naive, as previous_timerange from job instance is also tz naive
            load_vault_1(parent_job_instance=my_job_instance, file_name=processed_files_str, data=source_rows)

        blob_service_client.close()
    # # ### FRAMEWORK PHASE: End with bad or good result
    except Exception as e:
        my_job_instance.end_instance_with_error(1, 'some processing failed')
        raise e
    my_job_instance.set_work_item(processed_files_str)
    my_job_instance.set_time_range(time_range_start=previous_timerange, time_range_end=highest_last_modified)
    my_job_instance.end_instance()


if __name__ == '__main__':
    main()
