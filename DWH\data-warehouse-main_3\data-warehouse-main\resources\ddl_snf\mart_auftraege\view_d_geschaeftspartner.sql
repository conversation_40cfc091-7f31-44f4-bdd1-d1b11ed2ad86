
/* select * from  mart_auftraege.d_geschaeftspartner;
   
 */

CREATE OR replace VIEW mart_auftraege.d_geschaeftspartner AS 


-- Final dataset with last lookups, in row calculations and target column naming
select  hk_bgnr_geschaeftspartner dk_geschaeftspartner
		,* exclude(md_inserted_at,md_record_source,md_run_id,md_valid_before,hk_bgnr_geschaeftspartner,md_is_deleted ,rh_bgnr_geschaeftspartner_p1_b10_sat)
from bvlt_general.bgnr_geschaeftspartner_p1_b10_sat sat
where sat.md_valid_before =lib.dwh_far_future_date()
	and not sat.md_is_deleted 
	and md_record_source <> 'SYSTEM';
		
<PERSON> select on VIEW mart_auftraege.d_geschaeftspartner to role DV_D_ACCESS_MART_READ;