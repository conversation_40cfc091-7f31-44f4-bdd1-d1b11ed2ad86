/*to execute this, you must be a <PERSON>UR<PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON><PERSON>*/

use role SECURITYADMIN;
create role DV_PROJECT_DATA_VAULT;
grant create role on account to DV_PROJECT_DATA_VAULT;

create user DV_D_TUSER_LOADER
password = 'YOUR_PASSWORD';

create user DV_D_TUSER_READER
password = 'YOUR_PASSWORD';

create user DV_D_TUSER_TABLEAU
password = 'YOUR_PASSWORD';

grant role DV_PROJECT_DATA_VAULT TO USER <YOUR_USER>;

-----------ROLE CREATION----------------
use role DV_PROJECT_DATA_VAULT;

create role DV_D_OWNER_DATABASE;

create role DV_D_ACCESS_WRITE;

create role DV_D_ACCESS_VAULT_READ;

create role DV_D_ACCESS_MART_READ;

create role DV_D_USERROLE_LOAD_PROCESS;
grant role DV_D_ACCESS_WRITE to role DV_D_USERROLE_LOAD_PROCESS;
grant role DV_D_OWNER_DATABASE to role DV_D_USERROLE_LOAD_PROCESS;



create role DV_D_USERROLE_DEVELOPER;
grant role DV_D_OWNER_DATABASE to role DV_D_USERROLE_DEVELOPER;
grant role DV_D_ACCESS_WRITE to role DV_D_USERROLE_DEVELOPER;
grant role DV_D_ACCESS_VAULT_READ to role DV_D_USERROLE_DEVELOPER;
grant role DV_D_ACCESS_MART_READ to role DV_D_USERROLE_DEVELOPER;


create role DV_D_USERROLE_MART;
grant role DV_D_ACCESS_MART_READ to role DV_D_USERROLE_MART;

create role DV_D_USERROLE_FULL_READ;
grant role DV_D_ACCESS_MART_READ to role DV_D_USERROLE_FULL_READ;
grant role DV_D_ACCESS_VAULT_READ to role DV_D_USERROLE_FULL_READ;

-------------GRANT ROLE TO USER-------------

grant role DV_D_USERROLE_DEVELOPER to USER <YOUR_USER>;
grant role DV_D_USERROLE_LOAD_PROCESS to USER DV_D_TUSER_LOADER;
grant role DV_D_USERROLE_MART to USER DV_D_TUSER_TABLEAU;
grant role DV_D_USERROLE_FULL_READ to USER DV_D_TUSER_READER;


-------------DATABASE CREATION----------------

use role SYSADMIN;
grant create database on account to DV_D_OWNER_DATABASE;
grant create warehouse on account to DV_PROJECT_DATA_VAULT;


-------------DATABASE CREATION----------------

use role DV_D_OWNER_DATABASE;


create database DV_D_MAIN_DATABASE;

grant usage on database DV_D_MAIN_DATABASE to DV_D_ACCESS_WRITE;
grant usage on database DV_D_MAIN_DATABASE to DV_D_OWNER_DATABASE;
grant usage on database DV_D_MAIN_DATABASE to DV_D_ACCESS_VAULT_READ;
grant usage on database DV_D_MAIN_DATABASE to DV_D_ACCESS_MART_READ;



use role DV_PROJECT_DATA_VAULT;

create warehouse DV_D_LOADER_XS
with warehouse_size = XSMALL;

create warehouse DV_D_READER_M
with warehouse_size = MEDIUM;


-------------GRANT WAREHOUSE USAGE----------------
use role DV_PROJECT_DATA_VAULT;

grant usage on warehouse DV_D_LOADER_XS to DV_D_ACCESS_WRITE;
grant usage on warehouse DV_D_LOADER_XS to DV_D_OWNER_DATABASE;
grant usage on warehouse DV_D_LOADER_XS to DV_D_ACCESS_VAULT_READ;
grant usage on warehouse DV_D_LOADER_XS to DV_D_ACCESS_MART_READ;

grant usage on warehouse DV_D_READER_M to DV_D_ACCESS_VAULT_READ;
grant usage on warehouse DV_D_READER_M to DV_D_ACCESS_MART_READ;

