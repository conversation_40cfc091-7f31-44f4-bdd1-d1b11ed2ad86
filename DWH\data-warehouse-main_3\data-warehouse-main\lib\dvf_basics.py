"""
Module with essential data vault functions
"""

import base64
import hashlib
import datetime
from enum import Enum

# # ------------------- Constants ----------------------------

#                             1234567890123456789012345678
HASHKEY_FOR_MISSING_DATA   = "ffffffffffffffffffffffffffff"
HASHKEY_FOR_DELIVERED_NULL = "0000000000000000000000000000"
FAR_FUTURE_DATE_SQL_STRING = "2299-12-30 00:00:00.000"

# # ------------------- Constants ----------------------------

class Dvf_dataComplianceError(Exception):
    """Exception raised for errors that trigger a file rejection.
    Attributes:
        message -- explanation of the error
    """

    def __init__(self, message):
        self.message = message
    pass

class Dvf_generatorSettingsError(Exception):
    """Exception raised for errors that trigger a file rejection.
    Attributes:
        message -- explanation of the error
    """

    def __init__(self, message):
        self.message = message
    pass


class Dvf_dwh_connection_type(Enum):
    owner = 'data_owner'
    metadata = 'job_instance'
    raw_vault = 'data_writer'
    business_vault = 'data_writer'
    mart_general = 'data_writer'

def dvf_assemble_datavault_hash(attribute_list):
    """Calculates a datavault hash from all attributes in the list. Order of attributes is essential"""
    if type(attribute_list) is list:
        stringified = ""
        separator = ""
        has_data = False
        for element in attribute_list:
            if element is not None:
                stringified += separator + str(element)
                has_data = True
            else:
                stringified += separator
            separator = "|"
        while stringified[-1:] == "|":
            stringified = stringified[:-1]
        if __name__ == '__main__':
            print(">", stringified, "<")
        if not has_data:
            return HASHKEY_FOR_DELIVERED_NULL
        digest = hashlib.sha1(stringified.encode('utf-8')).digest()
        return base64.b64encode(digest).decode('ascii')
    else:
        raise Exception('Expected a list as parameter')


if __name__ == '__main__':
    # demo and test
    print(dvf_assemble_datavault_hash(['Ein Element']))
    print(dvf_assemble_datavault_hash(['Mit Zahlen und None ', 1, 5, None, None, 'hallo']))
    print(dvf_assemble_datavault_hash(['Ein Element und 2 None', None, None]))
    print(dvf_assemble_datavault_hash(['Zwei Elemente und 2 None', 2.23, None, None]))
    my_datetime = datetime.datetime.strptime("2020-12-14 12:00:00", "%Y-%m-%d %H:%M:%S")
    my_date = datetime.datetime.strptime("1980-08-19", "%Y-%m-%d").date()
    my_time = datetime.datetime.strptime("17:10:00", "%H:%M:%S").time()

    print(dvf_assemble_datavault_hash(['Time und Date elemente', 2, my_datetime, my_date, my_time]))

    try:
        print(dvf_assemble_datavault_hash('abcdef'))
        raise ("Should not work with string")
    except:
        print("Catched expected exeption")

    try:
        print(dvf_assemble_datavault_hash(12))
        raise ("Should not work with integer")
    except:
        print("Catched expected exeption")
