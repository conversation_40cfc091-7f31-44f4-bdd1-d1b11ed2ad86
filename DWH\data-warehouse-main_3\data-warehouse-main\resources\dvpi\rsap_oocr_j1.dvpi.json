{"dvdp_compiler": "dvpdc reference compiler,  release 0.6.2", "dvpi_version": "0.6.2", "compile_timestamp": "2024-10-28 12:45:21", "dvpd_version": "0.6.2", "pipeline_name": "rsap_oocr_j1", "dvpd_filename": "rsap_oocr_j1.dvpd.json", "tables": [{"table_name": "rsap_distribution_rule_oocr_hub", "table_stereotype": "hub", "schema_name": "rvlt_sap", "storage_component": "", "columns": [{"column_name": "MD_INSERTED_AT", "is_nullable": false, "column_class": "meta_load_date", "column_type": "TIMESTAMP"}, {"column_name": "MD_RUN_ID", "is_nullable": false, "column_class": "meta_load_process_id", "column_type": "INT"}, {"column_name": "MD_RECORD_SOURCE", "is_nullable": false, "column_class": "meta_record_source", "column_type": "VARCHAR(255)"}, {"column_name": "HK_RSAP_DISTRIBUTION_RULE_OOCR", "is_nullable": false, "column_class": "key", "column_type": "CHAR(28)"}, {"column_name": "COMPANY", "is_nullable": true, "column_class": "business_key", "column_type": "VARCHAR(50)", "prio_for_column_position": 50000}, {"column_name": "OCRCODE", "is_nullable": true, "column_class": "business_key", "column_type": "VARCHAR(20)", "prio_for_column_position": 50000}]}, {"table_name": "rsap_distribution_rule_oocr_j1_l10_sat", "table_stereotype": "sat", "schema_name": "rvlt_sap", "storage_component": "", "has_deletion_flag": true, "is_effectivity_sat": false, "is_enddated": true, "is_multiactive": false, "compare_criteria": "key+current", "uses_diff_hash": true, "columns": [{"column_name": "MD_INSERTED_AT", "is_nullable": false, "column_class": "meta_load_date", "column_type": "TIMESTAMP"}, {"column_name": "MD_RUN_ID", "is_nullable": false, "column_class": "meta_load_process_id", "column_type": "INT"}, {"column_name": "MD_RECORD_SOURCE", "is_nullable": false, "column_class": "meta_record_source", "column_type": "VARCHAR(255)"}, {"column_name": "MD_IS_DELETED", "is_nullable": false, "column_class": "meta_deletion_flag", "column_type": "BOOLEAN"}, {"column_name": "MD_VALID_BEFORE", "is_nullable": false, "column_class": "meta_load_enddate", "column_type": "TIMESTAMP"}, {"column_name": "HK_RSAP_DISTRIBUTION_RULE_OOCR", "is_nullable": false, "column_class": "parent_key", "column_type": "CHAR(28)", "parent_key_column_name": "HK_RSAP_DISTRIBUTION_RULE_OOCR", "parent_table_name": "rsap_distribution_rule_oocr_hub"}, {"column_name": "RH_RSAP_DISTRIBUTION_RULE_OOCR_J1_L10_SAT", "is_nullable": false, "column_class": "diff_hash", "column_type": "CHAR(28)"}, {"column_name": "JSON_TEXT", "is_nullable": true, "column_class": "content", "column_type": "VARCHAR", "exclude_from_change_detection": false, "prio_for_column_position": 50000}]}], "data_extraction": {"fetch_module_name": "none - ddl generation only"}, "parse_sets": [{"stage_properties": [{"stage_schema": "stage_rvlt", "stage_table_name": "rsap_oocr_j1_stage", "storage_component": ""}], "record_source_name_expression": "sap.oocr", "fields": [{"field_type": "VARCHAR(50)", "field_position": 1, "needs_encryption": false, "field_name": "COMPANY"}, {"field_type": "VARCHAR(20)", "field_position": 2, "needs_encryption": false, "field_name": "OCRCODE"}, {"field_type": "VARCHAR", "field_position": 3, "needs_encryption": false, "field_name": "JSON_TEXT"}], "hashes": [{"stage_column_name": "HK_RSAP_DISTRIBUTION_RULE_OOCR", "hash_origin_table": "rsap_distribution_rule_oocr_hub", "column_class": "key", "hash_fields": [{"field_name": "COMPANY", "prio_in_key_hash": 0, "field_target_table": "rsap_distribution_rule_oocr_hub", "field_target_column": "COMPANY"}, {"field_name": "OCRCODE", "prio_in_key_hash": 0, "field_target_table": "rsap_distribution_rule_oocr_hub", "field_target_column": "OCRCODE"}], "column_type": "CHAR(28)", "hash_encoding": "BASE64", "hash_function": "sha-1", "hash_concatenation_seperator": "|", "hash_timestamp_format_sqlstyle": "YYYY-MM-DD HH24:MI:SS.US", "hash_null_value_string": "", "model_profile_name": "_default", "hash_name": "KEY_OF_RSAP_DISTRIBUTION_RULE_OOCR_HUB"}, {"stage_column_name": "RH_RSAP_DISTRIBUTION_RULE_OOCR_J1_L10_SAT", "hash_origin_table": "rsap_distribution_rule_oocr_j1_l10_sat", "multi_row_content": false, "column_class": "diff_hash", "hash_fields": [{"field_name": "JSON_TEXT", "prio_in_diff_hash": 0, "prio_for_row_order": 50000, "field_target_table": "rsap_distribution_rule_oocr_j1_l10_sat", "field_target_column": "JSON_TEXT"}], "column_type": "CHAR(28)", "hash_encoding": "BASE64", "hash_function": "sha-1", "hash_concatenation_seperator": "|", "hash_timestamp_format_sqlstyle": "YYYY-MM-DD HH24:MI:SS.US", "hash_null_value_string": "", "model_profile_name": "_default", "related_key_hash": "KEY_OF_RSAP_DISTRIBUTION_RULE_OOCR_HUB", "hash_name": "DIFF_OF_RSAP_DISTRIBUTION_RULE_OOCR_J1_L10_SAT"}], "load_operations": [{"table_name": "rsap_distribution_rule_oocr_hub", "relation_name": "/", "operation_origin": "field mapping relation", "hash_mappings": [{"hash_class": "key", "column_name": "HK_RSAP_DISTRIBUTION_RULE_OOCR", "is_nullable": false, "hash_name": "KEY_OF_RSAP_DISTRIBUTION_RULE_OOCR_HUB", "stage_column_name": "HK_RSAP_DISTRIBUTION_RULE_OOCR"}], "data_mapping": [{"column_name": "COMPANY", "field_name": "COMPANY", "column_class": "business_key", "is_nullable": true, "stage_column_name": "COMPANY"}, {"column_name": "OCRCODE", "field_name": "OCRCODE", "column_class": "business_key", "is_nullable": true, "stage_column_name": "OCRCODE"}]}, {"table_name": "rsap_distribution_rule_oocr_j1_l10_sat", "relation_name": "/", "operation_origin": "field mapping relation", "hash_mappings": [{"hash_class": "parent_key", "column_name": "HK_RSAP_DISTRIBUTION_RULE_OOCR", "is_nullable": false, "hash_name": "KEY_OF_RSAP_DISTRIBUTION_RULE_OOCR_HUB", "stage_column_name": "HK_RSAP_DISTRIBUTION_RULE_OOCR"}, {"hash_class": "diff_hash", "column_name": "RH_RSAP_DISTRIBUTION_RULE_OOCR_J1_L10_SAT", "is_nullable": false, "hash_name": "DIFF_OF_RSAP_DISTRIBUTION_RULE_OOCR_J1_L10_SAT", "stage_column_name": "RH_RSAP_DISTRIBUTION_RULE_OOCR_J1_L10_SAT"}], "data_mapping": [{"column_name": "JSON_TEXT", "field_name": "JSON_TEXT", "column_class": "content", "is_nullable": true, "stage_column_name": "JSON_TEXT"}]}], "stage_columns": [{"stage_column_name": "MD_INSERTED_AT", "is_nullable": false, "stage_column_class": "meta_load_date", "column_type": "TIMESTAMP"}, {"stage_column_name": "MD_RUN_ID", "is_nullable": false, "stage_column_class": "meta_load_process_id", "column_type": "INT"}, {"stage_column_name": "MD_RECORD_SOURCE", "is_nullable": false, "stage_column_class": "meta_record_source", "column_type": "VARCHAR(255)"}, {"stage_column_name": "MD_IS_DELETED", "is_nullable": false, "stage_column_class": "meta_deletion_flag", "column_type": "BOOLEAN"}, {"stage_column_name": "HK_RSAP_DISTRIBUTION_RULE_OOCR", "stage_column_class": "hash", "is_nullable": false, "hash_name": "KEY_OF_RSAP_DISTRIBUTION_RULE_OOCR_HUB", "column_type": "CHAR(28)"}, {"stage_column_name": "RH_RSAP_DISTRIBUTION_RULE_OOCR_J1_L10_SAT", "stage_column_class": "hash", "is_nullable": false, "hash_name": "DIFF_OF_RSAP_DISTRIBUTION_RULE_OOCR_J1_L10_SAT", "column_type": "CHAR(28)"}, {"stage_column_name": "COMPANY", "stage_column_class": "data", "field_name": "COMPANY", "is_nullable": true, "column_type": "VARCHAR(50)", "column_classes": ["business_key"]}, {"stage_column_name": "OCRCODE", "stage_column_class": "data", "field_name": "OCRCODE", "is_nullable": true, "column_type": "VARCHAR(20)", "column_classes": ["business_key"]}, {"stage_column_name": "JSON_TEXT", "stage_column_class": "data", "field_name": "JSON_TEXT", "is_nullable": true, "column_type": "VARCHAR", "column_classes": ["content"]}]}]}