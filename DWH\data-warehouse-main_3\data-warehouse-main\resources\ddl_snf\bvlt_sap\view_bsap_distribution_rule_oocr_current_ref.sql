CREATE VIEW bvlt_sap.bsap_distribution_rule_oocr_current_ref AS
SELECT oocr_s.* exclude(MD_RUN_ID, MD_IS_DELETED,MD_VALID_BEFORE,HK_RSAP_DISTRIBUTION_RULE_OOCR,RH_RSAP_DISTRIBUTION_RULE_OOCR_J1_L10_SAT),
        oocr_h.company,
        oocr_h.ocrcode
FROM RVLT_SAP.RSAP_DISTRIBUTION_RULE_OOCR_HUB oocr_h
JOIN RVLT_SAP.RSAP_DISTRIBUTION_RULE_OOCR_P1_L20_SAT oocr_s ON oocr_h.hk_rsap_distribution_rule_oocr = oocr_s.hk_rsap_distribution_rule_oocr
    AND oocr_s.md_valid_before = lib.dwh_far_future_date()
    AND NOT oocr_s.md_is_deleted
    AND oocr_s.MD_RECORD_SOURCE <> 'SYSTEM';