{"dvpd_version": "0.6.2", "stage_properties": [{"stage_schema": "stage_rvlt", "stage_table_name": "rbbe_ORDER_P1_stage"}], "pipeline_name": "rbbe_ORDER_P1", "record_source_name_expression": "billbee.order", "data_extraction": {"fetch_module_name": "billbee api", "parse_module_name": "json_array", "load_module_name": "python_framework", "json_array_path": "$.Data"}, "fields": [{"field_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>rId", "json_path": "<PERSON><PERSON><PERSON><PERSON><PERSON>rId", "field_type": "NUMBER(36,0)", "targets": [{"table_name": "RBBE_ORDER_HUB"}]}, {"field_name": "Bill<PERSON>eeParentOrderId", "json_path": "Bill<PERSON>eeParentOrderId", "field_type": "NUMBER(36,0)", "targets": [{"table_name": "RBBE_ORDER_HUB", "column_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>rId", "relation_names": ["parent"]}]}, {"field_name": "Customer_Id", "json_path": "Customer.Id", "field_type": "NUMBER(36,0)", "targets": [{"table_name": "RBBE_CUSTOMER_HUB", "column_name": "ID"}]}, {"field_name": "BillbeeShopId", "json_path": "<PERSON><PERSON><PERSON>ShopId", "field_type": "NUMBER(36,0)", "targets": [{"table_name": "RBBE_SHOP_HUB"}]}, {"field_name": "OrderNumber", "json_path": "OrderNumber", "field_type": "VARCHAR(200)", "targets": [{"table_name": "RBBE_ORDER_ORDER_P1_L10_SAT"}]}, {"field_name": "TotalCost", "json_path": "TotalCost", "field_type": "NUMBER(20,2)", "targets": [{"table_name": "RBBE_ORDER_ORDER_P1_L10_SAT"}]}, {"field_name": "AdjustmentCost", "json_path": "AdjustmentCost", "field_type": "NUMBER(20,2)", "targets": [{"table_name": "RBBE_ORDER_ORDER_P1_L10_SAT"}]}, {"field_name": "<PERSON><PERSON><PERSON><PERSON>", "json_path": "<PERSON><PERSON><PERSON><PERSON>", "field_type": "VARCHAR(200)", "targets": [{"table_name": "RBBE_ORDER_ORDER_P1_L10_SAT"}]}, {"field_name": "ShippingCost", "json_path": "ShippingCost", "field_type": "NUMBER(20,2)", "targets": [{"table_name": "RBBE_ORDER_ORDER_P1_L10_SAT"}]}, {"field_name": "InvoiceAddress_countryISO2", "json_path": "InvoiceAddress.CountryISO2", "field_type": "VARCHAR(20)", "targets": [{"table_name": "RBBE_ORDER_ORDER_P1_L10_SAT"}]}, {"field_name": "State", "json_path": "State", "field_type": "NUMBER(36,0)", "targets": [{"table_name": "RBBE_ORDER_ORDER_P2_L10_SAT"}]}, {"field_name": "CreatedAt", "json_path": "CreatedAt", "field_type": "TIMESTAMP", "targets": [{"table_name": "RBBE_ORDER_ORDER_P2_L10_SAT"}]}, {"field_name": "ShippedAt", "json_path": "ShippedAt", "field_type": "TIMESTAMP", "targets": [{"table_name": "RBBE_ORDER_ORDER_P2_L10_SAT"}]}, {"field_name": "ConfirmedAt", "json_path": "ConfirmedAt", "field_type": "TIMESTAMP", "targets": [{"table_name": "RBBE_ORDER_ORDER_P2_L10_SAT"}]}, {"field_name": "InvoiceDate", "json_path": "InvoiceDate", "field_type": "TIMESTAMP", "targets": [{"table_name": "RBBE_ORDER_ORDER_P2_L10_SAT"}]}, {"field_name": "PayedAt", "json_path": "PayedAt", "field_type": "TIMESTAMP", "targets": [{"table_name": "RBBE_ORDER_ORDER_P2_L10_SAT"}]}, {"field_name": "UpdatedAt", "json_path": "UpdatedAt", "field_type": "TIMESTAMP", "targets": [{"table_name": "RBBE_ORDER_ORDER_P1_L10_SAT", "exclude_from_change_detection": "true"}, {"table_name": "RBBE_ORDER_ORDER_P2_L10_SAT", "exclude_from_change_detection": "true"}]}, {"field_name": "LastModifiedAt", "json_path": "LastModifiedAt", "field_type": "TIMESTAMP", "targets": [{"table_name": "RBBE_ORDER_ORDER_P1_L10_SAT", "exclude_from_change_detection": "true"}, {"table_name": "RBBE_ORDER_ORDER_P2_L10_SAT", "exclude_from_change_detection": "true"}]}, {"field_name": "<PERSON><PERSON>_<PERSON>beeShopName", "json_path": "<PERSON><PERSON><PERSON>", "field_type": "VARCHAR(200)", "targets": [{"table_name": "RBBE_ORDER_SHOP_P1_L10_SAT"}]}], "data_vault_model": [{"schema_name": "rvlt_billbee", "tables": [{"table_name": "RBBE_ORDER_HUB", "table_stereotype": "hub", "hub_key_column_name": "HK_RBBE_ORDER"}, {"table_name": "RBBE_ORDER_ORDER_P1_L10_SAT", "table_stereotype": "sat", "satellite_parent_table": "RBBE_ORDER_HUB", "diff_hash_column_name": "RH_RBBE_ORDER_ORDER_P1_L10_SAT"}, {"table_name": "RBBE_ORDER_ORDER_P2_L10_SAT", "table_stereotype": "sat", "satellite_parent_table": "RBBE_ORDER_HUB", "diff_hash_column_name": "RH_RBBE_ORDER_ORDER_P2_L10_SAT"}, {"table_name": "RBBE_CUSTOMER_HUB", "table_stereotype": "hub", "hub_key_column_name": "HK_RBBE_CUSTOMER"}, {"table_name": "RBBE_ORDER_CUSTOMER_LNK", "table_stereotype": "lnk", "link_key_column_name": "LK_RBBE_ORDER_CUSTOMER", "link_parent_tables": ["RBBE_ORDER_HUB", "RBBE_CUSTOMER_HUB"]}, {"table_name": "RBBE_ORDER_CUSTOMER_ESAT", "table_stereotype": "sat", "satellite_parent_table": "RBBE_ORDER_CUSTOMER_LNK", "tracked_relation_name": "/", "driving_keys": ["HK_RBBE_ORDER"]}, {"table_name": "RBBE_SHOP_HUB", "table_stereotype": "hub", "hub_key_column_name": "HK_RBBE_SHOP"}, {"table_name": "RBBE_ORDER_SHOP_LNK", "table_stereotype": "lnk", "link_key_column_name": "LK_RBBE_ORDER_SHOP", "link_parent_tables": ["RBBE_ORDER_HUB", "RBBE_SHOP_HUB"]}, {"table_name": "RBBE_ORDER_SHOP_P1_L10_SAT", "table_stereotype": "sat", "satellite_parent_table": "RBBE_ORDER_SHOP_LNK", "driving_keys": ["HK_RBBE_ORDER"]}, {"table_name": "RBBE_ORDER_PARENT_ORDER_LNK", "table_stereotype": "lnk", "link_key_column_name": "LK_RBBE_ORDER_PARENT_ORDER", "link_parent_tables": ["RBBE_ORDER_HUB", {"table_name": "RBBE_ORDER_HUB", "relation_name": "parent"}]}, {"table_name": "RBBE_ORDER_PARENT_ORDER_ESAT", "table_stereotype": "sat", "satellite_parent_table": "RBBE_ORDER_PARENT_ORDER_LNK", "driving_keys": ["HK_RBBE_ORDER"]}]}]}