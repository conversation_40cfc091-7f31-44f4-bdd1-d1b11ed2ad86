from azure.storage.blob import BlobServiceClient
from azure.identity import DefaultAzureCredential
import os
import sys
from lib.configuration import configuration_read


def error_print(*args, **kwargs):
    """Print a message to stderr"""
    print(*args, file=sys.stderr, **kwargs)

def connection_azrblob(object="blob_storage"):
    """Reads configuration for the resource from environment and returns a new azure blob storage connection"""
    system = 'dwh_azrblob'
    try:
        credential = DefaultAzureCredential(managed_identity_client_id=os.getenv('UMI_CLIENT_ID')) #UMI_CLIENT_ID need to be explicit for cloud execution
        params = configuration_read(system, object,['url'])
        bs_url = params['url']
        connection = BlobServiceClient(account_url=bs_url, credential=credential)
    except Exception as error:
        error_print('Connection Error:', error)
        error_print('Object used:', object)
        raise
    return connection


if __name__ == '__main__':
    # small test
    blob_service_client = connection_azrblob()
    containers = blob_service_client.list_containers(include_metadata=True)
    for container in containers:
        print(container['name'], container['metadata'])