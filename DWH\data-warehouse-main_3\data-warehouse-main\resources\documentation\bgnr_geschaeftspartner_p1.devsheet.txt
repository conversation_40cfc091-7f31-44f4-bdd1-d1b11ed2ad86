Data vault pipeline developer cheat sheet 
rendered from  bgnr_geschaeftspartner_p1.dvpi

pipeline name:  bgnr_geschaeftspartner_p1

------------------------------------------------------
record source:  bgnr_geschaeftspartner_p1

Source fields:
       XD_GESCHAEFTSPARTNER_ID  VARCHAR(255)
       GESELLSCHAFT             VARCHAR(255)
       NAME                     VARCHAR(255)
       KUNDENGRUPPE             VARCHAR(255)
       VERKAUFSGEBIET           VARCHAR(255)
       VERKAEUFERNUMMER         VARCHAR(255)
       COMPANY                  VARCHAR(255)
       CARDCODE                 VARCHAR(255)
       BILLBEESHOPID            NUMBER(36,0)


------------------------------------------------------
Table List:
stage_table.bvlt_general.bgnr_geschaeftspartner_p1_stage
table.bvlt_general.bgnr_geschaeftspartner_hub
table.bvlt_general.bgnr_geschaeftspartner_p1_b10_sat
table.bvlt_general.bgnr_geschaeftspartner_rsap_business_partner_lnk
table.bvlt_general.bgnr_geschaeftspartner_rsap_business_partner_esat
table.bvlt_general.bgnr_geschaeftspartner_rbbe_shop_lnk
table.bvlt_general.bgnr_geschaeftspartner_rbbe_shop_esat

------------------------------------------------------
stage table:  stage_bvlt.bgnr_geschaeftspartner_p1_stage
Field to Stage mapping:
	--business keys,
		BILLBEESHOPID            >  BILLBEESHOPID,
		CARDCODE                 >  CARDCODE,
		COMPANY                  >  COMPANY,
		XD_GESCHAEFTSPARTNER_ID  >  XD_GESCHAEFTSPARTNER_ID,

	--content,
		GESELLSCHAFT             >  GESELLSCHAFT,
		KUNDENGRUPPE             >  KUNDENGRUPPE ,
		NAME                     >  NAME,
		VERKAEUFERNUMMER         >  VERKAEUFERNUMMER ,
		VERKAUFSGEBIET           >  VERKAUFSGEBIET 

------------------------------------------------------
Hash value composition

HK_RSAP_BUSINESS_PARTNER_OCRD (key)
		CARDCODE 
		COMPANY 

HK_RBBE_SHOP (key)
		BILLBEESHOPID 

HK_BGNR_GESCHAEFTSPARTNER (key)
		XD_GESCHAEFTSPARTNER_ID 

LK_BGNR_GESCHAEFTSPARTNER_RSAP_BUSINESS_PARTNER (key)
		XD_GESCHAEFTSPARTNER_ID 
		CARDCODE 
		COMPANY 

LK_BGNR_GESCHAEFTSPARTNER_RBBE_SHOP (key)
		XD_GESCHAEFTSPARTNER_ID 
		BILLBEESHOPID 

RH_BGNR_GESCHAEFTSPARTNER_P1_B10_SAT (diff_hash)
		GESELLSCHAFT 
		KUNDENGRUPPE  
		NAME 
		VERKAEUFERNUMMER  
		VERKAUFSGEBIET  


------------------------------------------------------
Table load method
(STAGE >  VAULT)

bgnr_geschaeftspartner_hub (/) can be loaded by convention
		  key: HK_BGNR_GESCHAEFTSPARTNER  >  HK_BGNR_GESCHAEFTSPARTNER
		  business_key: XD_GESCHAEFTSPARTNER_ID  >  XD_GESCHAEFTSPARTNER_ID 

bgnr_geschaeftspartner_p1_b10_sat (/) can be loaded by convention
		  parent_key: HK_BGNR_GESCHAEFTSPARTNER  >  HK_BGNR_GESCHAEFTSPARTNER
		  diff_hash: RH_BGNR_GESCHAEFTSPARTNER_P1_B10_SAT  >  RH_BGNR_GESCHAEFTSPARTNER_P1_B10_SAT
		  content: GESELLSCHAFT  >  GESELLSCHAFT 
		  content: NAME  >  NAME 
		  content: KUNDENGRUPPE   >  KUNDENGRUPPE  
		  content: VERKAUFSGEBIET   >  VERKAUFSGEBIET  
		  content: VERKAEUFERNUMMER   >  VERKAEUFERNUMMER  

bgnr_geschaeftspartner_rsap_business_partner_lnk (/) can be loaded by convention
		  parent_key_1: HK_BGNR_GESCHAEFTSPARTNER  >  HK_BGNR_GESCHAEFTSPARTNER
		  parent_key_2: HK_RSAP_BUSINESS_PARTNER_OCRD  >  HK_RSAP_BUSINESS_PARTNER_OCRD
		  key: LK_BGNR_GESCHAEFTSPARTNER_RSAP_BUSINESS_PARTNER  >  LK_BGNR_GESCHAEFTSPARTNER_RSAP_BUSINESS_PARTNER

bgnr_geschaeftspartner_rsap_business_partner_esat (/) can be loaded by convention
		! Driving keys: HK_BGNR_GESCHAEFTSPARTNER 
		  parent_key: LK_BGNR_GESCHAEFTSPARTNER_RSAP_BUSINESS_PARTNER  >  LK_BGNR_GESCHAEFTSPARTNER_RSAP_BUSINESS_PARTNER

bgnr_geschaeftspartner_rbbe_shop_lnk (/) can be loaded by convention
		  parent_key_1: HK_BGNR_GESCHAEFTSPARTNER  >  HK_BGNR_GESCHAEFTSPARTNER
		  parent_key_2: HK_RBBE_SHOP  >  HK_RBBE_SHOP
		  key: LK_BGNR_GESCHAEFTSPARTNER_RBBE_SHOP  >  LK_BGNR_GESCHAEFTSPARTNER_RBBE_SHOP

bgnr_geschaeftspartner_rbbe_shop_esat (/) can be loaded by convention
		! Driving keys: HK_BGNR_GESCHAEFTSPARTNER 
		  parent_key: LK_BGNR_GESCHAEFTSPARTNER_RBBE_SHOP  >  LK_BGNR_GESCHAEFTSPARTNER_RBBE_SHOP

