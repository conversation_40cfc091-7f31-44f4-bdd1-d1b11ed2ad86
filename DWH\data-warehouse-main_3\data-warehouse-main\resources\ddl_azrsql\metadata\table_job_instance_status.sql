-- DROP TABLE metadata.job_instance_status - Falls die Tabelle schon existiert

CREATE TABLE metadata.job_instance_status (
	job_instance_id int not null primary key,
	process_instance_id bigint NULL,
	parent_id bigint NULL,
	process_instance_name varchar(255) NULL,
	job_name varchar(255) NULL,
	work_item varchar(1024) NULL,
	time_range_start datetime2  NULL,
	time_range_end datetime2  NULL,
	value_range_start varchar(512) NULL,
	value_range_end varchar(512) NULL,
	job_started_at datetime2  NOT NULL,
	job_ended_at datetime2  NULL,
	count_input bigint NULL,
	count_output bigint NULL,
	count_rejected bigint NULL,
	count_ignored bigint NULL,
	return_code bigint NULL,
	return_message varchar(1024) NULL,
	host_name varchar(255) NULL,
	host_pid bigint NULL,
	host_user varchar(128) NULL,
);

GRANT INSERT, SELECT, UPDATE ON OBJECT::metadata.job_instance_status TO process_user_metadata;

CREATE INDEX JOB_INSTANCE_STATUS_JOB_NAME ON metadata.job_instance_status(job_name);

-- <PERSON><PERSON><PERSON> dem <PERSON>utzer Rechte für das Schema
GRANT SELECT, INSERT, UPDATE ON SCHEMA::metadata TO process_user_metadata;

-- Erstellt sequence
CREATE SEQUENCE metadata.job_instance_status_job_instance_id_seq
AS INT
START WITH 1    
INCREMENT BY 1;