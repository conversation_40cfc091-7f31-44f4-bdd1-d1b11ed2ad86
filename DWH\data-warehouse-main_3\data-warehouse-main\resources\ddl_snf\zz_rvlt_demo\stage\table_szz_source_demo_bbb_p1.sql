-- generated script for stage_rvlt.szz_source_demo_bbb_p1

-- DROP TABLE stage_rvlt.szz_source_demo_bbb_p1;

CREATE TABLE stage_rvlt.szz_source_demo_bbb_p1 (
--metadata,
MD_INSERTED_AT TIMESTAMP NOT NULL,
MD_JOB_INSTANCE_ID INT NOT NULL,
MD_RECORD_SOURCE VARCHAR(255) NOT NULL,
MD_IS_DELETED BOOLEAN NOT NULL,
--hash keys,
GH_BBB_BBB_P1_MSAT CHAR(28) NOT NULL,
HK_BBB CHAR(28) NOT NULL,
L<PERSON>_<PERSON><PERSON>_DCL CHAR(28) NOT NULL,
--business keys,
BBB_BK1F integer  NULL,
BBB_DCL_DC1F VARCHAR(100)  NULL,
--content,
BBB_DCL_P1_C1 VARCHAR(20)  NULL,
BBB_DCL_P1_C2F VARCHAR(100)  NULL
);
-- end of script --