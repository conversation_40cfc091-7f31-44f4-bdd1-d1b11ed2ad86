CREATE VIEW bvlt_sap.bsap_exchange_rate_ortt_current_ref AS
SELECT ortt_s.* exclude(MD_RUN_ID, MD_IS_DELETED,MD_VALID_BEFORE,lk_rsap_ortt_exchange_rate_dlnk,rh_rsap_currency_ortt_exchange_rate_j1_l10_sat),
        ortt_h.company,
        ortt_h.currency,
        ortt_dl.ratedate
FROM RVLT_SAP.RSAP_CURRENCY_ORTT_HUB ortt_h
JOIN RVLT_SAP.RSAP_CURRENCY_ORTT_EXCHANGE_RATE_DLNK ortt_dl ON
    ortt_h.hk_rsap_currency_ortt = ortt_dl.hk_rsap_currency_ortt
JOIN RVLT_SAP.RSAP_CURRENCY_ORTT_EXCHANGE_RATE_P1_L20_SAT ortt_s ON
    ortt_dl.lk_rsap_ortt_exchange_rate_dlnk = ortt_s.lk_rsap_ortt_exchange_rate_dlnk
    and ortt_s.md_valid_before = lib.dwh_far_future_date()
    and not ortt_s.md_is_deleted
    AND ortt_s.MD_RECORD_SOURCE <> 'SYSTEM';