# RIN2 DVPD ETL 用户手册
## 基于Data Vault 2.0理论的完整实践指南

### 版本信息
- **文档版本**: 1.0
- **创建日期**: 2024年
- **适用系统**: Data Warehouse Main 3
- **DVPD版本**: 0.6.2

---

## 目录

1. [概述](#概述)
2. [Data Vault 2.0理论基础](#data-vault-20理论基础)
3. [RIN2业务场景](#rin2业务场景)
4. [DVPD配置详解](#dvpd配置详解)
5. [ETL处理流程](#etl处理流程)
6. [数据模型设计](#数据模型设计)
7. [代码实现分析](#代码实现分析)
8. [数据质量保证](#数据质量保证)
9. [部署与运维](#部署与运维)
10. [故障排除](#故障排除)

---

## 概述

本手册以SAP系统中的RIN2（贷项凭证行项目）为实际案例，详细介绍如何使用DVPD（Data Vault Pipeline Definition）框架实现基于Data Vault 2.0架构的ETL处理流程。

### 关键特性
- **声明式配置**: 通过JSON配置文件定义数据管道
- **自动化代码生成**: 基于配置自动生成DDL和ETL代码
- **Data Vault 2.0合规**: 严格遵循Data Vault 2.0建模标准
- **数据质量保证**: 内置哈希冲突检测和数据一致性验证
- **可追溯性**: 完整的数据血缘和变更历史记录

---

## Data Vault 2.0理论基础

### 核心概念

#### 1. Hub（中心表）
Hub表存储业务实体的唯一标识符，是Data Vault模型的核心。

**特征**：
- 包含业务键（Business Key）
- 包含哈希键（Hash Key）
- 包含元数据字段（加载时间、记录源等）
- 不包含描述性属性

**RIN2示例**：
```sql
-- RSAP_CREDIT_MEMO_LINE_RIN1_HUB
HK_RSAP_CREDIT_MEMO_LINE_RIN1  -- 哈希键
COMPANY                        -- 业务键：公司
DOCENTRY                       -- 业务键：文档编号
LINENUM                        -- 业务键：行号
MD_INSERTED_AT                 -- 元数据：插入时间
MD_RUN_ID                      -- 元数据：运行ID
MD_RECORD_SOURCE               -- 元数据：记录源
```

#### 2. Link（链接表）
Link表表示业务实体之间的关系。

**特征**：
- 包含多个Hub的哈希键
- 包含自己的链接键（Link Key）
- 可以包含关系的上下文信息

**RIN2示例**：
```sql
-- RSAP_CREDIT_MEMO_LINE_RIN1_RIN2_DLNK (Dependent Link)
LK_RSAP_CREDIT_MEMO_LINE_RIN1_RIN2  -- 链接键
HK_RSAP_CREDIT_MEMO_LINE_RIN1       -- 父Hub的哈希键
GROUPNUM                            -- 依赖上下文：组号
```

#### 3. Satellite（卫星表）
Satellite表存储业务实体的描述性属性和历史变更。

**特征**：
- 包含父表的哈希键
- 包含记录哈希（Record Hash）用于变更检测
- 包含有效期字段
- 存储所有描述性属性

**RIN2示例**：
```sql
-- RSAP_CREDIT_MEMO_LINE_RIN1_RIN2_J1_L10_SAT
LK_RSAP_CREDIT_MEMO_LINE_RIN1_RIN2           -- 父Link的键
RH_RSAP_CREDIT_MEMO_LINE_RIN1_RIN2_J1_L10_SAT -- 记录哈希
JSON_TEXT                                     -- 业务属性
MD_VALID_BEFORE                              -- 有效期结束时间
MD_IS_DELETED                                -- 删除标记
```

### Data Vault 2.0设计原则

#### 1. 插入优先（Insert-Only）
- 所有数据变更通过插入新记录实现
- 保持完整的历史记录
- 支持时间旅行查询

#### 2. 哈希键设计
- 使用SHA-1算法生成28字符的Base64编码哈希
- 确保全局唯一性
- 支持分布式环境

#### 3. 元数据标准化
- 统一的元数据字段命名
- 标准化的数据类型
- 一致的审计信息

---

## RIN2业务场景

### 业务背景
RIN2表示SAP系统中贷项凭证的行项目详细信息，是财务管理中的重要数据实体。

### 数据特征
- **来源系统**: SAP Business One
- **数据格式**: JSON
- **更新频率**: 实时/批量
- **数据量级**: 中等规模
- **业务重要性**: 高

### 关键业务字段
- **Company**: 公司代码
- **DocEntry**: 文档编号
- **LineNum**: 行号
- **GroupNum**: 组号（用于关联RIN1和RIN2）
- **JSON_TEXT**: 完整的业务属性JSON

### 数据关系
```
RIN1 (贷项凭证头) 1:N RIN2 (贷项凭证行)
通过 GroupNum 字段建立关联关系
```

---

## DVPD配置详解

### 配置文件结构
DVPD配置文件（rsap_rin2_j1.dvpd.json）定义了完整的数据管道：

```json
{
    "dvpd_version": "0.6.2",
    "stage_properties": [
        {
            "stage_schema": "stage_rvlt",
            "stage_table_name": "rsap_rin2_j1_stage"
        }
    ],
    "pipeline_name": "rsap_rin2_j1",
    "record_source_name_expression": "sap.rin2",
    "data_extraction": {
        "fetch_module_name": "none - ddl and cnode snippet generation only"
    }
}
```

### 字段映射配置
```json
"fields": [
    {
        "field_name": "company",
        "field_type": "Varchar(50)",
        "targets": [{"table_name": "RSAP_CREDIT_MEMO_LINE_RIN1_HUB"}]
    },
    {
        "field_name": "docentry",
        "field_type": "INTEGER",
        "targets": [{"table_name": "RSAP_CREDIT_MEMO_LINE_RIN1_HUB"}]
    },
    {
        "field_name": "linenum",
        "field_type": "INTEGER",
        "targets": [{"table_name": "RSAP_CREDIT_MEMO_LINE_RIN1_HUB"}]
    },
    {
        "field_name": "groupnum",
        "field_type": "INTEGER",
        "targets": [{"table_name": "RSAP_CREDIT_MEMO_LINE_RIN1_RIN2_DLNK"}]
    },
    {
        "field_name": "json_text",
        "field_type": "VARCHAR",
        "targets": [{
            "table_name": "RSAP_CREDIT_MEMO_LINE_RIN1_RIN2_J1_L10_SAT",
            "exclude_json_paths_from_change_detection": ["UpdateDate"]
        }]
    }
]
```

### Data Vault模型配置
```json
"data_vault_model": [
    {
        "schema_name": "rvlt_sap",
        "tables": [
            {
                "table_name": "RSAP_CREDIT_MEMO_LINE_RIN1_HUB",
                "table_stereotype": "hub",
                "hub_key_column_name": "HK_RSAP_CREDIT_MEMO_LINE_RIN1"
            },
            {
                "table_name": "RSAP_CREDIT_MEMO_LINE_RIN1_RIN2_DLNK",
                "table_stereotype": "lnk",
                "link_key_column_name": "LK_RSAP_CREDIT_MEMO_LINE_RIN1_RIN2",
                "link_parent_tables": ["RSAP_CREDIT_MEMO_LINE_RIN1_HUB"]
            },
            {
                "table_name": "RSAP_CREDIT_MEMO_LINE_RIN1_RIN2_J1_L10_SAT",
                "table_stereotype": "sat",
                "satellite_parent_table": "RSAP_CREDIT_MEMO_LINE_RIN1_RIN2_DLNK",
                "diff_hash_column_name": "RH_RSAP_CREDIT_MEMO_LINE_RIN1_RIN2_J1_L10_SAT"
            }
        ]
    }
]
```

---

## ETL处理流程

### 整体架构
```
源数据(JSON) → 暂存区(Stage) → 数据保险库(Raw Vault) → 业务保险库(Business Vault)
```

### 详细流程步骤

#### 1. 数据提取（Extract）
```python
# 从Azure Blob Storage获取源文件
source_files = get_source_files_from_blob_storage(
    blob_service_client,
    sap_source_object,
    container_name
)

# 解析JSON数据
source_json = fetch_json_source_file_from_blob_container(blob_client)
```

#### 2. 数据转换（Transform）
```python
# 构建业务键哈希
stage_data_row_temp['hk_rsap_credit_memo_line_rin1'] = dvf_assemble_datavault_hash([
    stage_data_row_temp['company'],
    stage_data_row_temp['docentry'],
    stage_data_row_temp['linenum']
])

# 构建链接键哈希
stage_data_row_temp['lk_rsap_credit_memo_line_rin1_rin2'] = dvf_assemble_datavault_hash([
    stage_data_row_temp['groupnum'],
    stage_data_row_temp['company'],
    stage_data_row_temp['docentry'],
    stage_data_row_temp['linenum']
])

# 构建记录哈希
stage_data_row_temp['rh_rsap_credit_memo_line_rin1_rin2_j1_l10_sat'] = dvf_assemble_datavault_hash([
    json_text_for_hash_diff
])
```

#### 3. 数据加载（Load）
```python
# 加载到Hub表
statement_list = dvf_get_datavault_hub_elt_sql(
    vault_table='rsap_credit_memo_line_rin1_hub',
    vault_schema='rvlt_sap',
    stage_hk_column='HK_RSAP_CREDIT_MEMO_LINE_RIN1',
    stage_bk_column_list=['COMPANY', 'DOCENTRY', 'LINENUM']
)

# 加载到Link表
statement_list = dvf_get_datavault_dlnk_elt_sql(
    vault_table='rsap_credit_memo_line_rin1_rin2_dlnk',
    vault_schema='rvlt_sap',
    stage_lk_column='LK_RSAP_credit_memo_LINE_rin1_rin2',
    stage_hk_column_list=['HK_RSAP_CREDIT_MEMO_LINE_RIN1'],
    stage_dc_column_list=['GROUPNUM']
)

# 加载到Satellite表
statement_list = dvf_get_datavault_sat_elt_sql(
    vault_table='rsap_credit_memo_line_rin1_rin2_j1_l10_sat',
    vault_schema='rvlt_sap',
    stage_hk_column='LK_RSAP_credit_memo_LINE_rin1_rin2',
    stage_rh_column='RH_RSAP_credit_memo_LINE_rin1_rin2_J1_L10_SAT',
    stage_content_column_list=['JSON_TEXT']
)
```

---

## 数据模型设计

### 物理表结构

#### Hub表：RSAP_CREDIT_MEMO_LINE_RIN1_HUB
```sql
CREATE TABLE rvlt_sap.rsap_credit_memo_line_rin1_hub (
    MD_INSERTED_AT                TIMESTAMP      NOT NULL,
    MD_RUN_ID                     INT            NOT NULL,
    MD_RECORD_SOURCE              VARCHAR(255)   NOT NULL,
    HK_RSAP_CREDIT_MEMO_LINE_RIN1 CHAR(28)       NOT NULL,
    COMPANY                       VARCHAR(50)    NULL,
    DOCENTRY                      INTEGER        NULL,
    LINENUM                       INTEGER        NULL
);
```

#### Link表：RSAP_CREDIT_MEMO_LINE_RIN1_RIN2_DLNK
```sql
CREATE TABLE rvlt_sap.rsap_credit_memo_line_rin1_rin2_dlnk (
    MD_INSERTED_AT                     TIMESTAMP      NOT NULL,
    MD_RUN_ID                          INT            NOT NULL,
    MD_RECORD_SOURCE                   VARCHAR(255)   NOT NULL,
    HK_RSAP_CREDIT_MEMO_LINE_RIN1      CHAR(28)       NOT NULL,
    LK_RSAP_CREDIT_MEMO_LINE_RIN1_RIN2 CHAR(28)       NOT NULL,
    GROUPNUM                           INTEGER        NULL
);
```

#### Satellite表：RSAP_CREDIT_MEMO_LINE_RIN1_RIN2_J1_L10_SAT
```sql
CREATE TABLE rvlt_sap.rsap_credit_memo_line_rin1_rin2_j1_l10_sat (
    MD_INSERTED_AT                                TIMESTAMP      NOT NULL,
    MD_RUN_ID                                     INT            NOT NULL,
    MD_RECORD_SOURCE                              VARCHAR(255)   NOT NULL,
    MD_IS_DELETED                                 BOOLEAN        NOT NULL,
    MD_VALID_BEFORE                               TIMESTAMP      NOT NULL,
    LK_RSAP_CREDIT_MEMO_LINE_RIN1_RIN2            CHAR(28)       NOT NULL,
    RH_RSAP_CREDIT_MEMO_LINE_RIN1_RIN2_J1_L10_SAT CHAR(28)       NOT NULL,
    JSON_TEXT                                     VARCHAR        NULL
);
```

#### 暂存表：rsap_rin2_j1_stage
```sql
CREATE TABLE stage_rvlt.rsap_rin2_j1_stage (
    MD_INSERTED_AT                                TIMESTAMP      NOT NULL,
    MD_RUN_ID                                     INT            NOT NULL,
    MD_RECORD_SOURCE                              VARCHAR(255)   NOT NULL,
    MD_IS_DELETED                                 BOOLEAN        NOT NULL,
    COMPANY                                       VARCHAR(50)    NULL,
    DOCENTRY                                      INTEGER        NULL,
    LINENUM                                       INTEGER        NULL,
    GROUPNUM                                      INTEGER        NULL,
    JSON_TEXT                                     VARCHAR        NULL,
    HK_RSAP_CREDIT_MEMO_LINE_RIN1                 CHAR(28)       NULL,
    LK_RSAP_CREDIT_MEMO_LINE_RIN1_RIN2            CHAR(28)       NULL,
    RH_RSAP_CREDIT_MEMO_LINE_RIN1_RIN2_J1_L10_SAT CHAR(28)       NULL
);
```

### 数据关系图
```
┌─────────────────────────────────┐
│ RSAP_CREDIT_MEMO_LINE_RIN1_HUB  │
│ ─────────────────────────────── │
│ HK_RSAP_CREDIT_MEMO_LINE_RIN1   │◄─┐
│ COMPANY                         │  │
│ DOCENTRY                        │  │
│ LINENUM                         │  │
└─────────────────────────────────┘  │
                                     │
┌─────────────────────────────────────┐ │
│ RSAP_CREDIT_MEMO_LINE_RIN1_RIN2_DLNK│ │
│ ─────────────────────────────────── │ │
│ LK_RSAP_CREDIT_MEMO_LINE_RIN1_RIN2  │◄┼─┐
│ HK_RSAP_CREDIT_MEMO_LINE_RIN1       │─┘ │
│ GROUPNUM                            │   │
└─────────────────────────────────────┘   │
                                          │
┌──────────────────────────────────────────┐ │
│ RSAP_CREDIT_MEMO_LINE_RIN1_RIN2_J1_L10_SAT│ │
│ ──────────────────────────────────────── │ │
│ LK_RSAP_CREDIT_MEMO_LINE_RIN1_RIN2       │─┘
│ RH_RSAP_CREDIT_MEMO_LINE_RIN1_RIN2_J1_L10_SAT│
│ JSON_TEXT                                │
│ MD_VALID_BEFORE                          │
└──────────────────────────────────────────┘
```

---

## 代码实现分析

### 主程序结构（__main__.py）
```python
def main():
    # 1. 初始化作业实例
    my_job_instance = CimtJobInstance("rsap_rin2_j1")
    my_job_instance.start_instance()

    # 2. 部署数据模型
    deploy_datamodel(my_job_instance)

    # 3. 处理源文件
    source_files = get_source_files_from_blob_storage(
        blob_service_client, sap_source_object, container_name
    )

    # 4. 按文件名排序处理
    sorted_source_file_names = sorted([file.name for file in source_files])
    for file_name in sorted_source_file_names:
        load_vault_1(parent_job_instance=my_job_instance, file_name=file_name)
        move_processed_file_to_processed_container(blob_service_client, file_name)

    # 5. 结束作业实例
    my_job_instance.end_instance()
```

### 数据模型部署（deploy_datamodel）
```python
def deploy_datamodel(my_job_instance):
    deployment_manager = Dvf_ddl_deploymentmanager_snf(
        connection_snf_for_dwh_connection_type(Dvf_dwh_connection_type.owner),
        my_job_instance.get_job_instance_id(),
        my_job_instance.get_job_started_at()
    )

    # 部署暂存表
    deployment_manager.deploy_stage_table("rvlt_sap", "rsap_rin2_j1_stage")

    # 部署Hub表
    deployment_manager.deploy_table("rvlt_sap", "rsap_credit_memo_line_rin1_hub")

    # 部署Link表
    deployment_manager.deploy_table("rvlt_sap", "rsap_credit_memo_line_rin1_rin2_dlnk")

    # 部署Satellite表
    deployment_manager.deploy_table("rvlt_sap", "rsap_credit_memo_line_rin1_rin2_j1_l10_sat")

    # 部署视图
    deployment_manager.deploy_view("rvlt_sap", "rsap_credit_memo_line_rin1_rin2_p1_l20_sat")
```

### 数据提取和暂存（fetch_and_stage_file）
```python
@cimtjobinstance_job
def fetch_and_stage_file(parent_job_instance, dwh_connection, arzblob_connection,
                        file_name, bk_keys, exclude_from_hash_diff=None, **kwargs):

    # 1. 清空暂存表
    dwh_cursor.execute("TRUNCATE TABLE stage_rvlt.rsap_rin2_j1_stage")

    # 2. 准备插入语句
    insert_statement = get_snf_dict_insert_sql(dwh_connection, "stage_rvlt", "rsap_rin2_j1_stage")

    # 3. 设置元数据
    stage_data_row = {
        'md_inserted_at': my_job_instance.get_job_started_at().isoformat(),
        'md_record_source': 'sap.rin2',
        'md_run_id': my_job_instance.get_job_instance_id(),
        'md_is_deleted': False
    }

    # 4. 处理每条源数据记录
    for source_row in source_json:
        stage_data_row_temp = dict(stage_data_row)

        # 提取业务字段
        stage_data_row_temp['company'] = source_row.get('Company')
        stage_data_row_temp['docentry'] = source_row.get('DocEntry')
        stage_data_row_temp['groupnum'] = source_row.get('GroupNum')
        stage_data_row_temp['linenum'] = source_row.get('LineNum')

        # 处理JSON内容
        json_text = sort_json_content_column(
            get_json_text_content_column(source_row, keys_to_remove=bk_keys)
        )
        stage_data_row_temp['json_text'] = json.dumps(json_text)

        # 计算哈希值
        stage_data_row_temp['hk_rsap_credit_memo_line_rin1'] = dvf_assemble_datavault_hash([
            stage_data_row_temp['company'],
            stage_data_row_temp['docentry'],
            stage_data_row_temp['linenum']
        ])

        stage_data_row_temp['lk_rsap_credit_memo_line_rin1_rin2'] = dvf_assemble_datavault_hash([
            stage_data_row_temp['groupnum'],
            stage_data_row_temp['company'],
            stage_data_row_temp['docentry'],
            stage_data_row_temp['linenum']
        ])

        json_text_for_hash_diff = json_text if exclude_from_hash_diff is None else \
            get_json_text_content_column(json_text, keys_to_remove=exclude_from_hash_diff)
        stage_data_row_temp['rh_rsap_credit_memo_line_rin1_rin2_j1_l10_sat'] = \
            dvf_assemble_datavault_hash([json_text_for_hash_diff])

        stage_data_rows.append(stage_data_row_temp)
        my_job_instance.count_input(1)

    # 5. 批量插入暂存表
    execute_snf_dict_bulk_insert(dwh_cursor, insert_statement, stage_data_rows)
    dwh_connection.commit()
```

### 数据保险库加载（load_data_to_vault）
```python
@cimtjobinstance_job
def load_data_to_vault(parent_job_instance, dwh_connection, file_name=None, **kwargs):

    stage_schema = 'stage_rvlt'
    stage_table = 'rsap_rin2_j1_stage'
    dwh_cursor = dwh_connection.cursor()

    # 1. Hub表哈希冲突检查
    hash_collision_check_statement_list = dvf_get_check_hash_collision_hub_elt_sql(
        vault_table='rsap_credit_memo_line_rin1_hub',
        vault_schema='rvlt_sap',
        stage_hk_column='HK_RSAP_CREDIT_MEMO_LINE_RIN1',
        stage_bk_column_list=['COMPANY', 'DOCENTRY', 'LINENUM'],
        db_connection=dwh_connection,
        stage_schema=stage_schema,
        stage_table=stage_table
    )
    dvf_execute_elt_statement_list(dwh_cursor, hash_collision_check_statement_list)

    # 2. 加载Hub表
    statement_list = dvf_get_datavault_hub_elt_sql(
        vault_table='rsap_credit_memo_line_rin1_hub',
        vault_schema='rvlt_sap',
        stage_hk_column='HK_RSAP_CREDIT_MEMO_LINE_RIN1',
        stage_bk_column_list=['COMPANY', 'DOCENTRY', 'LINENUM'],
        db_connection=dwh_connection,
        stage_schema=stage_schema,
        stage_table=stage_table,
        meta_job_instance_id=my_job_instance.get_job_instance_id(),
        meta_inserted_at=my_job_instance.get_job_started_at()
    )
    dvf_execute_elt_statement_list(dwh_cursor, statement_list)

    # 3. Link表哈希冲突检查
    hash_collision_check_statement_list = dvf_get_check_hash_collision_dlnk_elt_sql(
        vault_table='rsap_credit_memo_line_rin1_rin2_dlnk',
        vault_schema='rvlt_sap',
        stage_lk_column='LK_RSAP_credit_memo_LINE_rin1_rin2',
        stage_hk_column_list=['HK_RSAP_CREDIT_MEMO_LINE_RIN1'],
        stage_dc_column_list=['GROUPNUM'],
        db_connection=dwh_connection,
        stage_schema=stage_schema,
        stage_table=stage_table
    )
    dvf_execute_elt_statement_list(dwh_cursor, hash_collision_check_statement_list)

    # 4. 加载Link表
    statement_list = dvf_get_datavault_dlnk_elt_sql(
        vault_table='rsap_credit_memo_line_rin1_rin2_dlnk',
        vault_schema='rvlt_sap',
        stage_lk_column='LK_RSAP_credit_memo_LINE_rin1_rin2',
        stage_hk_column_list=['HK_RSAP_CREDIT_MEMO_LINE_RIN1'],
        stage_dc_column_list=['GROUPNUM'],
        db_connection=dwh_connection,
        stage_schema=stage_schema,
        stage_table=stage_table,
        meta_job_instance_id=my_job_instance.get_job_instance_id(),
        meta_inserted_at=my_job_instance.get_job_started_at()
    )
    dvf_execute_elt_statement_list(dwh_cursor, statement_list)

    # 5. Satellite表单一性检查
    singularity_check_statement_list = dvf_get_check_singularity_sat_elt_sql(
        vault_table='rsap_credit_memo_line_rin1_rin2_j1_l10_sat',
        stage_hk_column='LK_RSAP_credit_memo_LINE_rin1_rin2',
        stage_rh_column='RH_RSAP_credit_memo_LINE_rin1_rin2_J1_L10_SAT',
        stage_schema=stage_schema,
        stage_table=stage_table
    )
    dvf_execute_elt_statement_list(dwh_cursor, singularity_check_statement_list)

    # 6. 加载Satellite表
    statement_list = dvf_get_datavault_sat_elt_sql(
        vault_table='rsap_credit_memo_line_rin1_rin2_j1_l10_sat',
        vault_schema='rvlt_sap',
        stage_hk_column='LK_RSAP_credit_memo_LINE_rin1_rin2',
        stage_rh_column='RH_RSAP_credit_memo_LINE_rin1_rin2_J1_L10_SAT',
        stage_content_column_list=['JSON_TEXT'],
        with_deletion_detection=False,
        db_connection=dwh_connection,
        stage_schema=stage_schema,
        stage_table=stage_table,
        meta_job_instance_id=my_job_instance.get_job_instance_id(),
        meta_inserted_at=my_job_instance.get_job_started_at()
    )
    dvf_execute_elt_statement_list(dwh_cursor, statement_list)

    dwh_connection.commit()
```

## 数据质量保证

### 哈希算法实现
DVF框架使用SHA-1算法生成28字符的Base64编码哈希值：

```python
def dvf_assemble_datavault_hash(attribute_list):
    """计算Data Vault哈希值，属性顺序至关重要"""
    if type(attribute_list) is list:
        stringified = ""
        separator = ""
        has_data = False

        for element in attribute_list:
            if element is not None:
                stringified += separator + str(element)
                has_data = True
            else:
                stringified += separator
            separator = "|"

        # 移除尾部分隔符
        while stringified[-1:] == "|":
            stringified = stringified[:-1]

        # 处理空值情况
        if not has_data:
            return HASHKEY_FOR_DELIVERED_NULL  # "0000000000000000000000000000"

        # 生成SHA-1哈希
        digest = hashlib.sha1(stringified.encode('utf-8')).digest()
        return base64.b64encode(digest).decode('ascii')
```

### 数据质量检查机制

#### 1. 哈希冲突检测
```python
def dvf_get_check_hash_collision_hub_elt_sql(vault_table, stage_hk_column,
                                           stage_bk_column_list, **kwargs):
    """检测Hub表中的哈希冲突"""
    statement = f"""
    WITH hash_collision_check AS (
        SELECT {stage_hk_column},
               COUNT(DISTINCT CONCAT({', '.join(stage_bk_column_list)})) as bk_variations
        FROM {stage_schema}.{stage_table} stage
        INNER JOIN {vault_schema}.{vault_table} vault
            ON stage.{stage_hk_column} = vault.{stage_hk_column}
        GROUP BY {stage_hk_column}
        HAVING COUNT(DISTINCT CONCAT({', '.join(stage_bk_column_list)})) > 1
    )
    SELECT to_number('HASH COLLISION DETECTED in {vault_table}')
    FROM hash_collision_check
    WHERE bk_variations > 1
    """
    return [statement]
```

#### 2. 卫星表单一性检查
```python
def dvf_get_check_singularity_sat_elt_sql(vault_table, stage_hk_column,
                                         stage_rh_column, **kwargs):
    """检查同一键值是否有多个不同的记录哈希"""
    statement = f"""
    WITH hk_value_variations AS (
        SELECT {stage_hk_column},
               COUNT(DISTINCT {stage_rh_column}) variation_count
        FROM {stage_schema}.{stage_table} stage
        GROUP BY 1
    )
    SELECT to_number('DATA VAULT LOAD EXCEPTION: Multiple values for same key in {vault_table}')
    FROM hk_value_variations
    WHERE variation_count > 1
    """
    return [statement]
```

#### 3. 变更检测机制
- **记录哈希**: 基于业务属性计算，用于检测数据变更
- **排除字段**: 可配置排除某些字段（如UpdateDate）不参与变更检测
- **历史保留**: 所有历史版本都被保留，支持时间旅行查询

### 数据一致性保证

#### 1. 事务管理
```python
try:
    # 执行所有ETL操作
    fetch_and_stage_file(...)
    load_data_to_vault(...)
    dwh_connection.commit()  # 统一提交
except Exception as e:
    dwh_connection.rollback()  # 发生错误时回滚
    raise e
```

#### 2. 幂等性设计
- 相同的源数据多次加载不会产生重复记录
- 基于哈希值的去重机制
- 支持重新运行失败的作业

#### 3. 审计跟踪
```python
# 每条记录都包含完整的审计信息
audit_fields = {
    'MD_INSERTED_AT': job_instance.get_job_started_at(),
    'MD_RUN_ID': job_instance.get_job_instance_id(),
    'MD_RECORD_SOURCE': 'sap.rin2',
    'MD_IS_DELETED': False
}
```

---

## 部署与运维

### 环境配置

#### 1. 配置文件结构
```
config_template/
├── dvf.ini              # DVF框架配置
├── dwh_azrblob.ini      # Azure Blob Storage配置
├── dwh_azrkeyvault.ini  # Azure Key Vault配置
├── dwh_azrsql.ini       # Azure SQL配置
└── dwh_snf.ini          # Snowflake配置
```

#### 2. 连接配置示例
```ini
[dwh_snf]
account = your_account.snowflakecomputing.com
user = your_username
password = your_password
warehouse = your_warehouse
database = your_database
schema = your_schema
role = your_role
```

#### 3. Azure集成配置
```ini
[dwh_azrblob]
account_name = your_storage_account
container_name = rawdata
connection_string = DefaultEndpointsProtocol=https;...

[dwh_azrkeyvault]
url = https://your-keyvault.vault.azure.net/
```

### 部署流程

#### 1. 环境准备
```bash
# 1. 创建Python虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate     # Windows

# 2. 安装依赖
pip install -r requirements.txt

# 3. 配置环境变量
export PYTHONPATH=$PYTHONPATH:/path/to/data-warehouse
export UMI_CLIENT_ID=your_managed_identity_client_id
```

#### 2. 数据库对象部署
```python
# 自动部署DDL脚本
deployment_manager = Dvf_ddl_deploymentmanager_snf(connection, run_id, insert_date)

# 部署顺序：Schema → Stage Tables → Hub Tables → Link Tables → Satellite Tables → Views
deployment_manager.deploy_stage_table("rvlt_sap", "rsap_rin2_j1_stage")
deployment_manager.deploy_table("rvlt_sap", "rsap_credit_memo_line_rin1_hub")
deployment_manager.deploy_table("rvlt_sap", "rsap_credit_memo_line_rin1_rin2_dlnk")
deployment_manager.deploy_table("rvlt_sap", "rsap_credit_memo_line_rin1_rin2_j1_l10_sat")
```

#### 3. 作业调度
```python
# 使用Azure Data Factory或类似工具调度
# 或者使用cron作业
# 0 2 * * * /path/to/python /path/to/processes/rsap_rin2_j1/__main__.py
```

### 监控与日志

#### 1. 作业实例跟踪
```python
class CimtJobInstance:
    def __init__(self, job_name, parent_instance=None):
        self.job_name = job_name
        self.start_time = None
        self.end_time = None
        self.status = 'INITIALIZED'
        self.input_count = 0
        self.output_count = 0
        self.error_message = None

    def start_instance(self):
        self.start_time = datetime.now()
        self.status = 'RUNNING'
        # 记录到数据库

    def end_instance(self):
        self.end_time = datetime.now()
        self.status = 'COMPLETED'
        # 更新数据库记录

    def end_instance_with_error(self, error_code, error_message):
        self.end_time = datetime.now()
        self.status = 'FAILED'
        self.error_message = error_message
        # 记录错误信息
```

#### 2. 性能监控
```sql
-- 监控作业执行情况
SELECT
    job_name,
    start_time,
    end_time,
    DATEDIFF(second, start_time, end_time) as duration_seconds,
    input_count,
    output_count,
    status
FROM job_instance_log
WHERE job_name = 'rsap_rin2_j1'
ORDER BY start_time DESC;

-- 监控数据增长
SELECT
    table_name,
    COUNT(*) as record_count,
    MAX(md_inserted_at) as last_update
FROM rvlt_sap.rsap_credit_memo_line_rin1_rin2_j1_l10_sat
GROUP BY table_name;
```

#### 3. 数据质量监控
```sql
-- 检查哈希冲突
SELECT
    hk_rsap_credit_memo_line_rin1,
    COUNT(DISTINCT CONCAT(company, '|', docentry, '|', linenum)) as bk_variations
FROM rvlt_sap.rsap_credit_memo_line_rin1_hub
GROUP BY hk_rsap_credit_memo_line_rin1
HAVING COUNT(DISTINCT CONCAT(company, '|', docentry, '|', linenum)) > 1;

-- 检查数据完整性
SELECT
    COUNT(*) as total_records,
    COUNT(DISTINCT hk_rsap_credit_memo_line_rin1) as unique_hubs,
    COUNT(DISTINCT lk_rsap_credit_memo_line_rin1_rin2) as unique_links
FROM stage_rvlt.rsap_rin2_j1_stage;
```

---

## 故障排除

### 常见问题及解决方案

#### 1. 哈希冲突错误
**错误信息**: "HASH COLLISION DETECTED"

**原因**: 不同的业务键组合产生了相同的哈希值

**解决方案**:
```python
# 1. 检查业务键定义是否正确
bk_keys = ['Company', 'DocEntry', 'LineNum']  # 确保包含所有必要字段

# 2. 检查数据质量
SELECT company, docentry, linenum, COUNT(*)
FROM source_data
GROUP BY company, docentry, linenum
HAVING COUNT(*) > 1;

# 3. 如果确实存在冲突，考虑添加额外的区分字段
```

#### 2. 连接超时错误
**错误信息**: "Connection timeout"

**原因**: 网络连接不稳定或数据库负载过高

**解决方案**:
```python
# 1. 增加连接超时设置
connection_params = {
    'connect_timeout': 300,
    'socket_timeout': 300,
    'autocommit': False
}

# 2. 实现重试机制
import time
from functools import wraps

def retry_on_failure(max_retries=3, delay=5):
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            for attempt in range(max_retries):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    if attempt == max_retries - 1:
                        raise e
                    time.sleep(delay * (2 ** attempt))  # 指数退避
            return None
        return wrapper
    return decorator
```

#### 3. 内存不足错误
**错误信息**: "MemoryError" 或 "Out of memory"

**原因**: 处理大量数据时内存不足

**解决方案**:
```python
# 1. 分批处理数据
def process_in_batches(data_list, batch_size=1000):
    for i in range(0, len(data_list), batch_size):
        batch = data_list[i:i + batch_size]
        yield batch

# 2. 使用生成器而不是列表
def process_source_data(source_json):
    for source_row in source_json:
        yield transform_row(source_row)

# 3. 及时释放内存
import gc
gc.collect()  # 强制垃圾回收
```

#### 4. JSON解析错误
**错误信息**: "JSONDecodeError"

**原因**: 源数据格式不正确

**解决方案**:
```python
import json
import logging

def safe_json_parse(json_string):
    try:
        return json.loads(json_string)
    except json.JSONDecodeError as e:
        logging.error(f"JSON解析错误: {e}")
        logging.error(f"问题数据: {json_string[:100]}...")
        # 返回默认值或跳过该记录
        return None

# 数据清理
def clean_json_data(raw_data):
    # 移除BOM标记
    if raw_data.startswith('\ufeff'):
        raw_data = raw_data[1:]

    # 替换常见的问题字符
    raw_data = raw_data.replace('\x00', '')  # 移除NULL字符
    raw_data = raw_data.replace('\\', '\\\\')  # 转义反斜杠

    return raw_data
```

#### 5. 权限错误
**错误信息**: "Access denied" 或 "Permission denied"

**原因**: 数据库或存储账户权限不足

**解决方案**:
```sql
-- 1. 检查数据库权限
SHOW GRANTS FOR 'username'@'hostname';

-- 2. 授予必要权限
GRANT SELECT, INSERT, UPDATE, DELETE ON database.* TO 'username'@'hostname';
GRANT CREATE, DROP, ALTER ON database.* TO 'username'@'hostname';

-- 3. 检查Azure权限
# 确保Managed Identity具有以下权限：
# - Storage Blob Data Contributor (存储账户)
# - Key Vault Secrets User (Key Vault)
# - SQL DB Contributor (SQL数据库)
```

### 调试技巧

#### 1. 启用详细日志
```python
import logging

# 配置日志级别
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('etl_debug.log'),
        logging.StreamHandler()
    ]
)

# 在关键位置添加日志
logger = logging.getLogger(__name__)
logger.debug(f"处理文件: {file_name}")
logger.debug(f"暂存数据行数: {len(stage_data_rows)}")
```

#### 2. 数据采样检查
```python
# 检查前几条记录
def debug_stage_data(stage_data_rows, sample_size=5):
    logger.debug(f"暂存数据样本 (前{sample_size}条):")
    for i, row in enumerate(stage_data_rows[:sample_size]):
        logger.debug(f"记录 {i+1}: {row}")

# 检查哈希值计算
def debug_hash_calculation(business_keys):
    hash_input = "|".join(str(key) for key in business_keys)
    hash_output = dvf_assemble_datavault_hash(business_keys)
    logger.debug(f"哈希输入: '{hash_input}' -> 哈希输出: '{hash_output}'")
```

#### 3. 性能分析
```python
import time
from functools import wraps

def timing_decorator(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        logger.info(f"{func.__name__} 执行时间: {end_time - start_time:.2f}秒")
        return result
    return wrapper

# 应用到关键函数
@timing_decorator
def fetch_and_stage_file(...):
    # 函数实现
    pass
```

---

## 最佳实践

### 1. 配置管理
- 使用环境变量或配置文件管理敏感信息
- 不同环境使用不同的配置文件
- 定期轮换密钥和连接字符串

### 2. 错误处理
- 实现全面的异常处理机制
- 记录详细的错误信息和上下文
- 设计合理的重试策略

### 3. 性能优化
- 使用批量操作减少数据库往返
- 合理设置批次大小
- 监控和优化SQL查询性能

### 4. 数据治理
- 建立数据质量监控机制
- 定期进行数据一致性检查
- 维护完整的数据血缘关系

### 5. 文档维护
- 保持配置文档的及时更新
- 记录重要的设计决策和变更
- 提供清晰的操作手册

---

## 总结

本手册以RIN2为实际案例，详细介绍了基于DVPD框架和Data Vault 2.0理论的ETL实现。通过声明式配置、自动化代码生成和严格的数据质量保证机制，该框架能够高效、可靠地处理企业级数据仓库的ETL需求。

关键优势：
- **标准化**: 严格遵循Data Vault 2.0建模标准
- **自动化**: 基于配置自动生成代码，减少人工错误
- **可追溯**: 完整的数据血缘和变更历史
- **可扩展**: 支持多种数据源和目标系统
- **高质量**: 内置多层数据质量检查机制

通过本手册的学习和实践，开发人员能够快速掌握DVPD ETL的开发和运维技能，为企业数据仓库建设提供强有力的技术支撑。

---

*文档版本: 1.0*
*最后更新: 2024年*
*作者: Data Warehouse Team*